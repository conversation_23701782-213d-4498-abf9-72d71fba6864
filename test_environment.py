#!/usr/bin/env python3
"""
测试 Cura & Uranium 开发环境是否正确设置
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """测试 Python 版本"""
    print(f"Python 版本: {sys.version}")
    major, minor = sys.version_info[:2]
    if major == 3 and minor >= 11:
        print("✓ Python 版本符合要求 (3.11+)")
        return True
    else:
        print("❌ Python 版本不符合要求，需要 3.11+")
        return False

def test_imports():
    """测试关键模块导入"""
    modules_to_test = [
        "PyQt6",
        "numpy", 
        "scipy",
        "cryptography",
        "colorlog",
        "pyclipper",
        "networkx"
    ]
    
    success = True
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✓ {module}: 导入成功")
        except ImportError as e:
            print(f"❌ {module}: 导入失败 - {e}")
            success = False
    
    return success

def test_uranium_import():
    """测试 Uranium 模块导入"""
    try:
        # 添加 Uranium 路径到 sys.path
        uranium_path = Path(__file__).parent / "Uranium"
        if uranium_path.exists():
            sys.path.insert(0, str(uranium_path))
        
        import UM
        print("✓ Uranium (UM): 导入成功")
        
        # 测试一些核心模块
        from UM.Application import Application
        from UM.Logger import Logger
        print("✓ Uranium 核心模块: 导入成功")
        return True
    except ImportError as e:
        print(f"❌ Uranium: 导入失败 - {e}")
        return False

def test_cura_import():
    """测试 Cura 模块导入"""
    try:
        # 添加 Cura 路径到 sys.path
        cura_path = Path(__file__).parent / "Cura"
        if cura_path.exists():
            sys.path.insert(0, str(cura_path))
        
        import cura
        print("✓ Cura: 导入成功")
        
        # 测试一些核心模块
        from cura.CuraApplication import CuraApplication
        print("✓ Cura 核心模块: 导入成功")
        return True
    except ImportError as e:
        print(f"❌ Cura: 导入失败 - {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    required_paths = [
        "Uranium/build_windows/generators/uranium_venv",
        "Cura/build_windows/generators/cura_venv",
        "Uranium/conanfile.py",
        "Cura/conanfile.py",
        "activate_uranium_windows.bat",
        "run_cura_windows.bat"
    ]
    
    success = True
    for path in required_paths:
        if Path(path).exists():
            print(f"✓ {path}: 存在")
        else:
            print(f"❌ {path}: 不存在")
            success = False
    
    return success

def main():
    """主测试函数"""
    print("🧪 测试 Cura & Uranium 开发环境")
    print("=" * 50)
    
    tests = [
        ("Python 版本", test_python_version),
        ("文件结构", test_file_structure),
        ("Python 模块导入", test_imports),
        ("Uranium 导入", test_uranium_import),
        ("Cura 导入", test_cura_import)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！环境设置成功！")
        print("\n📝 下一步:")
        print("1. 打开 PyCharm 并导入项目")
        print("2. 配置 Python 解释器为虚拟环境中的 Python")
        print("3. 开始 Cura & Uranium 并行开发")
    else:
        print("❌ 部分测试失败，请检查环境设置")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
