@echo off
echo Fixing PyQt6 DLL issues...
echo ==========================

echo Step 1: Activating virtual environment...
cd /d "C:\Mac\Home\Desktop\CuraProject\Uranium"
call build_windows\generators\virtual_python_env.bat

echo.
echo Step 2: Uninstalling existing PyQt6...
python -m pip uninstall -y PyQt6 PyQt6-Qt6 PyQt6-sip

echo.
echo Step 3: Reinstalling PyQt6 with all components...
python -m pip install --force-reinstall PyQt6==6.6.0 PyQt6-Qt6==6.6.0 PyQt6-sip==13.6.0

echo.
echo Step 4: Testing PyQt6 installation...
python -c "import PyQt6; print('PyQt6 base imported successfully')"
python -c "import PyQt6.QtCore; print('PyQt6.QtCore imported successfully')"
python -c "import PyQt6.QtWidgets; print('PyQt6.QtWidgets imported successfully')"
python -c "import PyQt6.QtNetwork; print('PyQt6.QtNetwork imported successfully')"

echo.
echo PyQt6 fix completed!
pause
