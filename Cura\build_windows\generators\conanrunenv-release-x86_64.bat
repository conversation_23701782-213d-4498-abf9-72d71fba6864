@echo off
chcp 65001 > nul
setlocal
echo @echo off > "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
echo echo Restoring environment >> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
for %%v in (PYTHONPATH PATH PYTHON PYTHON_ROOT) do (
    set foundenvvar=
    for /f "delims== tokens=1,2" %%a in ('set') do (
        if /I "%%a" == "%%v" (
            echo set "%%a=%%b">> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
            set foundenvvar=1
        )
    )
    if not defined foundenvvar (
        echo set %%v=>> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
    )
)
endlocal


set "PYTHONPATH=C:\Mac\Home\Desktop\CuraProject\Uranium\plugins;C:\Mac\Home\Desktop\CuraProject\Uranium\.;C:\Users\<USER>\.conan2\p\b\pyarceb17c3a0b4179\p\lib;%PYTHONPATH%"
set "PATH=C:\Users\<USER>\.conan2\p\b\arcus03cb8757a37eb\p\bin;C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p\bin;%PATH%;C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p\bin"
set "PYTHON=%PYTHON%;C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p\bin\python.exe"
set "PYTHON_ROOT=%PYTHON_ROOT%;C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p"