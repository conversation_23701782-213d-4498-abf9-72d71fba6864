# Copyright (c) 2019 Ultimaker B.V.
# Cura is released under the terms of the LGPLv3 or higher.
from datetime import datetime
from typing import Optional, Union

from ..BaseModel import BaseModel


# Model that represents the responses received from the cloud after requesting a job to be printed.
class CloudPrintResponse(BaseModel):

    def __init__(self, job_id: str, status: str, generated_time: Union[str, datetime],
                 cluster_job_id: Optional[str] = None, **kwargs) -> None:
        """Creates a new print response object.

        :param job_id: The unique ID of a print job inside of the cluster. This ID is generated by Cura Connect.
        :param status: The status of the print request (queued or failed).
        :param generated_time: The datetime when the object was generated on the server-side.
        :param cluster_job_id: The unique ID of a print job inside of the cluster. This ID is generated by Cura Connect.
        """

        self.job_id = job_id
        self.status = status
        self.cluster_job_id = cluster_job_id
        self.generated_time = self.parseDate(generated_time)
        super().__init__(**kwargs)
