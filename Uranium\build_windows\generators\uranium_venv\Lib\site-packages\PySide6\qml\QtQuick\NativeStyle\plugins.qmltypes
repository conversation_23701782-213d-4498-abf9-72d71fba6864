import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qquickstyleitem.h"
        name: "QQuickStyleItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.NativeStyle/StyleItem 6.0",
            "QtQuick.NativeStyle/StyleItem 6.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1539]
        Enum {
            name: "OverrideState"
            values: ["None", "AlwaysHovered", "NeverHovered", "AlwaysSunken"]
        }
        Property {
            name: "control"
            type: "QQuickItem"
            isPointer: true
            notify: "controlChanged"
            index: 0
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            write: "setContentWidth"
            index: 1
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            write: "setContentHeight"
            index: 2
        }
        Property { name: "useNinePatchImage"; type: "bool"; index: 3 }
        Property { name: "overrideState"; type: "OverrideState"; index: 4 }
        Property {
            name: "contentPadding"
            type: "QQuickStyleMargins"
            read: "contentPadding"
            notify: "contentPaddingChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "layoutMargins"
            type: "QQuickStyleMargins"
            read: "layoutMargins"
            notify: "layoutMarginsChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "minimumSize"
            type: "QSize"
            read: "minimumSize"
            notify: "minimumSizeChanged"
            index: 7
            isReadonly: true
        }
        Property { name: "transitionDuration"; type: "int"; index: 8; isConstant: true }
        Signal { name: "controlChanged" }
        Signal { name: "contentPaddingChanged" }
        Signal { name: "layoutMarginsChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "minimumSizeChanged" }
        Method {
            name: "styleFont"
            type: "QFont"
            Parameter { name: "control"; type: "QQuickItem"; isPointer: true }
        }
    }
    Component {
        file: "qquickstyleitembutton.h"
        name: "QQuickStyleItemButton"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/Button 6.0",
            "QtQuick.NativeStyle/Button 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemcheckbox.h"
        name: "QQuickStyleItemCheckBox"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/CheckBox 6.0",
            "QtQuick.NativeStyle/CheckBox 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemcombobox.h"
        name: "QQuickStyleItemComboBox"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/ComboBox 6.0",
            "QtQuick.NativeStyle/ComboBox 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemdial.h"
        name: "QQuickStyleItemDial"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/Dial 6.0",
            "QtQuick.NativeStyle/Dial 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemframe.h"
        name: "QQuickStyleItemFrame"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/Frame 6.0",
            "QtQuick.NativeStyle/Frame 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemgroupbox.h"
        name: "QQuickStyleItemGroupBox"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/GroupBox 6.0",
            "QtQuick.NativeStyle/GroupBox 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
        Property {
            name: "groupBoxPadding"
            type: "QQuickStyleMargins"
            read: "groupBoxPadding"
            notify: "groupBoxPaddingChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "labelPos"
            type: "QPointF"
            read: "labelPos"
            notify: "labelPosChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "groupBoxPaddingChanged" }
        Signal { name: "labelPosChanged" }
    }
    Component {
        file: "qquickstyleitemprogressbar.h"
        name: "QQuickStyleItemProgressBar"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/ProgressBar 6.0",
            "QtQuick.NativeStyle/ProgressBar 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemradiobutton.h"
        name: "QQuickStyleItemRadioButton"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/RadioButton 6.0",
            "QtQuick.NativeStyle/RadioButton 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemscrollbar.h"
        name: "QQuickStyleItemScrollBar"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/ScrollBar 6.0",
            "QtQuick.NativeStyle/ScrollBar 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
        Enum {
            name: "SubControl"
            values: ["Groove", "Handle", "AddLine", "SubLine"]
        }
        Property { name: "subControl"; type: "SubControl"; index: 0 }
    }
    Component {
        file: "qquickstyleitemslider.h"
        name: "QQuickStyleItemSlider"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/Slider 6.0",
            "QtQuick.NativeStyle/Slider 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
        Enum {
            name: "SubControl"
            values: ["Groove", "Handle"]
        }
        Property { name: "subControl"; type: "SubControl"; index: 0 }
    }
    Component {
        file: "qquickstyleitemspinbox.h"
        name: "QQuickStyleItemSpinBox"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/SpinBox 6.0",
            "QtQuick.NativeStyle/SpinBox 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
        Enum {
            name: "SubControl"
            values: ["Frame", "Up", "Down"]
        }
        Property { name: "subControl"; type: "SubControl"; index: 0 }
    }
    Component {
        file: "qquickstyleitemtextfield.h"
        name: "QQuickStyleItemTextField"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/TextField 6.0",
            "QtQuick.NativeStyle/TextField 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitemtreeindicator.h"
        name: "QQuickStyleItemTreeIndicator"
        accessSemantics: "reference"
        prototype: "QQuickStyleItem"
        exports: [
            "QtQuick.NativeStyle/TreeIndicator 6.0",
            "QtQuick.NativeStyle/TreeIndicator 6.3"
        ]
        exportMetaObjectRevisions: [1536, 1539]
    }
    Component {
        file: "qquickstyleitem.h"
        name: "QQuickStyleMargins"
        accessSemantics: "value"
        exports: ["QtQuick.NativeStyle/stylemargins 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property { name: "left"; type: "int"; read: "left"; index: 0; isReadonly: true }
        Property { name: "top"; type: "int"; read: "top"; index: 1; isReadonly: true }
        Property { name: "right"; type: "int"; read: "right"; index: 2; isReadonly: true }
        Property { name: "bottom"; type: "int"; read: "bottom"; index: 3; isReadonly: true }
    }
}
