[{"classes": [{"className": "AutofillPopupWidget", "object": true, "qualifiedClassName": "QtWebEngineWidgetUI::AutofillPopupWidget", "superClasses": [{"access": "public", "name": "QFrame"}]}], "inputFile": "autofillpopupwidget_p.h", "outputRevision": 68}, {"classes": [{"className": "DefaultNotificationPresenter", "object": true, "qualifiedClassName": "DefaultNotificationPresenter", "slots": [{"access": "private", "name": "messageClicked", "returnType": "void"}, {"access": "private", "name": "closeNotification", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwebenginenotificationpresenter_p.h", "outputRevision": 68}, {"classes": [{"className": "QWebEngineView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "title", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "url", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setUrl"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "iconUrl", "notify": "iconUrlChanged", "read": "iconUrl", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QIcon", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "selectedText", "read": "selectedText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "hasSelection", "read": "hasSelection", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "zoomFactor", "read": "zoomFactor", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setZoomFactor"}], "qualifiedClassName": "QWebEngineView", "signals": [{"access": "public", "name": "loadStarted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "progress", "type": "int"}], "name": "loadProgress", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "name": "loadFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}], "name": "titleChanged", "returnType": "void"}, {"access": "public", "name": "selectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QUrl"}], "name": "url<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QUrl"}], "name": "iconUrlChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QIcon"}], "name": "iconChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "terminationStatus", "type": "QWebEnginePage::RenderProcessTerminationStatus"}, {"name": "exitCode", "type": "int"}], "name": "renderProcessTerminated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}, {"name": "success", "type": "bool"}], "name": "pdfPrintingFinished", "returnType": "void"}, {"access": "public", "name": "printRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "success", "type": "bool"}], "name": "printFinished", "returnType": "void"}], "slots": [{"access": "public", "name": "stop", "returnType": "void"}, {"access": "public", "name": "back", "returnType": "void"}, {"access": "public", "name": "forward", "returnType": "void"}, {"access": "public", "name": "reload", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qwebengineview.h", "outputRevision": 68}]