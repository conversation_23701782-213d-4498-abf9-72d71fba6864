[{"classes": [{"className": "QSSGAssetImporter", "object": true, "qualifiedClassName": "QSSGAssetImporter", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qssgassetimporter_p.h", "outputRevision": 68}, {"classes": [{"className": "QSSGAssetImporterPlugin", "object": true, "qualifiedClassName": "QSSGAssetImporterPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qssgassetimporterplugin_p.h", "outputRevision": 68}, {"classes": [{"className": "QSSGAssetImportManager", "object": true, "qualifiedClassName": "QSSGAssetImportManager", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qssgassetimportmanager_p.h", "outputRevision": 68}]