@echo off
echo Starting Cura with virtual environment...
echo ==========================================

echo Step 1: Activating Uranium virtual environment...
cd /d "C:\Mac\Home\Desktop\CuraProject\Uranium"
call build_windows\generators\virtual_python_env.bat

echo.
echo Step 2: Checking Python and PyQt6...
python --version
python -c "import sys; print('Python path:', sys.executable)"
python -c "import PyQt6; print('PyQt6 imported successfully')"
python -c "import PyQt6.QtCore; print('PyQt6.QtCore version:', PyQt6.QtCore.PYQT_VERSION_STR)"

echo.
echo Step 3: Testing PyQt6.QtNetwork specifically...
python -c "import PyQt6.QtNetwork; print('PyQt6.QtNetwork imported successfully')"

echo.
echo Step 4: Changing to Cura directory...
cd /d "C:\Mac\Home\Desktop\CuraProject\Cura"

echo.
echo Step 5: Checking if cura_app.py exists...
if exist cura_app.py (
    echo cura_app.py found
) else (
    echo ERROR: cura_app.py not found!
    pause
    exit /b 1
)

echo.
echo Step 6: Running Cura...
python cura_app.py

echo.
echo Cura execution completed.
pause
