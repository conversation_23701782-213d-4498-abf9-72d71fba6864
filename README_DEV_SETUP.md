# Cura & Uranium 并行开发环境设置指南

本指南帮助您设置 Cura 和 Uranium 的并行开发环境，支持 macOS 和 Windows 跨平台开发。

## 🎯 特性

- ✅ 支持 macOS 和 Windows 跨平台开发
- ✅ 自动添加系统后缀避免平台冲突
- ✅ 排除 CuraEngine 构建（可单独开发）
- ✅ 严格按照官方文档依赖版本
- ✅ 自动生成 PyCharm 多仓库项目配置
- ✅ 一键式环境设置脚本
- ✅ Uranium 可编辑模式支持

## 📋 系统要求

### Windows
- Windows 10 或更高版本
- Visual Studio with MSVC 2022 或更高版本
- Python 3.12 或更高版本
- CMake 3.23 或更高版本
- Ninja 1.10 或更高版本
- Conan >=2.7.0 <3.0.0

### macOS
- macOS 11 或更高版本
- Xcode 12 或更高版本
- Python 3.12 或更高版本
- CMake 3.23 或更高版本
- Ninja 1.10 或更高版本
- Conan >=2.7.0 <3.0.0

## 🚀 快速开始

### 1. 克隆项目

```bash
# 创建项目目录
mkdir CuraProject
cd CuraProject

# 克隆所有仓库
git clone https://github.com/Ultimaker/Cura.git
git clone https://github.com/Ultimaker/Uranium.git
git clone https://github.com/Ultimaker/CuraEngine.git  # 可选，如果需要单独开发
```

### 2. 运行自动化设置脚本

```bash
# 运行设置脚本
python setup_cura_dev_env.py
```

脚本将自动执行以下步骤：
1. 检查系统依赖
2. 配置 Conan 环境
3. 构建 Uranium 开发环境（带系统后缀）
4. 设置 Uranium 为可编辑模式
5. 构建 Cura 开发环境
6. 创建 PyCharm 配置
7. 生成运行脚本
8. 验证环境设置

### 3. 使用开发环境

#### Windows
```cmd
# 激活 Uranium 开发环境
activate_uranium_windows.bat

# 运行 Cura
run_cura_windows.bat
```

#### macOS/Linux
```bash
# 激活 Uranium 开发环境
./activate_uranium_macos.sh

# 运行 Cura
./run_cura_macos.sh
```

## 🔧 PyCharm 设置

1. 打开 PyCharm
2. 选择 "Open" 并选择项目根目录
3. PyCharm 会自动识别多模块项目结构
4. 配置 Python 解释器：
   - 路径：`Uranium/build_[系统后缀]/generators/cura_venv/bin/python`
   - 对 Cura 和 Uranium 使用相同的解释器

## 📁 项目结构

```
CuraProject/
├── Cura/                          # Cura 主项目
│   ├── build_windows/             # Windows 构建目录
│   ├── build_macos/               # macOS 构建目录
│   ├── conanfile.py               # 新增的 Cura conanfile
│   └── ...
├── Uranium/                       # Uranium 框架
│   ├── build_windows/             # Windows 构建目录
│   ├── build_macos/               # macOS 构建目录
│   ├── venv_windows/              # Windows 虚拟环境
│   ├── venv_macos/                # macOS 虚拟环境
│   └── ...
├── CuraEngine/                    # CuraEngine (可选)
├── setup_cura_dev_env.py          # 自动化设置脚本
├── activate_uranium_*.sh/bat      # 环境激活脚本
├── run_cura_*.sh/bat              # Cura 运行脚本
└── README_DEV_SETUP.md            # 本文档
```

## 🧪 测试

```bash
# 测试 Uranium
cd Uranium
python -m pytest tests/

# 测试 Cura
cd Cura
python -m pytest tests/
```

## 🔄 重新构建环境

如果需要重新构建环境：

```bash
# 清理构建目录
rm -rf Uranium/build_*
rm -rf Uranium/venv_*
rm -rf Cura/build_*

# 重新运行设置脚本
python setup_cura_dev_env.py
```

## ⚠️ 注意事项

1. **依赖版本**：严格按照官方文档的依赖版本，不允许修改
2. **Python 版本**：必须使用 Python 3.12+
3. **Conan 版本**：必须使用 Conan >=2.7.0 <3.0.0
4. **CuraEngine**：已从构建过程中排除，可单独开发
5. **系统后缀**：自动添加以避免跨平台冲突
6. **虚拟环境**：使用 Conan 构建的虚拟环境，不要使用系统 Python

## 🐛 故障排除

### Conan 问题
```bash
# 检查 Conan 版本
conan --version

# 强制重新安装正确版本
pip install --force-reinstall -v "conan==2.7.0"

# 清理 Conan 缓存
conan remove "*" -f

# 重新配置 Conan
conan config install https://github.com/ultimaker/conan-config.git
conan profile detect --force
```

### 权限问题
- 不要使用 `sudo` 运行 conan install
- 如果使用了 sudo，请删除缓存重新开始

### 依赖问题
- 确保所有系统依赖都已正确安装
- 检查 PATH 环境变量是否包含必要的工具

## 📚 参考文档

- [Cura Getting Started](https://github.com/Ultimaker/Cura/wiki/Getting-Started)
- [Running Cura from Source](https://github.com/Ultimaker/Cura/wiki/Running-Cura-from-Source)
- [Uranium Building And Developing](https://github.com/Ultimaker/Uranium/wiki/Building-And-Developing)

## 🤝 贡献

如果您发现问题或有改进建议，请创建 Issue 或 Pull Request。

## 📄 许可证

本设置脚本遵循与 Cura 和 Uranium 相同的 LGPL-3.0 许可证。
