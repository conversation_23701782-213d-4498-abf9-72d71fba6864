@echo off
echo 🧪 在虚拟环境中测试 Cura & Uranium 开发环境
echo ==================================================

echo 激活 Uranium 虚拟环境...
cd /d "C:\Mac\Home\Desktop\CuraProject\Uranium"
call build_windows\generators\virtual_python_env.bat

echo.
echo 🔍 测试 Python 版本和模块...
python --version

echo.
echo 🔍 测试关键模块导入...
python -c "import PyQt6; print('✓ PyQt6: 导入成功')"
python -c "import numpy; print('✓ numpy: 导入成功')"
python -c "import scipy; print('✓ scipy: 导入成功')"
python -c "import cryptography; print('✓ cryptography: 导入成功')"
python -c "import colorlog; print('✓ colorlog: 导入成功')"

echo.
echo 🔍 测试 Uranium 模块...
python -c "import UM; print('✓ UM: 导入成功')"
python -c "from UM.Application import Application; print('✓ UM.Application: 导入成功')"

echo.
echo 🔍 测试 Cura 模块...
cd /d "C:\Mac\Home\Desktop\CuraProject\Cura"
python -c "import cura; print('✓ cura: 导入成功')"

echo.
echo 🎉 虚拟环境测试完成！
echo 如果上面没有错误信息，说明环境设置成功！
pause
