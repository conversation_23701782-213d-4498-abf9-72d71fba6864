[{"classes": [{"className": "QCanBus", "object": true, "qualifiedClassName": "QCanBus", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcanbus.h", "outputRevision": 68}, {"classes": [{"className": "QCanBusDevice", "enums": [{"isClass": false, "isFlag": false, "name": "CanBusError", "values": ["NoError", "ReadError", "WriteError", "ConnectionError", "ConfigurationError", "UnknownE<PERSON>r", "OperationError", "TimeoutError"]}, {"isClass": false, "isFlag": false, "name": "CanBusDeviceState", "values": ["UnconnectedState", "ConnectingState", "ConnectedState", "ClosingState"]}, {"isClass": true, "isFlag": false, "name": "CanBusStatus", "values": ["Unknown", "Good", "Warning", "Error", "BusOff"]}, {"isClass": false, "isFlag": false, "name": "Configuration<PERSON>ey", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Loopback<PERSON>ey", "ReceiveOwnKey", "BitRateKey", "CanFdKey", "DataBitRateKey", "<PERSON><PERSON>ey", "User<PERSON>ey"]}], "object": true, "qualifiedClassName": "QCanBusDevice", "signals": [{"access": "public", "arguments": [{"type": "QCanBusDevice::CanBusError"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "name": "framesReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "framesCount", "type": "qint64"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QCanBusDevice::CanBusDeviceState"}], "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcanbusdevice.h", "outputRevision": 68}, {"classes": [{"className": "QModbusClient", "object": true, "qualifiedClassName": "QModbusClient", "signals": [{"access": "public", "arguments": [{"name": "newTimeout", "type": "int"}], "name": "timeoutChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QModbusDevice"}]}], "inputFile": "qmodbusclient.h", "outputRevision": 68}, {"classes": [{"className": "QModbusDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ReadError", "WriteError", "ConnectionError", "ConfigurationError", "TimeoutError", "ProtocolError", "ReplyAbortedError", "UnknownE<PERSON>r", "InvalidResponseError"]}, {"isClass": false, "isFlag": false, "name": "State", "values": ["UnconnectedState", "ConnectingState", "ConnectedState", "ClosingState"]}, {"isClass": false, "isFlag": false, "name": "ConnectionParameter", "values": ["SerialPortNameParameter", "SerialParityParameter", "SerialBaudRateParameter", "SerialDataBitsParameter", "SerialStopBitsParameter", "NetworkPortParameter", "NetworkAddressParameter"]}, {"isClass": false, "isFlag": false, "name": "IntermediateError", "values": ["ResponseCrcError", "ResponseRequestMismatch"]}], "object": true, "qualifiedClassName": "QModbusDevice", "signals": [{"access": "public", "arguments": [{"name": "error", "type": "QModbusDevice::<PERSON><PERSON><PERSON>"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QModbusDevice::State"}], "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmodbusdevice.h", "outputRevision": 68}, {"classes": [{"className": "Timer", "object": true, "qualifiedClassName": "Timer", "signals": [{"access": "public", "arguments": [{"name": "timerId", "type": "int"}], "name": "timeout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmodbusrtuserialclient_p.h", "outputRevision": 68}, {"classes": [{"className": "QModbusRtuSerialServer", "object": true, "qualifiedClassName": "QModbusRtuSerialServer", "superClasses": [{"access": "public", "name": "QModbusServer"}]}], "inputFile": "qmodbusrtuserialserver.h", "outputRevision": 68}, {"classes": [{"className": "QModbusServer", "enums": [{"isClass": false, "isFlag": false, "name": "Option", "values": ["DiagnosticRegister", "ExceptionStatusOffset", "DeviceBusy", "AsciiInputDelimiter", "ListenOnlyMode", "ServerIdentifier", "RunIndicatorStatus", "AdditionalData", "DeviceIdentification", "UserOption"]}], "object": true, "qualifiedClassName": "QModbusServer", "signals": [{"access": "public", "arguments": [{"name": "table", "type": "QModbusDataUnit::RegisterType"}, {"name": "address", "type": "int"}, {"name": "size", "type": "int"}], "name": "dataWritten", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QModbusDevice"}]}], "inputFile": "qmodbusserver.h", "outputRevision": 68}, {"classes": [{"className": "QModbusTcpClient", "object": true, "qualifiedClassName": "QModbusTcpClient", "superClasses": [{"access": "public", "name": "QModbusClient"}]}], "inputFile": "qmodbustcpclient.h", "outputRevision": 68}, {"classes": [{"className": "QModbusTcpServer", "object": true, "qualifiedClassName": "QModbusTcpServer", "signals": [{"access": "public", "arguments": [{"name": "modbusClient", "type": "QTcpSocket*"}], "name": "modbusClientDisconnected", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QModbusServer"}]}], "inputFile": "qmodbustcpserver.h", "outputRevision": 68}, {"classes": [{"className": "QModbusReply", "enums": [{"isClass": false, "isFlag": false, "name": "ReplyType", "values": ["Raw", "Common", "Broadcast"]}], "object": true, "qualifiedClassName": "QModbusReply", "signals": [{"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QModbusDevice::<PERSON><PERSON><PERSON>"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QModbusDevice::IntermediateError"}], "name": "intermediateErrorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmodbusreply.h", "outputRevision": 68}, {"classes": [{"className": "QModbusRtuSerialClient", "object": true, "qualifiedClassName": "QModbusRtuSerialClient", "superClasses": [{"access": "public", "name": "QModbusClient"}]}], "inputFile": "qmodbusrtuserialclient.h", "outputRevision": 68}]