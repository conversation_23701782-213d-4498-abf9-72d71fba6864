<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtQuick3D">
    <load-typesystem name="typesystem_quick.xml" generate="no"/>

    <object-type name="QQuick3D"/>
    <object-type name="QQuick3DObject">
        <enum-type name="ItemChange"/>
        <modify-function signature="QQuick3DObject(QQuick3DObject*)" remove="all"/>
    </object-type>
    <object-type name="QQuick3DGeometry">
        <value-type name="Attribute">
            <enum-type name="Semantic"/>
            <enum-type name="ComponentType"/>
        </value-type>
        <value-type name="TargetAttribute" since="6.6"/>
        <enum-type name="PrimitiveType"/>
    </object-type>
    <object-type name="QQuick3DInstancing">
        <value-type name="InstanceTableEntry"/>
    </object-type>
    <object-type name="QQuick3DTextureData">
        <enum-type name="Format"/>
    </object-type>
</typesystem>
