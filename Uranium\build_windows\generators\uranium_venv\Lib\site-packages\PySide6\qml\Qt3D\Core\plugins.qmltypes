import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QAbstractSkeleton"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Core/AbstractSkeleton 2.10",
            "Qt3D.Core/AbstractSkeleton 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [522, 1536]
        Property {
            name: "jointCount"
            type: "int"
            read: "jointCount"
            notify: "jointCountChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "jointCountChanged"
            Parameter { name: "jointCount"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QArmature"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Core/Armature 2.0", "Qt3D.Core/Armature 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "skeleton"
            type: "Qt3DCore::QAbstractSkeleton"
            isPointer: true
            read: "skeleton"
            write: "setSkeleton"
            notify: "skeletonChanged"
            index: 0
        }
        Signal {
            name: "skeletonChanged"
            Parameter { name: "skeleton"; type: "Qt3DCore::QAbstractSkeleton"; isPointer: true }
        }
        Method {
            name: "setSkeleton"
            Parameter { name: "skeleton"; type: "Qt3DCore::QAbstractSkeleton"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QAttribute"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Core/Attribute 2.0",
            "Qt3D.Core/Attribute 2.10",
            "Qt3D.Core/Attribute 2.11",
            "Qt3D.Core/Attribute 6.0"
        ]
        exportMetaObjectRevisions: [512, 522, 523, 1536]
        Enum {
            name: "AttributeType"
            values: [
                "VertexAttribute",
                "IndexAttribute",
                "DrawIndirectAttribute"
            ]
        }
        Enum {
            name: "VertexBaseType"
            values: [
                "Byte",
                "UnsignedByte",
                "Short",
                "UnsignedShort",
                "Int",
                "UnsignedInt",
                "HalfFloat",
                "Float",
                "Double"
            ]
        }
        Property {
            name: "buffer"
            type: "Qt3DCore::QBuffer"
            isPointer: true
            read: "buffer"
            write: "setBuffer"
            notify: "bufferChanged"
            index: 0
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 1
        }
        Property {
            name: "vertexBaseType"
            type: "VertexBaseType"
            read: "vertexBaseType"
            write: "setVertexBaseType"
            notify: "vertexBaseTypeChanged"
            index: 2
        }
        Property {
            name: "vertexSize"
            type: "uint"
            read: "vertexSize"
            write: "setVertexSize"
            notify: "vertexSizeChanged"
            index: 3
        }
        Property {
            name: "count"
            type: "uint"
            read: "count"
            write: "setCount"
            notify: "countChanged"
            index: 4
        }
        Property {
            name: "byteStride"
            type: "uint"
            read: "byteStride"
            write: "setByteStride"
            notify: "byteStrideChanged"
            index: 5
        }
        Property {
            name: "byteOffset"
            type: "uint"
            read: "byteOffset"
            write: "setByteOffset"
            notify: "byteOffsetChanged"
            index: 6
        }
        Property {
            name: "divisor"
            type: "uint"
            read: "divisor"
            write: "setDivisor"
            notify: "divisorChanged"
            index: 7
        }
        Property {
            name: "attributeType"
            type: "AttributeType"
            read: "attributeType"
            write: "setAttributeType"
            notify: "attributeTypeChanged"
            index: 8
        }
        Property {
            name: "defaultPositionAttributeName"
            type: "QString"
            read: "defaultPositionAttributeName"
            index: 9
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultNormalAttributeName"
            type: "QString"
            read: "defaultNormalAttributeName"
            index: 10
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultColorAttributeName"
            type: "QString"
            read: "defaultColorAttributeName"
            index: 11
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultTextureCoordinateAttributeName"
            type: "QString"
            read: "defaultTextureCoordinateAttributeName"
            index: 12
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultTextureCoordinate1AttributeName"
            revision: 523
            type: "QString"
            read: "defaultTextureCoordinate1AttributeName"
            index: 13
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultTextureCoordinate2AttributeName"
            revision: 523
            type: "QString"
            read: "defaultTextureCoordinate2AttributeName"
            index: 14
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultTangentAttributeName"
            type: "QString"
            read: "defaultTangentAttributeName"
            index: 15
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultJointIndicesAttributeName"
            revision: 522
            type: "QString"
            read: "defaultJointIndicesAttributeName"
            index: 16
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultJointWeightsAttributeName"
            revision: 522
            type: "QString"
            read: "defaultJointWeightsAttributeName"
            index: 17
            isReadonly: true
            isConstant: true
        }
        Signal {
            name: "bufferChanged"
            Parameter { name: "buffer"; type: "QBuffer"; isPointer: true }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "vertexBaseTypeChanged"
            Parameter { name: "vertexBaseType"; type: "VertexBaseType" }
        }
        Signal {
            name: "vertexSizeChanged"
            Parameter { name: "vertexSize"; type: "uint" }
        }
        Signal {
            name: "dataTypeChanged"
            Parameter { name: "vertexBaseType"; type: "VertexBaseType" }
        }
        Signal {
            name: "dataSizeChanged"
            Parameter { name: "vertexSize"; type: "uint" }
        }
        Signal {
            name: "countChanged"
            Parameter { name: "count"; type: "uint" }
        }
        Signal {
            name: "byteStrideChanged"
            Parameter { name: "byteStride"; type: "uint" }
        }
        Signal {
            name: "byteOffsetChanged"
            Parameter { name: "byteOffset"; type: "uint" }
        }
        Signal {
            name: "divisorChanged"
            Parameter { name: "divisor"; type: "uint" }
        }
        Signal {
            name: "attributeTypeChanged"
            Parameter { name: "attributeType"; type: "AttributeType" }
        }
        Method {
            name: "setBuffer"
            Parameter { name: "buffer"; type: "QBuffer"; isPointer: true }
        }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setVertexBaseType"
            Parameter { name: "type"; type: "VertexBaseType" }
        }
        Method {
            name: "setVertexSize"
            Parameter { name: "size"; type: "uint" }
        }
        Method {
            name: "setCount"
            Parameter { name: "count"; type: "uint" }
        }
        Method {
            name: "setByteStride"
            Parameter { name: "byteStride"; type: "uint" }
        }
        Method {
            name: "setByteOffset"
            Parameter { name: "byteOffset"; type: "uint" }
        }
        Method {
            name: "setDivisor"
            Parameter { name: "divisor"; type: "uint" }
        }
        Method {
            name: "setAttributeType"
            Parameter { name: "attributeType"; type: "AttributeType" }
        }
        Method { name: "defaultPositionAttributeName"; type: "QString" }
        Method { name: "defaultNormalAttributeName"; type: "QString" }
        Method { name: "defaultColorAttributeName"; type: "QString" }
        Method { name: "defaultTextureCoordinateAttributeName"; type: "QString" }
        Method { name: "defaultTangentAttributeName"; type: "QString" }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QBoundingVolume"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Core/BoundingVolume 2.16",
            "Qt3D.Core/BoundingVolume 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
        Property {
            name: "view"
            type: "QGeometryView"
            isPointer: true
            read: "view"
            write: "setView"
            notify: "viewChanged"
            index: 0
        }
        Property {
            name: "implicitMinPoint"
            type: "QVector3D"
            read: "implicitMinPoint"
            notify: "implicitMinPointChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "implicitMaxPoint"
            type: "QVector3D"
            read: "implicitMaxPoint"
            notify: "implicitMaxPointChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "implicitPointsValid"
            type: "bool"
            read: "areImplicitPointsValid"
            notify: "implicitPointsValidChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "minPoint"
            type: "QVector3D"
            read: "minPoint"
            write: "setMinPoint"
            notify: "minPointChanged"
            index: 4
        }
        Property {
            name: "maxPoint"
            type: "QVector3D"
            read: "maxPoint"
            write: "setMaxPoint"
            notify: "maxPointChanged"
            index: 5
        }
        Signal {
            name: "viewChanged"
            Parameter { name: "view"; type: "QGeometryView"; isPointer: true }
        }
        Signal {
            name: "implicitMinPointChanged"
            Parameter { name: "implicitMinPoint"; type: "QVector3D" }
        }
        Signal {
            name: "implicitMaxPointChanged"
            Parameter { name: "implicitMaxPoint"; type: "QVector3D" }
        }
        Signal {
            name: "implicitPointsValidChanged"
            Parameter { name: "implicitPointsValid"; type: "bool" }
        }
        Signal {
            name: "minPointChanged"
            Parameter { name: "minPoint"; type: "QVector3D" }
        }
        Signal {
            name: "maxPointChanged"
            Parameter { name: "maxPoint"; type: "QVector3D" }
        }
        Method {
            name: "setView"
            Parameter { name: "view"; type: "QGeometryView"; isPointer: true }
        }
        Method {
            name: "setMinPoint"
            Parameter { name: "minPoint"; type: "QVector3D" }
        }
        Method {
            name: "setMaxPoint"
            Parameter { name: "maxPoint"; type: "QVector3D" }
        }
        Method { name: "updateImplicitBounds"; type: "bool" }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QBuffer"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Core/BufferBase 2.0",
            "Qt3D.Core/BufferBase 2.9",
            "Qt3D.Core/BufferBase 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 521, 1536]
        Enum {
            name: "UsageType"
            values: [
                "StreamDraw",
                "StreamRead",
                "StreamCopy",
                "StaticDraw",
                "StaticRead",
                "StaticCopy",
                "DynamicDraw",
                "DynamicRead",
                "DynamicCopy"
            ]
        }
        Enum {
            name: "AccessType"
            values: ["Write", "Read", "ReadWrite"]
        }
        Property {
            name: "usage"
            type: "UsageType"
            read: "usage"
            write: "setUsage"
            notify: "usageChanged"
            index: 0
        }
        Property {
            name: "accessType"
            revision: 521
            type: "AccessType"
            read: "accessType"
            write: "setAccessType"
            notify: "accessTypeChanged"
            index: 1
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "bytes"; type: "QByteArray" }
        }
        Signal {
            name: "usageChanged"
            Parameter { name: "usage"; type: "UsageType" }
        }
        Signal {
            name: "accessTypeChanged"
            Parameter { name: "access"; type: "AccessType" }
        }
        Signal { name: "dataAvailable" }
        Method {
            name: "setUsage"
            Parameter { name: "usage"; type: "UsageType" }
        }
        Method {
            name: "setAccessType"
            Parameter { name: "access"; type: "AccessType" }
        }
        Method {
            name: "updateData"
            Parameter { name: "offset"; type: "int" }
            Parameter { name: "bytes"; type: "QByteArray" }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QComponent"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Core/Component3D 2.0", "Qt3D.Core/Component3D 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "isShareable"
            type: "bool"
            read: "isShareable"
            write: "setShareable"
            notify: "shareableChanged"
            index: 0
        }
        Signal {
            name: "shareableChanged"
            Parameter { name: "isShareable"; type: "bool" }
        }
        Signal {
            name: "addedToEntity"
            Parameter { name: "entity"; type: "QEntity"; isPointer: true }
        }
        Signal {
            name: "removedFromEntity"
            Parameter { name: "entity"; type: "QEntity"; isPointer: true }
        }
        Method {
            name: "setShareable"
            Parameter { name: "isShareable"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QEntity"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DCore::Quick::Quick3DEntity"
        exports: ["Qt3D.Core/Entity 2.0", "Qt3D.Core/Entity 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Method {
            name: "onParentChanged"
            Parameter { type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DCore::Quick::Quick3DGeometry"
        exports: [
            "Qt3D.Core/Geometry 2.0",
            "Qt3D.Core/Geometry 2.13",
            "Qt3D.Core/Geometry 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "boundingVolumePositionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "boundingVolumePositionAttribute"
            write: "setBoundingVolumePositionAttribute"
            notify: "boundingVolumePositionAttributeChanged"
            index: 0
        }
        Property {
            name: "minExtent"
            revision: 525
            type: "QVector3D"
            read: "minExtent"
            notify: "minExtentChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "maxExtent"
            revision: 525
            type: "QVector3D"
            read: "maxExtent"
            notify: "maxExtentChanged"
            index: 2
            isReadonly: true
        }
        Signal {
            name: "boundingVolumePositionAttributeChanged"
            Parameter { name: "boundingVolumePositionAttribute"; type: "QAttribute"; isPointer: true }
        }
        Signal {
            name: "minExtentChanged"
            revision: 525
            Parameter { name: "minExtent"; type: "QVector3D" }
        }
        Signal {
            name: "maxExtentChanged"
            revision: 525
            Parameter { name: "maxExtent"; type: "QVector3D" }
        }
        Method {
            name: "setBoundingVolumePositionAttribute"
            Parameter { name: "boundingVolumePositionAttribute"; type: "QAttribute"; isPointer: true }
        }
        Method {
            name: "addAttribute"
            Parameter { name: "attribute"; type: "Qt3DCore::QAttribute"; isPointer: true }
        }
        Method {
            name: "removeAttribute"
            Parameter { name: "attribute"; type: "Qt3DCore::QAttribute"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QGeometryView"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Core/GeometryView 2.16", "Qt3D.Core/GeometryView 6.0"]
        exportMetaObjectRevisions: [528, 1536]
        Enum {
            name: "PrimitiveType"
            values: [
                "Points",
                "Lines",
                "LineLoop",
                "LineStrip",
                "Triangles",
                "TriangleStrip",
                "TriangleFan",
                "LinesAdjacency",
                "TrianglesAdjacency",
                "LineStripAdjacency",
                "TriangleStripAdjacency",
                "Patches"
            ]
        }
        Property {
            name: "instanceCount"
            type: "int"
            read: "instanceCount"
            write: "setInstanceCount"
            notify: "instanceCountChanged"
            index: 0
        }
        Property {
            name: "vertexCount"
            type: "int"
            read: "vertexCount"
            write: "setVertexCount"
            notify: "vertexCountChanged"
            index: 1
        }
        Property {
            name: "indexOffset"
            type: "int"
            read: "indexOffset"
            write: "setIndexOffset"
            notify: "indexOffsetChanged"
            index: 2
        }
        Property {
            name: "firstInstance"
            type: "int"
            read: "firstInstance"
            write: "setFirstInstance"
            notify: "firstInstanceChanged"
            index: 3
        }
        Property {
            name: "firstVertex"
            type: "int"
            read: "firstVertex"
            write: "setFirstVertex"
            notify: "firstVertexChanged"
            index: 4
        }
        Property {
            name: "indexBufferByteOffset"
            type: "int"
            read: "indexBufferByteOffset"
            write: "setIndexBufferByteOffset"
            notify: "indexBufferByteOffsetChanged"
            index: 5
        }
        Property {
            name: "restartIndexValue"
            type: "int"
            read: "restartIndexValue"
            write: "setRestartIndexValue"
            notify: "restartIndexValueChanged"
            index: 6
        }
        Property {
            name: "verticesPerPatch"
            type: "int"
            read: "verticesPerPatch"
            write: "setVerticesPerPatch"
            notify: "verticesPerPatchChanged"
            index: 7
        }
        Property {
            name: "primitiveRestartEnabled"
            type: "bool"
            read: "primitiveRestartEnabled"
            write: "setPrimitiveRestartEnabled"
            notify: "primitiveRestartEnabledChanged"
            index: 8
        }
        Property {
            name: "geometry"
            type: "Qt3DCore::QGeometry"
            isPointer: true
            read: "geometry"
            write: "setGeometry"
            notify: "geometryChanged"
            index: 9
        }
        Property {
            name: "primitiveType"
            type: "PrimitiveType"
            read: "primitiveType"
            write: "setPrimitiveType"
            notify: "primitiveTypeChanged"
            index: 10
        }
        Signal {
            name: "instanceCountChanged"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Signal {
            name: "vertexCountChanged"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Signal {
            name: "indexOffsetChanged"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Signal {
            name: "firstInstanceChanged"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Signal {
            name: "firstVertexChanged"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Signal {
            name: "indexBufferByteOffsetChanged"
            Parameter { name: "offset"; type: "int" }
        }
        Signal {
            name: "restartIndexValueChanged"
            Parameter { name: "restartIndexValue"; type: "int" }
        }
        Signal {
            name: "verticesPerPatchChanged"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Signal {
            name: "primitiveRestartEnabledChanged"
            Parameter { name: "primitiveRestartEnabled"; type: "bool" }
        }
        Signal {
            name: "geometryChanged"
            Parameter { name: "geometry"; type: "QGeometry"; isPointer: true }
        }
        Signal {
            name: "primitiveTypeChanged"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
        Method {
            name: "setInstanceCount"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Method {
            name: "setVertexCount"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Method {
            name: "setIndexOffset"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Method {
            name: "setFirstInstance"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Method {
            name: "setFirstVertex"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Method {
            name: "setIndexBufferByteOffset"
            Parameter { name: "offset"; type: "int" }
        }
        Method {
            name: "setRestartIndexValue"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setVerticesPerPatch"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Method {
            name: "setPrimitiveRestartEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "geometry"; type: "QGeometry"; isPointer: true }
        }
        Method {
            name: "setPrimitiveType"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QJoint"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DCore::Quick::Quick3DJoint"
        exports: ["Qt3D.Core/Joint 2.0", "Qt3D.Core/Joint 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "scale"
            type: "QVector3D"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 0
        }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 1
        }
        Property {
            name: "translation"
            type: "QVector3D"
            read: "translation"
            write: "setTranslation"
            notify: "translationChanged"
            index: 2
        }
        Property {
            name: "inverseBindMatrix"
            type: "QMatrix4x4"
            read: "inverseBindMatrix"
            write: "setInverseBindMatrix"
            notify: "inverseBindMatrixChanged"
            index: 3
        }
        Property {
            name: "rotationX"
            type: "float"
            read: "rotationX"
            write: "setRotationX"
            notify: "rotationXChanged"
            index: 4
        }
        Property {
            name: "rotationY"
            type: "float"
            read: "rotationY"
            write: "setRotationY"
            notify: "rotationYChanged"
            index: 5
        }
        Property {
            name: "rotationZ"
            type: "float"
            read: "rotationZ"
            write: "setRotationZ"
            notify: "rotationZChanged"
            index: 6
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 7
        }
        Signal {
            name: "scaleChanged"
            Parameter { name: "scale"; type: "QVector3D" }
        }
        Signal {
            name: "rotationChanged"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Signal {
            name: "translationChanged"
            Parameter { name: "translation"; type: "QVector3D" }
        }
        Signal {
            name: "inverseBindMatrixChanged"
            Parameter { name: "inverseBindMatrix"; type: "QMatrix4x4" }
        }
        Signal {
            name: "rotationXChanged"
            Parameter { name: "rotationX"; type: "float" }
        }
        Signal {
            name: "rotationYChanged"
            Parameter { name: "rotationY"; type: "float" }
        }
        Signal {
            name: "rotationZChanged"
            Parameter { name: "rotationZ"; type: "float" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "QVector3D" }
        }
        Method {
            name: "setRotation"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Method {
            name: "setTranslation"
            Parameter { name: "translation"; type: "QVector3D" }
        }
        Method {
            name: "setInverseBindMatrix"
            Parameter { name: "inverseBindMatrix"; type: "QMatrix4x4" }
        }
        Method {
            name: "setRotationX"
            Parameter { name: "rotationX"; type: "float" }
        }
        Method {
            name: "setRotationY"
            Parameter { name: "rotationY"; type: "float" }
        }
        Method {
            name: "setRotationZ"
            Parameter { name: "rotationZ"; type: "float" }
        }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "QString" }
        }
        Method { name: "setToIdentity" }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QNode"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "Qt3DCore::Quick::Quick3DNode"
        exports: ["Qt3D.Core/Node 2.0", "Qt3D.Core/Node 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "parent"
            type: "Qt3DCore::QNode"
            isPointer: true
            read: "parentNode"
            write: "setParent"
            notify: "parentChanged"
            index: 0
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 1
        }
        Signal {
            name: "parentChanged"
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal { name: "nodeDestroyed" }
        Method {
            name: "setParent"
            Parameter { name: "parent"; type: "QNode"; isPointer: true }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "isEnabled"; type: "bool" }
        }
        Method { name: "_q_postConstructorInit" }
        Method {
            name: "_q_addChild"
            Parameter { type: "Qt3DCore::QNode"; isPointer: true }
        }
        Method {
            name: "_q_removeChild"
            Parameter { type: "Qt3DCore::QNode"; isPointer: true }
        }
        Method {
            name: "_q_setParentHelper"
            Parameter { type: "Qt3DCore::QNode"; isPointer: true }
        }
    }
    Component {
        file: "private/qquaternionanimation_p.h"
        name: "Qt3DCore::Quick::QQuaternionAnimation"
        accessSemantics: "reference"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "Qt3D.Core/QuaternionAnimation 2.0",
            "Qt3D.Core/QuaternionAnimation 2.12",
            "Qt3D.Core/QuaternionAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Enum {
            name: "Type"
            values: ["Slerp", "Nlerp"]
        }
        Property { name: "from"; type: "QQuaternion"; read: "from"; write: "setFrom"; index: 0 }
        Property { name: "to"; type: "QQuaternion"; read: "to"; write: "setTo"; index: 1 }
        Property {
            name: "type"
            type: "Type"
            read: "type"
            write: "setType"
            notify: "typeChanged"
            index: 2
        }
        Property {
            name: "fromXRotation"
            type: "float"
            read: "fromXRotation"
            write: "setFromXRotation"
            notify: "fromXRotationChanged"
            index: 3
        }
        Property {
            name: "fromYRotation"
            type: "float"
            read: "fromYRotation"
            write: "setFromYRotation"
            notify: "fromYRotationChanged"
            index: 4
        }
        Property {
            name: "fromZRotation"
            type: "float"
            read: "fromZRotation"
            write: "setFromZRotation"
            notify: "fromZRotationChanged"
            index: 5
        }
        Property {
            name: "toXRotation"
            type: "float"
            read: "toXRotation"
            write: "setToXRotation"
            notify: "toXRotationChanged"
            index: 6
        }
        Property {
            name: "toYRotation"
            type: "float"
            read: "toYRotation"
            write: "setToYRotation"
            notify: "toYRotationChanged"
            index: 7
        }
        Property {
            name: "toZRotation"
            type: "float"
            read: "toZRotation"
            write: "setToZRotation"
            notify: "toZRotationChanged"
            index: 8
        }
        Signal {
            name: "typeChanged"
            Parameter { name: "type"; type: "Type" }
        }
        Signal {
            name: "fromXRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "fromYRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "fromZRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toXRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toYRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toZRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "QColor"
        accessSemantics: "value"
        extension: "Qt3DCore::Quick::QQuick3DColorValueType"
        exports: ["Qt3D.Core/color 2.0", "Qt3D.Core/color 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "Qt3DCore::Quick::QQuick3DColorValueType"
        accessSemantics: "value"
        Property { name: "r"; type: "double"; read: "r"; write: "setR"; index: 0; isFinal: true }
        Property { name: "g"; type: "double"; read: "g"; write: "setG"; index: 1; isFinal: true }
        Property { name: "b"; type: "double"; read: "b"; write: "setB"; index: 2; isFinal: true }
        Property { name: "a"; type: "double"; read: "a"; write: "setA"; index: 3; isFinal: true }
        Property {
            name: "hsvHue"
            type: "double"
            read: "hsvHue"
            write: "setHsvHue"
            index: 4
            isFinal: true
        }
        Property {
            name: "hsvSaturation"
            type: "double"
            read: "hsvSaturation"
            write: "setHsvSaturation"
            index: 5
            isFinal: true
        }
        Property {
            name: "hsvValue"
            type: "double"
            read: "hsvValue"
            write: "setHsvValue"
            index: 6
            isFinal: true
        }
        Property {
            name: "hslHue"
            type: "double"
            read: "hslHue"
            write: "setHslHue"
            index: 7
            isFinal: true
        }
        Property {
            name: "hslSaturation"
            type: "double"
            read: "hslSaturation"
            write: "setHslSaturation"
            index: 8
            isFinal: true
        }
        Property {
            name: "hslLightness"
            type: "double"
            read: "hslLightness"
            write: "setHslLightness"
            index: 9
            isFinal: true
        }
        Property { name: "valid"; type: "bool"; read: "isValid"; index: 10; isReadonly: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "alpha"
            type: "QVariant"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "lighter"
            type: "QVariant"
            Parameter { name: "factor"; type: "double" }
        }
        Method { name: "lighter"; type: "QVariant"; isCloned: true }
        Method {
            name: "darker"
            type: "QVariant"
            Parameter { name: "factor"; type: "double" }
        }
        Method { name: "darker"; type: "QVariant"; isCloned: true }
        Method {
            name: "tint"
            type: "QVariant"
            Parameter { name: "factor"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "QMatrix4x4"
        accessSemantics: "value"
        extension: "Qt3DCore::Quick::QQuick3DMatrix4x4ValueType"
        exports: ["Qt3D.Core/matrix4x4 2.0", "Qt3D.Core/matrix4x4 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "Qt3DCore::Quick::QQuick3DMatrix4x4ValueType"
        accessSemantics: "value"
        Property { name: "m11"; type: "double"; read: "m11"; write: "setM11"; index: 0; isFinal: true }
        Property { name: "m12"; type: "double"; read: "m12"; write: "setM12"; index: 1; isFinal: true }
        Property { name: "m13"; type: "double"; read: "m13"; write: "setM13"; index: 2; isFinal: true }
        Property { name: "m14"; type: "double"; read: "m14"; write: "setM14"; index: 3; isFinal: true }
        Property { name: "m21"; type: "double"; read: "m21"; write: "setM21"; index: 4; isFinal: true }
        Property { name: "m22"; type: "double"; read: "m22"; write: "setM22"; index: 5; isFinal: true }
        Property { name: "m23"; type: "double"; read: "m23"; write: "setM23"; index: 6; isFinal: true }
        Property { name: "m24"; type: "double"; read: "m24"; write: "setM24"; index: 7; isFinal: true }
        Property { name: "m31"; type: "double"; read: "m31"; write: "setM31"; index: 8; isFinal: true }
        Property { name: "m32"; type: "double"; read: "m32"; write: "setM32"; index: 9; isFinal: true }
        Property { name: "m33"; type: "double"; read: "m33"; write: "setM33"; index: 10; isFinal: true }
        Property { name: "m34"; type: "double"; read: "m34"; write: "setM34"; index: 11; isFinal: true }
        Property { name: "m41"; type: "double"; read: "m41"; write: "setM41"; index: 12; isFinal: true }
        Property { name: "m42"; type: "double"; read: "m42"; write: "setM42"; index: 13; isFinal: true }
        Property { name: "m43"; type: "double"; read: "m43"; write: "setM43"; index: 14; isFinal: true }
        Property { name: "m44"; type: "double"; read: "m44"; write: "setM44"; index: 15; isFinal: true }
        Method {
            name: "translate"
            Parameter { name: "t"; type: "QVector3D" }
        }
        Method {
            name: "rotate"
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "scale"
            Parameter { name: "s"; type: "float" }
        }
        Method {
            name: "scale"
            Parameter { name: "sx"; type: "float" }
            Parameter { name: "sy"; type: "float" }
            Parameter { name: "sz"; type: "float" }
        }
        Method {
            name: "scale"
            Parameter { name: "s"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            Parameter { name: "eye"; type: "QVector3D" }
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "up"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QMatrix4x4"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QMatrix4x4"
            Parameter { name: "factor"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QMatrix4x4"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "minus"
            type: "QMatrix4x4"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "row"
            type: "QVector4D"
            Parameter { name: "n"; type: "int" }
        }
        Method {
            name: "column"
            type: "QVector4D"
            Parameter { name: "m"; type: "int" }
        }
        Method { name: "determinant"; type: "double" }
        Method { name: "inverted"; type: "QMatrix4x4" }
        Method { name: "transposed"; type: "QMatrix4x4" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "m"; type: "QMatrix4x4" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "QQuaternion"
        accessSemantics: "value"
        extension: "Qt3DCore::Quick::QQuick3DQuaternionValueType"
        exports: ["Qt3D.Core/quaternion 2.0", "Qt3D.Core/quaternion 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "Qt3DCore::Quick::QQuick3DQuaternionValueType"
        accessSemantics: "value"
        Property { name: "scalar"; type: "double"; read: "scalar"; write: "setScalar"; index: 0 }
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 1 }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 2 }
        Property { name: "z"; type: "double"; read: "z"; write: "setZ"; index: 3 }
        Method { name: "toString"; type: "QString" }
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "QVector2D"
        accessSemantics: "value"
        extension: "Qt3DCore::Quick::QQuick3DVector2DValueType"
        exports: ["Qt3D.Core/vector2d 2.0", "Qt3D.Core/vector2d 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "Qt3DCore::Quick::QQuick3DVector2DValueType"
        accessSemantics: "value"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "dotProduct"
            type: "double"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method {
            name: "times"
            type: "QVector2D"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method {
            name: "times"
            type: "QVector2D"
            Parameter { name: "scalar"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QVector2D"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method {
            name: "minus"
            type: "QVector2D"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method { name: "normalized"; type: "QVector2D" }
        Method { name: "length"; type: "double" }
        Method { name: "toVector3d"; type: "QVector3D" }
        Method { name: "toVector4d"; type: "QVector4D" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector2D" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector2D" }
        }
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "QVector3D"
        accessSemantics: "value"
        extension: "Qt3DCore::Quick::QQuick3DVector3DValueType"
        exports: ["Qt3D.Core/vector3d 2.0", "Qt3D.Core/vector3d 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "Qt3DCore::Quick::QQuick3DVector3DValueType"
        accessSemantics: "value"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Property { name: "z"; type: "double"; read: "z"; write: "setZ"; index: 2; isFinal: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "crossProduct"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "dotProduct"
            type: "double"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "scalar"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "minus"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method { name: "normalized"; type: "QVector3D" }
        Method { name: "length"; type: "double" }
        Method { name: "toVector2d"; type: "QVector2D" }
        Method { name: "toVector4d"; type: "QVector4D" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector3D" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "QVector4D"
        accessSemantics: "value"
        extension: "Qt3DCore::Quick::QQuick3DVector4DValueType"
        exports: ["Qt3D.Core/vector4d 2.0", "Qt3D.Core/vector4d 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquickvaluetypes_p.h"
        name: "Qt3DCore::Quick::QQuick3DVector4DValueType"
        accessSemantics: "value"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Property { name: "z"; type: "double"; read: "z"; write: "setZ"; index: 2; isFinal: true }
        Property { name: "w"; type: "double"; read: "w"; write: "setW"; index: 3; isFinal: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "dotProduct"
            type: "double"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "scalar"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "minus"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method { name: "normalized"; type: "QVector4D" }
        Method { name: "length"; type: "double" }
        Method { name: "toVector2d"; type: "QVector2D" }
        Method { name: "toVector3d"; type: "QVector3D" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector4D" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector4D" }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QSkeletonLoader"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QAbstractSkeleton"
        exports: [
            "Qt3D.Core/SkeletonLoader 2.10",
            "Qt3D.Core/SkeletonLoader 6.0"
        ]
        exportMetaObjectRevisions: [522, 1536]
        Enum {
            name: "Status"
            values: ["NotReady", "Ready", "Error"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "createJointsEnabled"
            type: "bool"
            read: "isCreateJointsEnabled"
            write: "setCreateJointsEnabled"
            notify: "createJointsEnabledChanged"
            index: 2
        }
        Property {
            name: "rootJoint"
            type: "Qt3DCore::QJoint"
            isPointer: true
            read: "rootJoint"
            notify: "rootJointChanged"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Signal {
            name: "createJointsEnabledChanged"
            Parameter { name: "createJointsEnabled"; type: "bool" }
        }
        Signal {
            name: "rootJointChanged"
            Parameter { name: "rootJoint"; type: "Qt3DCore::QJoint"; isPointer: true }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setCreateJointsEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquickforeign_p.h"
        name: "Qt3DCore::QTransform"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Core/Transform 2.0",
            "Qt3D.Core/Transform 2.14",
            "Qt3D.Core/Transform 6.0"
        ]
        exportMetaObjectRevisions: [512, 526, 1536]
        Property {
            name: "matrix"
            type: "QMatrix4x4"
            read: "matrix"
            write: "setMatrix"
            notify: "matrixChanged"
            index: 0
        }
        Property {
            name: "scale"
            type: "float"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 1
        }
        Property {
            name: "scale3D"
            type: "QVector3D"
            read: "scale3D"
            write: "setScale3D"
            notify: "scale3DChanged"
            index: 2
        }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 3
        }
        Property {
            name: "translation"
            type: "QVector3D"
            read: "translation"
            write: "setTranslation"
            notify: "translationChanged"
            index: 4
        }
        Property {
            name: "rotationX"
            type: "float"
            read: "rotationX"
            write: "setRotationX"
            notify: "rotationXChanged"
            index: 5
        }
        Property {
            name: "rotationY"
            type: "float"
            read: "rotationY"
            write: "setRotationY"
            notify: "rotationYChanged"
            index: 6
        }
        Property {
            name: "rotationZ"
            type: "float"
            read: "rotationZ"
            write: "setRotationZ"
            notify: "rotationZChanged"
            index: 7
        }
        Property {
            name: "worldMatrix"
            revision: 526
            type: "QMatrix4x4"
            read: "worldMatrix"
            notify: "worldMatrixChanged"
            index: 8
            isReadonly: true
        }
        Signal {
            name: "scaleChanged"
            Parameter { name: "scale"; type: "float" }
        }
        Signal {
            name: "scale3DChanged"
            Parameter { name: "scale"; type: "QVector3D" }
        }
        Signal {
            name: "rotationChanged"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Signal {
            name: "translationChanged"
            Parameter { name: "translation"; type: "QVector3D" }
        }
        Signal { name: "matrixChanged" }
        Signal {
            name: "rotationXChanged"
            Parameter { name: "rotationX"; type: "float" }
        }
        Signal {
            name: "rotationYChanged"
            Parameter { name: "rotationY"; type: "float" }
        }
        Signal {
            name: "rotationZChanged"
            Parameter { name: "rotationZ"; type: "float" }
        }
        Signal {
            name: "worldMatrixChanged"
            Parameter { name: "worldMatrix"; type: "QMatrix4x4" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setScale3D"
            Parameter { name: "scale"; type: "QVector3D" }
        }
        Method {
            name: "setRotation"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Method {
            name: "setTranslation"
            Parameter { name: "translation"; type: "QVector3D" }
        }
        Method {
            name: "setMatrix"
            Parameter { name: "matrix"; type: "QMatrix4x4" }
        }
        Method {
            name: "setRotationX"
            Parameter { name: "rotationX"; type: "float" }
        }
        Method {
            name: "setRotationY"
            Parameter { name: "rotationY"; type: "float" }
        }
        Method {
            name: "setRotationZ"
            Parameter { name: "rotationZ"; type: "float" }
        }
        Method {
            name: "fromAxisAndAngle"
            type: "QQuaternion"
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "fromAxisAndAngle"
            type: "QQuaternion"
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
            Parameter { name: "z"; type: "float" }
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "fromAxesAndAngles"
            type: "QQuaternion"
            Parameter { name: "axis1"; type: "QVector3D" }
            Parameter { name: "angle1"; type: "float" }
            Parameter { name: "axis2"; type: "QVector3D" }
            Parameter { name: "angle2"; type: "float" }
        }
        Method {
            name: "fromAxesAndAngles"
            type: "QQuaternion"
            Parameter { name: "axis1"; type: "QVector3D" }
            Parameter { name: "angle1"; type: "float" }
            Parameter { name: "axis2"; type: "QVector3D" }
            Parameter { name: "angle2"; type: "float" }
            Parameter { name: "axis3"; type: "QVector3D" }
            Parameter { name: "angle3"; type: "float" }
        }
        Method {
            name: "fromAxes"
            type: "QQuaternion"
            Parameter { name: "xAxis"; type: "QVector3D" }
            Parameter { name: "yAxis"; type: "QVector3D" }
            Parameter { name: "zAxis"; type: "QVector3D" }
        }
        Method {
            name: "fromEulerAngles"
            type: "QQuaternion"
            Parameter { name: "eulerAngles"; type: "QVector3D" }
        }
        Method {
            name: "fromEulerAngles"
            type: "QQuaternion"
            Parameter { name: "pitch"; type: "float" }
            Parameter { name: "yaw"; type: "float" }
            Parameter { name: "roll"; type: "float" }
        }
        Method {
            name: "rotateAround"
            type: "QMatrix4x4"
            Parameter { name: "point"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "rotateFromAxes"
            type: "QMatrix4x4"
            Parameter { name: "xAxis"; type: "QVector3D" }
            Parameter { name: "yAxis"; type: "QVector3D" }
            Parameter { name: "zAxis"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/quick3dbuffer_p.h"
        name: "Qt3DCore::Quick::Quick3DBuffer"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QBuffer"
        exports: [
            "Qt3D.Core/Buffer 2.0",
            "Qt3D.Core/Buffer 2.9",
            "Qt3D.Core/Buffer 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 1536]
        Property {
            name: "data"
            type: "QVariant"
            read: "bufferData"
            write: "setBufferData"
            notify: "bufferDataChanged"
            index: 0
        }
        Signal { name: "bufferDataChanged" }
        Method {
            name: "updateData"
            Parameter { name: "offset"; type: "int" }
            Parameter { name: "bytes"; type: "QVariant" }
        }
        Method {
            name: "readBinaryFile"
            type: "QVariant"
            Parameter { name: "fileUrl"; type: "QUrl" }
        }
    }
    Component {
        file: "private/quick3dentity_p.h"
        name: "Qt3DCore::Quick::Quick3DEntity"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "components"
            type: "Qt3DCore::QComponent"
            isList: true
            read: "componentList"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dentityloader_p.h"
        name: "Qt3DCore::Quick::Quick3DEntityLoader"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QEntity"
        exports: [
            "Qt3D.Core/EntityLoader 2.0",
            "Qt3D.Core/EntityLoader 2.12",
            "Qt3D.Core/EntityLoader 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Enum {
            name: "Status"
            values: ["Null", "Loading", "Ready", "Error"]
        }
        Property {
            name: "entity"
            type: "QObject"
            isPointer: true
            read: "entity"
            notify: "entityChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 1
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "sourceComponent"
            revision: 524
            type: "QQmlComponent"
            isPointer: true
            read: "sourceComponent"
            write: "setSourceComponent"
            notify: "sourceComponentChanged"
            index: 3
        }
        Signal { name: "entityChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "sourceComponentChanged" }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "_q_componentStatusChanged"
            Parameter { type: "QQmlComponent::Status" }
        }
    }
    Component {
        file: "private/quick3dgeometry_p.h"
        name: "Qt3DCore::Quick::Quick3DGeometry"
        accessSemantics: "reference"
        defaultProperty: "attributes"
        prototype: "QObject"
        Property {
            name: "attributes"
            type: "Qt3DCore::QAttribute"
            isList: true
            read: "attributeList"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3djoint_p.h"
        name: "Qt3DCore::Quick::Quick3DJoint"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "childJoints"
            type: "Qt3DCore::QJoint"
            isList: true
            read: "childJoints"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dnode_p.h"
        name: "Qt3DCore::Quick::Quick3DNode"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QObject"
        Property { name: "data"; type: "QObject"; isList: true; read: "data"; index: 0; isReadonly: true }
        Property {
            name: "childNodes"
            type: "Qt3DCore::QNode"
            isList: true
            read: "childNodes"
            index: 1
            isReadonly: true
        }
        Method {
            name: "childAppended"
            Parameter { name: "idx"; type: "int" }
            Parameter { name: "child"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "childRemoved"
            Parameter { name: "idx"; type: "int" }
            Parameter { name: "child"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/quick3dnodeinstantiator_p.h"
        name: "Qt3DCore::Quick::Quick3DNodeInstantiator"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "Qt3DCore::QNode"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "Qt3D.Core/NodeInstantiator 2.0",
            "Qt3D.Core/NodeInstantiator 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "isAsync"
            write: "setAsync"
            notify: "asynchronousChanged"
            index: 1
        }
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 2
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 4
        }
        Property {
            name: "object"
            type: "QObject"
            isPointer: true
            read: "object"
            notify: "objectChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "countChanged" }
        Signal { name: "objectChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "asynchronousChanged" }
        Signal {
            name: "objectAdded"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "objectRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onParentChanged"
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "_q_createdItem"
            Parameter { type: "int" }
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "_q_modelUpdated"
            Parameter { type: "QQmlChangeSet" }
            Parameter { type: "bool" }
        }
        Method {
            name: "objectAt"
            type: "QObject"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
    }
}
