import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "hangulinputmethod_p.h"
        name: "QtVirtualKeyboard::HangulInputMethod"
        accessSemantics: "reference"
        prototype: "QVirtualKeyboardAbstractInputMethod"
        exports: [
            "QtQuick.VirtualKeyboard.Plugins.Hangul/HangulInputMethod 6.0",
            "QtQuick.VirtualKeyboard.Plugins.Hangul/HangulInputMethod 6.1"
        ]
        exportMetaObjectRevisions: [1536, 1537]
    }
}
