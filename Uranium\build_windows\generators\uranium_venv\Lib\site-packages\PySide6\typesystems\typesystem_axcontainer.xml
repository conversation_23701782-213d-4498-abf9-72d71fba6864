<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.QtAxContainer">
    <load-typesystem name="typesystem_widgets.xml" generate="no"/>
    <rejection class="*" function-name="connectNotify"/>
    <rejection class="*" function-name="queryInterface"/>
    <rejection class="*" function-name="qt_metacall"/>
    <rejection class="*" function-name="qt_static_metacall"/>

    <object-type name="QAxBase">
        <!--  PYSIDE-1410, Check for QVariantList first since it also is a QVariant -->
        <modify-function signature="dynamicCall(const char*,QList&lt;QVariant&gt;&amp;)" overload-number="0"/>
        <modify-function signature="dynamicCall(const char*,const QVariant&amp;,const QVariant&amp;,const QVariant&amp;,const QVariant&amp;,const QVariant&amp;,const QVariant&amp;,const QVariant&amp;,const QVariant&amp;)" overload-number="1"/>
        <!-- Remove protected, internal function -->
        <modify-function signature="dynamicCall(const char*,QList&lt;QVariant&gt;&amp;,unsigned)" remove="all"/>
    </object-type>
    <object-type name="QAxBaseObject"/>
    <object-type name="QAxObject"/>
    <object-type name="QAxObjectInterface"/>
    <object-type name="QAxScript">
        <enum-type name="FunctionFlags"/>
    </object-type>
    <object-type name="QAxScriptEngine">
        <enum-type name="State"/>
    </object-type>
    <object-type name="QAxScriptManager">
        <!-- Ax Servers only -->
        <modify-function signature="addObject(QObject*)" remove="all"/>
    </object-type>
    <object-type name="QAxBaseWidget"/>
    <object-type name="QAxSelect">
        <enum-type name="SandboxingLevel"/>
    </object-type>
    <object-type name="QAxWidget"/>
</typesystem>
