<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.Qt3DRender">
    <load-typesystem name="typesystem_3dcore.xml" generate="no"/>
    <smart-pointer-type name="QSharedPointer" type="shared" getter="data"
                        reset-method="reset"
                        instantiations="Qt3DRender::PropertyReaderInterface=Qt3DRender::PropertyReaderInterfacePtr,Qt3DRender::QTextureImageData=Qt3DRender::QTextureImageDataPtr,Qt3DRender::QTextureImageDataGenerator=Qt3DRender::QTextureImageDataGeneratorPtr"/>
    <namespace-type name="Qt3DRender">
        <enum-type name="API"/>
        <object-type name="PropertyReaderInterface"/>
        <object-type name="QAbstractLight">
            <enum-type name="Type"/>
        </object-type>
        <object-type name="QAbstractRayCaster">
            <enum-type name="RunMode"/>
            <enum-type name="FilterMode"/>
        </object-type>
        <object-type name="QAbstractTexture">
            <enum-type name="CubeMapFace"/>
            <enum-type name="ComparisonFunction"/>
            <enum-type name="ComparisonMode"/>
            <enum-type name="HandleType"/>
            <enum-type name="Filter"/>
            <enum-type name="Status"/>
            <enum-type name="Target"/>
            <enum-type name="TextureFormat"/>
        </object-type>
        <object-type name="QAbstractTextureImage">
            <modify-function signature="QAbstractTextureImage(Qt3DCore::QNode*)" remove="all"/>
        </object-type>
        <object-type name="QAlphaCoverage"/>
        <object-type name="QAlphaTest">
            <enum-type name="AlphaFunction"/>
        </object-type>
        <object-type name="QBlendEquation">
            <enum-type name="BlendFunction"/>
        </object-type>
        <object-type name="QBlendEquationArguments">
            <enum-type name="Blending"/>
        </object-type>
        <object-type name="QBlitFramebuffer">
            <enum-type name="InterpolationMethod"/>
        </object-type>
        <object-type name="QBufferCapture"/>
        <object-type name="QCamera">
            <enum-type name="CameraTranslationOption"/>
        </object-type>
        <object-type name="QCameraLens">
            <enum-type name="ProjectionType"/>
        </object-type>
        <object-type name="QCameraSelector"/>
        <object-type name="QClearBuffers">
            <enum-type name="BufferType" flags="BufferTypeFlags"/>
        </object-type>
        <object-type name="QClipPlane"/>
        <object-type name="QColorMask"/>
        <object-type name="QComputeCommand">
            <enum-type name="RunType"/>
        </object-type>
        <object-type name="QCullFace">
            <enum-type name="CullingMode"/>
        </object-type>
        <object-type name="QDepthRange"/>
        <object-type name="QDepthTest">
            <enum-type name="DepthFunction"/>
        </object-type>
        <object-type name="QDirectionalLight"/>
        <object-type name="QDispatchCompute"/>
        <object-type name="QDithering"/>
        <object-type name="QEffect"/>
        <object-type name="QEnvironmentLight"/>
        <object-type name="QFilterKey"/>
        <object-type name="QFrameGraphNode"/>
        <object-type name="QFrontFace">
            <enum-type name="WindingDirection"/>
        </object-type>
        <object-type name="QFrustumCulling"/>
        <object-type name="QGeometryRenderer">
            <enum-type name="PrimitiveType"/>
        </object-type>
        <object-type name="QGraphicsApiFilter">
            <enum-type name="Api"/>
            <enum-type name="OpenGLProfile"/>
            <!-- only Q_AUTOTEST_EXPORT -->
            <modify-function signature="operator==(Qt3DRender::QGraphicsApiFilter,Qt3DRender::QGraphicsApiFilter)"
                             remove="all"/>
            <modify-function signature="operator!=(Qt3DRender::QGraphicsApiFilter,Qt3DRender::QGraphicsApiFilter)"
                             remove="all"/>
        </object-type>
        <object-type name="QLayer"/>
        <object-type name="QLayerFilter">
            <enum-type name="FilterMode"/>
        </object-type>
        <object-type name="QLevelOfDetail">
            <enum-type name="ThresholdType"/>
        </object-type>
        <object-type name="QLevelOfDetailBoundingSphere"/>
        <object-type name="QLevelOfDetailSwitch"/>
        <object-type name="QLineWidth"/>
        <object-type name="QMaterial"/>
        <object-type name="QMemoryBarrier">
            <enum-type name="Operation" flags="Operations"/>
        </object-type>
        <object-type name="QMesh">
            <enum-type name="Status"/>
        </object-type>
        <object-type name="QMultiSampleAntiAliasing"/>
        <object-type name="QNoDepthMask"/>
        <object-type name="QNoDraw"/>
        <object-type name="QNoPicking"/>
        <object-type name="QObjectPicker"/>
        <object-type name="QPaintedTextureImage"/>
        <object-type name="QParameter"/>
        <object-type name="QPickEvent">
            <enum-type name="Buttons"/>
            <enum-type name="Modifiers"/>
        </object-type>
        <object-type name="QPickingProxy"/>
        <object-type name="QPickLineEvent"/>
        <object-type name="QPickPointEvent"/>
        <object-type name="QPickTriangleEvent"/>
        <object-type name="QPickingSettings">
            <enum-type name="FaceOrientationPickingMode"/>
            <enum-type name="PickMethod"/>
            <enum-type name="PickResultMode"/>
        </object-type>
        <object-type name="QPointLight"/>
        <object-type name="QPointSize">
            <enum-type name="SizeMode"/>
        </object-type>
        <object-type name="QPolygonOffset"/>
        <object-type name="QProximityFilter"/>
        <object-type name="QRasterMode">
            <enum-type name="RasterMode"/>
            <enum-type name="FaceMode"/>
        </object-type>
        <object-type name="QRayCaster"/>
        <value-type name="QRayCasterHit">
            <enum-type name="HitType"/>
        </value-type>
        <object-type name="QRenderAspect">
            <enum-type name="SubmissionType"/>
        </object-type>
        <object-type name="QRenderCapabilities">
            <enum-type name="API"/>
            <enum-type name="Profile"/>
        </object-type>
        <object-type name="QRenderCapture"/>
        <object-type name="QRenderCaptureReply"/>
        <object-type name="QRenderPass"/>
        <object-type name="QRenderPassFilter"/>
        <object-type name="QRenderSettings">
            <enum-type name="RenderPolicy"/>
        </object-type>
        <object-type name="QRenderState"/>
        <object-type name="QRenderStateSet"/>
        <object-type name="QRenderSurfaceSelector"/>
        <object-type name="QRenderTarget"/>
        <object-type name="QRenderTargetOutput">
            <enum-type name="AttachmentPoint"/>
        </object-type>
        <object-type name="QRenderTargetSelector"/>
        <object-type name="QSceneLoader">
            <enum-type name="Status"/>
            <enum-type name="ComponentType"/>
        </object-type>
        <object-type name="QScissorTest"/>
        <object-type name="QScreenRayCaster"/>
        <object-type name="QSeamlessCubemap"/>
        <object-type name="QSetFence">
            <enum-type name="HandleType"/>
        </object-type>
        <object-type name="QShaderData"/>
        <object-type name="QShaderProgram">
            <enum-type name="Format"/>
            <enum-type name="ShaderType"/>
            <enum-type name="Status"/>
        </object-type>
        <object-type name="QShaderImage">
            <enum-type name="Access"/>
            <enum-type name="ImageFormat"/>
        </object-type>
        <object-type name="QShaderProgramBuilder"/>
        <object-type name="QSharedGLTexture"/>
        <object-type name="QSortPolicy">
            <enum-type name="SortType"/>
        </object-type>
        <object-type name="QSpotLight"/>
        <object-type name="QStencilMask"/>
        <object-type name="QStencilOperation"/>
        <object-type name="QStencilOperationArguments">
            <enum-type name="FaceMode"/>
            <enum-type name="Operation"/>
        </object-type>
        <object-type name="QStencilTest"/>
        <object-type name="QStencilTestArguments">
            <enum-type name="StencilFaceMode"/>
            <enum-type name="StencilFunction"/>
        </object-type>
        <object-type name="QSubtreeEnabler">
            <enum-type name="Enablement"/>
        </object-type>
        <object-type name="QTechnique"/>
        <object-type name="QTechniqueFilter"/>
        <object-type name="QTexture1D"/>
        <object-type name="QTexture1DArray"/>
        <object-type name="QTexture2D"/>
        <object-type name="QTexture2DArray"/>
        <object-type name="QTexture2DMultisample"/>
        <object-type name="QTexture2DMultisampleArray"/>
        <object-type name="QTexture3D"/>
        <object-type name="QTextureBuffer"/>
        <object-type name="QTextureCubeMap"/>
        <object-type name="QTextureCubeMapArray"/>
        <object-type name="QTextureData"/>
        <value-type name="QTextureDataUpdate"/>
        <object-type name="QTextureImage">
            <enum-type name="Status"/>
        </object-type>
        <object-type name="QTextureImageData"/>
        <object-type name="QTextureImageDataGenerator">
            <modify-function signature="QTextureImageDataGenerator()" remove="all"/>
        </object-type>
        <object-type name="QTextureLoader"/>
        <object-type name="QTextureRectangle"/>
        <object-type name="QTextureWrapMode">
            <enum-type name="WrapMode"/>
        </object-type>
        <object-type name="QViewport"/>
        <object-type name="QWaitFence">
            <enum-type name="HandleType"/>
        </object-type>
    </namespace-type>
</typesystem>
