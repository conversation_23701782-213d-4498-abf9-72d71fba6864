<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtWebEngineWidgets">
  <load-typesystem name="typesystem_core.xml" generate="no"/>
  <load-typesystem name="typesystem_gui.xml" generate="no"/>
  <load-typesystem name="typesystem_widgets.xml" generate="no"/>
  <load-typesystem name="typesystem_network.xml" generate="no"/>
  <load-typesystem name="typesystem_webenginecore.xml" generate="no"/>
  <load-typesystem name="typesystem_printsupport.xml" generate="no"/>

  <object-type name="QWebEngineView">
      <add-function signature="findText(const QString &amp;,QWebEnginePage::FindFlags,PyObject*)">
          <inject-code class="target" position="beginning" file="../glue/qtwebenginewidgets.cpp" snippet="qwebenginepage-findtext"/>
      </add-function>
  </object-type>

</typesystem>
