# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtCharts, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtCharts`

import PySide6.QtCharts
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets

import enum
from typing import Any, ClassVar, Dict, List, Optional, Sequence, Tuple, Type, Union, overload
from PySide6.QtCore import Signal


NoneType = type(None)


class QAbstractAxis(PySide6.QtCore.QObject):

    colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
    gridLineColorChanged     : ClassVar[Signal] = ... # gridLineColorChanged(QColor)
    gridLinePenChanged       : ClassVar[Signal] = ... # gridLinePenChanged(QPen)
    gridVisibleChanged       : ClassVar[Signal] = ... # gridVisibleChanged(bool)
    labelsAngleChanged       : ClassVar[Signal] = ... # labelsAngleChanged(int)
    labelsBrushChanged       : ClassVar[Signal] = ... # labelsBrushChanged(QBrush)
    labelsColorChanged       : ClassVar[Signal] = ... # labelsColorChanged(QColor)
    labelsEditableChanged    : ClassVar[Signal] = ... # labelsEditableChanged(bool)
    labelsFontChanged        : ClassVar[Signal] = ... # labelsFontChanged(QFont)
    labelsTruncatedChanged   : ClassVar[Signal] = ... # labelsTruncatedChanged(bool)
    labelsVisibleChanged     : ClassVar[Signal] = ... # labelsVisibleChanged(bool)
    linePenChanged           : ClassVar[Signal] = ... # linePenChanged(QPen)
    lineVisibleChanged       : ClassVar[Signal] = ... # lineVisibleChanged(bool)
    minorGridLineColorChanged: ClassVar[Signal] = ... # minorGridLineColorChanged(QColor)
    minorGridLinePenChanged  : ClassVar[Signal] = ... # minorGridLinePenChanged(QPen)
    minorGridVisibleChanged  : ClassVar[Signal] = ... # minorGridVisibleChanged(bool)
    reverseChanged           : ClassVar[Signal] = ... # reverseChanged(bool)
    shadesBorderColorChanged : ClassVar[Signal] = ... # shadesBorderColorChanged(QColor)
    shadesBrushChanged       : ClassVar[Signal] = ... # shadesBrushChanged(QBrush)
    shadesColorChanged       : ClassVar[Signal] = ... # shadesColorChanged(QColor)
    shadesPenChanged         : ClassVar[Signal] = ... # shadesPenChanged(QPen)
    shadesVisibleChanged     : ClassVar[Signal] = ... # shadesVisibleChanged(bool)
    titleBrushChanged        : ClassVar[Signal] = ... # titleBrushChanged(QBrush)
    titleFontChanged         : ClassVar[Signal] = ... # titleFontChanged(QFont)
    titleTextChanged         : ClassVar[Signal] = ... # titleTextChanged(QString)
    titleVisibleChanged      : ClassVar[Signal] = ... # titleVisibleChanged(bool)
    truncateLabelsChanged    : ClassVar[Signal] = ... # truncateLabelsChanged(bool)
    visibleChanged           : ClassVar[Signal] = ... # visibleChanged(bool)

    class AxisType(enum.Enum):

        AxisTypeNoAxis           : QAbstractAxis.AxisType = ... # 0x0
        AxisTypeValue            : QAbstractAxis.AxisType = ... # 0x1
        AxisTypeBarCategory      : QAbstractAxis.AxisType = ... # 0x2
        AxisTypeCategory         : QAbstractAxis.AxisType = ... # 0x4
        AxisTypeDateTime         : QAbstractAxis.AxisType = ... # 0x8
        AxisTypeLogValue         : QAbstractAxis.AxisType = ... # 0x10
        AxisTypeColor            : QAbstractAxis.AxisType = ... # 0x20


    def alignment(self) -> PySide6.QtCore.Qt.AlignmentFlag: ...
    def gridLineColor(self) -> PySide6.QtGui.QColor: ...
    def gridLinePen(self) -> PySide6.QtGui.QPen: ...
    def hide(self) -> None: ...
    def isGridLineVisible(self) -> bool: ...
    def isLineVisible(self) -> bool: ...
    def isMinorGridLineVisible(self) -> bool: ...
    def isReverse(self) -> bool: ...
    def isTitleVisible(self) -> bool: ...
    def isVisible(self) -> bool: ...
    def labelsAngle(self) -> int: ...
    def labelsBrush(self) -> PySide6.QtGui.QBrush: ...
    def labelsColor(self) -> PySide6.QtGui.QColor: ...
    def labelsEditable(self) -> bool: ...
    def labelsFont(self) -> PySide6.QtGui.QFont: ...
    def labelsTruncated(self) -> bool: ...
    def labelsVisible(self) -> bool: ...
    def linePen(self) -> PySide6.QtGui.QPen: ...
    def linePenColor(self) -> PySide6.QtGui.QColor: ...
    def minorGridLineColor(self) -> PySide6.QtGui.QColor: ...
    def minorGridLinePen(self) -> PySide6.QtGui.QPen: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def setGridLineColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setGridLinePen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setGridLineVisible(self, visible: bool = ...) -> None: ...
    def setLabelsAngle(self, angle: int) -> None: ...
    def setLabelsBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setLabelsColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLabelsEditable(self, editable: bool = ...) -> None: ...
    def setLabelsFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setLabelsVisible(self, visible: bool = ...) -> None: ...
    def setLinePen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setLinePenColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLineVisible(self, visible: bool = ...) -> None: ...
    def setMax(self, max: Any) -> None: ...
    def setMin(self, min: Any) -> None: ...
    def setMinorGridLineColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setMinorGridLinePen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setMinorGridLineVisible(self, visible: bool = ...) -> None: ...
    def setRange(self, min: Any, max: Any) -> None: ...
    def setReverse(self, reverse: bool = ...) -> None: ...
    def setShadesBorderColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setShadesBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setShadesColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setShadesPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setShadesVisible(self, visible: bool = ...) -> None: ...
    def setTitleBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setTitleFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setTitleText(self, title: str) -> None: ...
    def setTitleVisible(self, visible: bool = ...) -> None: ...
    def setTruncateLabels(self, truncateLabels: bool = ...) -> None: ...
    def setVisible(self, visible: bool = ...) -> None: ...
    def shadesBorderColor(self) -> PySide6.QtGui.QColor: ...
    def shadesBrush(self) -> PySide6.QtGui.QBrush: ...
    def shadesColor(self) -> PySide6.QtGui.QColor: ...
    def shadesPen(self) -> PySide6.QtGui.QPen: ...
    def shadesVisible(self) -> bool: ...
    def show(self) -> None: ...
    def titleBrush(self) -> PySide6.QtGui.QBrush: ...
    def titleFont(self) -> PySide6.QtGui.QFont: ...
    def titleText(self) -> str: ...
    def truncateLabels(self) -> bool: ...
    def type(self) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QAbstractBarSeries(PySide6.QtCharts.QAbstractSeries):

    barsetsAdded             : ClassVar[Signal] = ... # barsetsAdded(QList<QBarSet*>)
    barsetsRemoved           : ClassVar[Signal] = ... # barsetsRemoved(QList<QBarSet*>)
    clicked                  : ClassVar[Signal] = ... # clicked(int,QBarSet*)
    countChanged             : ClassVar[Signal] = ... # countChanged()
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked(int,QBarSet*)
    hovered                  : ClassVar[Signal] = ... # hovered(bool,int,QBarSet*)
    labelsAngleChanged       : ClassVar[Signal] = ... # labelsAngleChanged(double)
    labelsFormatChanged      : ClassVar[Signal] = ... # labelsFormatChanged(QString)
    labelsPositionChanged    : ClassVar[Signal] = ... # labelsPositionChanged(QAbstractBarSeries::LabelsPosition)
    labelsPrecisionChanged   : ClassVar[Signal] = ... # labelsPrecisionChanged(int)
    labelsVisibleChanged     : ClassVar[Signal] = ... # labelsVisibleChanged()
    pressed                  : ClassVar[Signal] = ... # pressed(int,QBarSet*)
    released                 : ClassVar[Signal] = ... # released(int,QBarSet*)

    class LabelsPosition(enum.Enum):

        LabelsCenter             : QAbstractBarSeries.LabelsPosition = ... # 0x0
        LabelsInsideEnd          : QAbstractBarSeries.LabelsPosition = ... # 0x1
        LabelsInsideBase         : QAbstractBarSeries.LabelsPosition = ... # 0x2
        LabelsOutsideEnd         : QAbstractBarSeries.LabelsPosition = ... # 0x3


    @overload
    def append(self, set: PySide6.QtCharts.QBarSet) -> bool: ...
    @overload
    def append(self, sets: Sequence[PySide6.QtCharts.QBarSet]) -> bool: ...
    def barSets(self) -> List[PySide6.QtCharts.QBarSet]: ...
    def barWidth(self) -> float: ...
    def clear(self) -> None: ...
    def count(self) -> int: ...
    def insert(self, index: int, set: PySide6.QtCharts.QBarSet) -> bool: ...
    def isLabelsVisible(self) -> bool: ...
    def labelsAngle(self) -> float: ...
    def labelsFormat(self) -> str: ...
    def labelsPosition(self) -> PySide6.QtCharts.QAbstractBarSeries.LabelsPosition: ...
    def labelsPrecision(self) -> int: ...
    def remove(self, set: PySide6.QtCharts.QBarSet) -> bool: ...
    def setBarWidth(self, width: float) -> None: ...
    def setLabelsAngle(self, angle: float) -> None: ...
    def setLabelsFormat(self, format: str) -> None: ...
    def setLabelsPosition(self, position: PySide6.QtCharts.QAbstractBarSeries.LabelsPosition) -> None: ...
    def setLabelsPrecision(self, precision: int) -> None: ...
    def setLabelsVisible(self, visible: bool = ...) -> None: ...
    def take(self, set: PySide6.QtCharts.QBarSet) -> bool: ...


class QAbstractSeries(PySide6.QtCore.QObject):

    nameChanged              : ClassVar[Signal] = ... # nameChanged()
    opacityChanged           : ClassVar[Signal] = ... # opacityChanged()
    useOpenGLChanged         : ClassVar[Signal] = ... # useOpenGLChanged()
    visibleChanged           : ClassVar[Signal] = ... # visibleChanged()

    class SeriesType(enum.Enum):

        SeriesTypeLine           : QAbstractSeries.SeriesType = ... # 0x0
        SeriesTypeArea           : QAbstractSeries.SeriesType = ... # 0x1
        SeriesTypeBar            : QAbstractSeries.SeriesType = ... # 0x2
        SeriesTypeStackedBar     : QAbstractSeries.SeriesType = ... # 0x3
        SeriesTypePercentBar     : QAbstractSeries.SeriesType = ... # 0x4
        SeriesTypePie            : QAbstractSeries.SeriesType = ... # 0x5
        SeriesTypeScatter        : QAbstractSeries.SeriesType = ... # 0x6
        SeriesTypeSpline         : QAbstractSeries.SeriesType = ... # 0x7
        SeriesTypeHorizontalBar  : QAbstractSeries.SeriesType = ... # 0x8
        SeriesTypeHorizontalStackedBar: QAbstractSeries.SeriesType = ... # 0x9
        SeriesTypeHorizontalPercentBar: QAbstractSeries.SeriesType = ... # 0xa
        SeriesTypeBoxPlot        : QAbstractSeries.SeriesType = ... # 0xb
        SeriesTypeCandlestick    : QAbstractSeries.SeriesType = ... # 0xc


    def attachAxis(self, axis: PySide6.QtCharts.QAbstractAxis) -> bool: ...
    def attachedAxes(self) -> List[PySide6.QtCharts.QAbstractAxis]: ...
    def chart(self) -> PySide6.QtCharts.QChart: ...
    def detachAxis(self, axis: PySide6.QtCharts.QAbstractAxis) -> bool: ...
    def hide(self) -> None: ...
    def isVisible(self) -> bool: ...
    def name(self) -> str: ...
    def opacity(self) -> float: ...
    def setName(self, name: str) -> None: ...
    def setOpacity(self, opacity: float) -> None: ...
    def setUseOpenGL(self, enable: bool = ...) -> None: ...
    def setVisible(self, visible: bool = ...) -> None: ...
    def show(self) -> None: ...
    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...
    def useOpenGL(self) -> bool: ...


class QAreaLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QAreaSeries, legend: PySide6.QtCharts.QLegend, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def series(self) -> PySide6.QtCharts.QAreaSeries: ...
    def type(self) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QAreaSeries(PySide6.QtCharts.QAbstractSeries):

    borderColorChanged       : ClassVar[Signal] = ... # borderColorChanged(QColor)
    clicked                  : ClassVar[Signal] = ... # clicked(QPointF)
    colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked(QPointF)
    hovered                  : ClassVar[Signal] = ... # hovered(QPointF,bool)
    pointLabelsClippingChanged: ClassVar[Signal] = ... # pointLabelsClippingChanged(bool)
    pointLabelsColorChanged  : ClassVar[Signal] = ... # pointLabelsColorChanged(QColor)
    pointLabelsFontChanged   : ClassVar[Signal] = ... # pointLabelsFontChanged(QFont)
    pointLabelsFormatChanged : ClassVar[Signal] = ... # pointLabelsFormatChanged(QString)
    pointLabelsVisibilityChanged: ClassVar[Signal] = ... # pointLabelsVisibilityChanged(bool)
    pressed                  : ClassVar[Signal] = ... # pressed(QPointF)
    released                 : ClassVar[Signal] = ... # released(QPointF)
    selected                 : ClassVar[Signal] = ... # selected()

    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, upperSeries: PySide6.QtCharts.QLineSeries, lowerSeries: Optional[PySide6.QtCharts.QLineSeries] = ...) -> None: ...

    def borderColor(self) -> PySide6.QtGui.QColor: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def color(self) -> PySide6.QtGui.QColor: ...
    def lowerSeries(self) -> PySide6.QtCharts.QLineSeries: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def pointLabelsClipping(self) -> bool: ...
    def pointLabelsColor(self) -> PySide6.QtGui.QColor: ...
    def pointLabelsFont(self) -> PySide6.QtGui.QFont: ...
    def pointLabelsFormat(self) -> str: ...
    def pointLabelsVisible(self) -> bool: ...
    def pointsVisible(self) -> bool: ...
    def setBorderColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLowerSeries(self, series: PySide6.QtCharts.QLineSeries) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setPointLabelsClipping(self, enabled: bool = ...) -> None: ...
    def setPointLabelsColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setPointLabelsFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setPointLabelsFormat(self, format: str) -> None: ...
    def setPointLabelsVisible(self, visible: bool = ...) -> None: ...
    def setPointsVisible(self, visible: bool = ...) -> None: ...
    def setUpperSeries(self, series: PySide6.QtCharts.QLineSeries) -> None: ...
    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...
    def upperSeries(self) -> PySide6.QtCharts.QLineSeries: ...


class QBarCategoryAxis(PySide6.QtCharts.QAbstractAxis):

    categoriesChanged        : ClassVar[Signal] = ... # categoriesChanged()
    countChanged             : ClassVar[Signal] = ... # countChanged()
    maxChanged               : ClassVar[Signal] = ... # maxChanged(QString)
    minChanged               : ClassVar[Signal] = ... # minChanged(QString)
    rangeChanged             : ClassVar[Signal] = ... # rangeChanged(QString,QString)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    @overload
    def append(self, categories: Sequence[str]) -> None: ...
    @overload
    def append(self, category: str) -> None: ...
    def at(self, index: int) -> str: ...
    def categories(self) -> List[str]: ...
    def clear(self) -> None: ...
    def count(self) -> int: ...
    def insert(self, index: int, category: str) -> None: ...
    def max(self) -> str: ...
    def min(self) -> str: ...
    def remove(self, category: str) -> None: ...
    def replace(self, oldCategory: str, newCategory: str) -> None: ...
    def setCategories(self, categories: Sequence[str]) -> None: ...
    def setMax(self, maxCategory: str) -> None: ...
    def setMin(self, minCategory: str) -> None: ...
    def setRange(self, minCategory: str, maxCategory: str) -> None: ...
    def type(self) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QBarLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QAbstractBarSeries, barset: PySide6.QtCharts.QBarSet, legend: PySide6.QtCharts.QLegend, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def barset(self) -> PySide6.QtCharts.QBarSet: ...
    def series(self) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def type(self) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QBarModelMapper(PySide6.QtCore.QObject):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def count(self) -> int: ...
    def first(self) -> int: ...
    def firstBarSetSection(self) -> int: ...
    def lastBarSetSection(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def setCount(self, count: int) -> None: ...
    def setFirst(self, first: int) -> None: ...
    def setFirstBarSetSection(self, firstBarSetSection: int) -> None: ...
    def setLastBarSetSection(self, lastBarSetSection: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QAbstractBarSeries) -> None: ...


class QBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QBarSet(PySide6.QtCore.QObject):

    borderColorChanged       : ClassVar[Signal] = ... # borderColorChanged(QColor)
    brushChanged             : ClassVar[Signal] = ... # brushChanged()
    clicked                  : ClassVar[Signal] = ... # clicked(int)
    colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked(int)
    hovered                  : ClassVar[Signal] = ... # hovered(bool,int)
    labelBrushChanged        : ClassVar[Signal] = ... # labelBrushChanged()
    labelChanged             : ClassVar[Signal] = ... # labelChanged()
    labelColorChanged        : ClassVar[Signal] = ... # labelColorChanged(QColor)
    labelFontChanged         : ClassVar[Signal] = ... # labelFontChanged()
    penChanged               : ClassVar[Signal] = ... # penChanged()
    pressed                  : ClassVar[Signal] = ... # pressed(int)
    released                 : ClassVar[Signal] = ... # released(int)
    selectedBarsChanged      : ClassVar[Signal] = ... # selectedBarsChanged(QList<int>)
    selectedColorChanged     : ClassVar[Signal] = ... # selectedColorChanged(QColor)
    valueChanged             : ClassVar[Signal] = ... # valueChanged(int)
    valuesAdded              : ClassVar[Signal] = ... # valuesAdded(int,int)
    valuesRemoved            : ClassVar[Signal] = ... # valuesRemoved(int,int)

    def __init__(self, label: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def __lshift__(self, value: float) -> PySide6.QtCharts.QBarSet: ...
    @overload
    def append(self, value: float) -> None: ...
    @overload
    def append(self, values: Sequence[float]) -> None: ...
    def at(self, index: int) -> float: ...
    def borderColor(self) -> PySide6.QtGui.QColor: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def color(self) -> PySide6.QtGui.QColor: ...
    def count(self) -> int: ...
    def deselectAllBars(self) -> None: ...
    def deselectBar(self, index: int) -> None: ...
    def deselectBars(self, indexes: Sequence[int]) -> None: ...
    def insert(self, index: int, value: float) -> None: ...
    def isBarSelected(self, index: int) -> bool: ...
    def label(self) -> str: ...
    def labelBrush(self) -> PySide6.QtGui.QBrush: ...
    def labelColor(self) -> PySide6.QtGui.QColor: ...
    def labelFont(self) -> PySide6.QtGui.QFont: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def remove(self, index: int, count: int = ...) -> None: ...
    def replace(self, index: int, value: float) -> None: ...
    def selectAllBars(self) -> None: ...
    def selectBar(self, index: int) -> None: ...
    def selectBars(self, indexes: Sequence[int]) -> None: ...
    def selectedBars(self) -> List[int]: ...
    def selectedColor(self) -> PySide6.QtGui.QColor: ...
    def setBarSelected(self, index: int, selected: bool) -> None: ...
    def setBorderColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLabel(self, label: str) -> None: ...
    def setLabelBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setLabelColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLabelFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setSelectedColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def sum(self) -> float: ...
    def toggleSelection(self, indexes: Sequence[int]) -> None: ...


class QBoxPlotLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QBoxPlotSeries, legend: PySide6.QtCharts.QLegend, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def series(self) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def type(self) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QBoxPlotModelMapper(PySide6.QtCore.QObject):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def count(self) -> int: ...
    def first(self) -> int: ...
    def firstBoxSetSection(self) -> int: ...
    def lastBoxSetSection(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def setCount(self, count: int) -> None: ...
    def setFirst(self, first: int) -> None: ...
    def setFirstBoxSetSection(self, firstBoxSetSection: int) -> None: ...
    def setLastBoxSetSection(self, lastBoxSetSection: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QBoxPlotSeries) -> None: ...


class QBoxPlotSeries(PySide6.QtCharts.QAbstractSeries):

    boxOutlineVisibilityChanged: ClassVar[Signal] = ... # boxOutlineVisibilityChanged()
    boxWidthChanged          : ClassVar[Signal] = ... # boxWidthChanged()
    boxsetsAdded             : ClassVar[Signal] = ... # boxsetsAdded(QList<QBoxSet*>)
    boxsetsRemoved           : ClassVar[Signal] = ... # boxsetsRemoved(QList<QBoxSet*>)
    brushChanged             : ClassVar[Signal] = ... # brushChanged()
    clicked                  : ClassVar[Signal] = ... # clicked(QBoxSet*)
    countChanged             : ClassVar[Signal] = ... # countChanged()
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked(QBoxSet*)
    hovered                  : ClassVar[Signal] = ... # hovered(bool,QBoxSet*)
    penChanged               : ClassVar[Signal] = ... # penChanged()
    pressed                  : ClassVar[Signal] = ... # pressed(QBoxSet*)
    released                 : ClassVar[Signal] = ... # released(QBoxSet*)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    @overload
    def append(self, box: PySide6.QtCharts.QBoxSet) -> bool: ...
    @overload
    def append(self, boxes: Sequence[PySide6.QtCharts.QBoxSet]) -> bool: ...
    def boxOutlineVisible(self) -> bool: ...
    def boxSets(self) -> List[PySide6.QtCharts.QBoxSet]: ...
    def boxWidth(self) -> float: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def clear(self) -> None: ...
    def count(self) -> int: ...
    def insert(self, index: int, box: PySide6.QtCharts.QBoxSet) -> bool: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def remove(self, box: PySide6.QtCharts.QBoxSet) -> bool: ...
    def setBoxOutlineVisible(self, visible: bool) -> None: ...
    def setBoxWidth(self, width: float) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def take(self, box: PySide6.QtCharts.QBoxSet) -> bool: ...
    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QBoxSet(PySide6.QtCore.QObject):

    brushChanged             : ClassVar[Signal] = ... # brushChanged()
    cleared                  : ClassVar[Signal] = ... # cleared()
    clicked                  : ClassVar[Signal] = ... # clicked()
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked()
    hovered                  : ClassVar[Signal] = ... # hovered(bool)
    penChanged               : ClassVar[Signal] = ... # penChanged()
    pressed                  : ClassVar[Signal] = ... # pressed()
    released                 : ClassVar[Signal] = ... # released()
    valueChanged             : ClassVar[Signal] = ... # valueChanged(int)
    valuesChanged            : ClassVar[Signal] = ... # valuesChanged()

    class ValuePositions(enum.IntEnum):

        LowerExtreme             : QBoxSet.ValuePositions = ... # 0x0
        LowerQuartile            : QBoxSet.ValuePositions = ... # 0x1
        Median                   : QBoxSet.ValuePositions = ... # 0x2
        UpperQuartile            : QBoxSet.ValuePositions = ... # 0x3
        UpperExtreme             : QBoxSet.ValuePositions = ... # 0x4


    @overload
    def __init__(self, label: str = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, le: float, lq: float, m: float, uq: float, ue: float, label: str = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def __lshift__(self, value: float) -> PySide6.QtCharts.QBoxSet: ...
    @overload
    def append(self, value: float) -> None: ...
    @overload
    def append(self, values: Sequence[float]) -> None: ...
    def at(self, index: int) -> float: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def clear(self) -> None: ...
    def count(self) -> int: ...
    def label(self) -> str: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setLabel(self, label: str) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setValue(self, index: int, value: float) -> None: ...


class QCandlestickLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QCandlestickSeries, legend: PySide6.QtCharts.QLegend, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def series(self) -> PySide6.QtCharts.QCandlestickSeries: ...
    def type(self) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QCandlestickModelMapper(PySide6.QtCore.QObject):

    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def close(self) -> int: ...
    def firstSetSection(self) -> int: ...
    def high(self) -> int: ...
    def lastSetSection(self) -> int: ...
    def low(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def open(self) -> int: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self) -> PySide6.QtCharts.QCandlestickSeries: ...
    def setClose(self, close: int) -> None: ...
    def setFirstSetSection(self, firstSetSection: int) -> None: ...
    def setHigh(self, high: int) -> None: ...
    def setLastSetSection(self, lastSetSection: int) -> None: ...
    def setLow(self, low: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setOpen(self, open: int) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QCandlestickSeries) -> None: ...
    def setTimestamp(self, timestamp: int) -> None: ...
    def timestamp(self) -> int: ...


class QCandlestickSeries(PySide6.QtCharts.QAbstractSeries):

    bodyOutlineVisibilityChanged: ClassVar[Signal] = ... # bodyOutlineVisibilityChanged()
    bodyWidthChanged         : ClassVar[Signal] = ... # bodyWidthChanged()
    brushChanged             : ClassVar[Signal] = ... # brushChanged()
    candlestickSetsAdded     : ClassVar[Signal] = ... # candlestickSetsAdded(QList<QCandlestickSet*>)
    candlestickSetsRemoved   : ClassVar[Signal] = ... # candlestickSetsRemoved(QList<QCandlestickSet*>)
    capsVisibilityChanged    : ClassVar[Signal] = ... # capsVisibilityChanged()
    capsWidthChanged         : ClassVar[Signal] = ... # capsWidthChanged()
    clicked                  : ClassVar[Signal] = ... # clicked(QCandlestickSet*)
    countChanged             : ClassVar[Signal] = ... # countChanged()
    decreasingColorChanged   : ClassVar[Signal] = ... # decreasingColorChanged()
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked(QCandlestickSet*)
    hovered                  : ClassVar[Signal] = ... # hovered(bool,QCandlestickSet*)
    increasingColorChanged   : ClassVar[Signal] = ... # increasingColorChanged()
    maximumColumnWidthChanged: ClassVar[Signal] = ... # maximumColumnWidthChanged()
    minimumColumnWidthChanged: ClassVar[Signal] = ... # minimumColumnWidthChanged()
    penChanged               : ClassVar[Signal] = ... # penChanged()
    pressed                  : ClassVar[Signal] = ... # pressed(QCandlestickSet*)
    released                 : ClassVar[Signal] = ... # released(QCandlestickSet*)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    @overload
    def append(self, set: PySide6.QtCharts.QCandlestickSet) -> bool: ...
    @overload
    def append(self, sets: Sequence[PySide6.QtCharts.QCandlestickSet]) -> bool: ...
    def bodyOutlineVisible(self) -> bool: ...
    def bodyWidth(self) -> float: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def capsVisible(self) -> bool: ...
    def capsWidth(self) -> float: ...
    def clear(self) -> None: ...
    def count(self) -> int: ...
    def decreasingColor(self) -> PySide6.QtGui.QColor: ...
    def increasingColor(self) -> PySide6.QtGui.QColor: ...
    def insert(self, index: int, set: PySide6.QtCharts.QCandlestickSet) -> bool: ...
    def maximumColumnWidth(self) -> float: ...
    def minimumColumnWidth(self) -> float: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    @overload
    def remove(self, set: PySide6.QtCharts.QCandlestickSet) -> bool: ...
    @overload
    def remove(self, sets: Sequence[PySide6.QtCharts.QCandlestickSet]) -> bool: ...
    def setBodyOutlineVisible(self, bodyOutlineVisible: bool) -> None: ...
    def setBodyWidth(self, bodyWidth: float) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setCapsVisible(self, capsVisible: bool) -> None: ...
    def setCapsWidth(self, capsWidth: float) -> None: ...
    def setDecreasingColor(self, decreasingColor: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setIncreasingColor(self, increasingColor: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setMaximumColumnWidth(self, maximumColumnWidth: float) -> None: ...
    def setMinimumColumnWidth(self, minimumColumnWidth: float) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def sets(self) -> List[PySide6.QtCharts.QCandlestickSet]: ...
    def take(self, set: PySide6.QtCharts.QCandlestickSet) -> bool: ...
    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QCandlestickSet(PySide6.QtCore.QObject):

    brushChanged             : ClassVar[Signal] = ... # brushChanged()
    clicked                  : ClassVar[Signal] = ... # clicked()
    closeChanged             : ClassVar[Signal] = ... # closeChanged()
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked()
    highChanged              : ClassVar[Signal] = ... # highChanged()
    hovered                  : ClassVar[Signal] = ... # hovered(bool)
    lowChanged               : ClassVar[Signal] = ... # lowChanged()
    openChanged              : ClassVar[Signal] = ... # openChanged()
    penChanged               : ClassVar[Signal] = ... # penChanged()
    pressed                  : ClassVar[Signal] = ... # pressed()
    released                 : ClassVar[Signal] = ... # released()
    timestampChanged         : ClassVar[Signal] = ... # timestampChanged()

    @overload
    def __init__(self, open: float, high: float, low: float, close: float, timestamp: float = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, timestamp: float = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def brush(self) -> PySide6.QtGui.QBrush: ...
    def close(self) -> float: ...
    def high(self) -> float: ...
    def low(self) -> float: ...
    def open(self) -> float: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setClose(self, close: float) -> None: ...
    def setHigh(self, high: float) -> None: ...
    def setLow(self, low: float) -> None: ...
    def setOpen(self, open: float) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setTimestamp(self, timestamp: float) -> None: ...
    def timestamp(self) -> float: ...


class QCategoryAxis(PySide6.QtCharts.QValueAxis):

    categoriesChanged        : ClassVar[Signal] = ... # categoriesChanged()
    labelsPositionChanged    : ClassVar[Signal] = ... # labelsPositionChanged(QCategoryAxis::AxisLabelsPosition)

    class AxisLabelsPosition(enum.Enum):

        AxisLabelsPositionCenter : QCategoryAxis.AxisLabelsPosition = ... # 0x0
        AxisLabelsPositionOnValue: QCategoryAxis.AxisLabelsPosition = ... # 0x1


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def append(self, label: str, categoryEndValue: float) -> None: ...
    def categoriesLabels(self) -> List[str]: ...
    def count(self) -> int: ...
    def endValue(self, categoryLabel: str) -> float: ...
    def labelsPosition(self) -> PySide6.QtCharts.QCategoryAxis.AxisLabelsPosition: ...
    def remove(self, label: str) -> None: ...
    def replaceLabel(self, oldLabel: str, newLabel: str) -> None: ...
    def setLabelsPosition(self, position: PySide6.QtCharts.QCategoryAxis.AxisLabelsPosition) -> None: ...
    def setStartValue(self, min: float) -> None: ...
    def startValue(self, categoryLabel: str = ...) -> float: ...
    def type(self) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QChart(PySide6.QtWidgets.QGraphicsWidget):

    plotAreaChanged          : ClassVar[Signal] = ... # plotAreaChanged(QRectF)

    class AnimationOption(enum.Flag):

        NoAnimation              : QChart.AnimationOption = ... # 0x0
        GridAxisAnimations       : QChart.AnimationOption = ... # 0x1
        SeriesAnimations         : QChart.AnimationOption = ... # 0x2
        AllAnimations            : QChart.AnimationOption = ... # 0x3

    class ChartTheme(enum.Enum):

        ChartThemeLight          : QChart.ChartTheme = ... # 0x0
        ChartThemeBlueCerulean   : QChart.ChartTheme = ... # 0x1
        ChartThemeDark           : QChart.ChartTheme = ... # 0x2
        ChartThemeBrownSand      : QChart.ChartTheme = ... # 0x3
        ChartThemeBlueNcs        : QChart.ChartTheme = ... # 0x4
        ChartThemeHighContrast   : QChart.ChartTheme = ... # 0x5
        ChartThemeBlueIcy        : QChart.ChartTheme = ... # 0x6
        ChartThemeQt             : QChart.ChartTheme = ... # 0x7

    class ChartType(enum.Enum):

        ChartTypeUndefined       : QChart.ChartType = ... # 0x0
        ChartTypeCartesian       : QChart.ChartType = ... # 0x1
        ChartTypePolar           : QChart.ChartType = ... # 0x2


    @overload
    def __init__(self, parent: Optional[PySide6.QtWidgets.QGraphicsItem] = ..., wFlags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...
    @overload
    def __init__(self, type: PySide6.QtCharts.QChart.ChartType, parent: PySide6.QtWidgets.QGraphicsItem, wFlags: PySide6.QtCore.Qt.WindowType) -> None: ...

    def addAxis(self, axis: PySide6.QtCharts.QAbstractAxis, alignment: PySide6.QtCore.Qt.AlignmentFlag) -> None: ...
    def addSeries(self, series: PySide6.QtCharts.QAbstractSeries) -> None: ...
    def animationDuration(self) -> int: ...
    def animationEasingCurve(self) -> PySide6.QtCore.QEasingCurve: ...
    def animationOptions(self) -> PySide6.QtCharts.QChart.AnimationOption: ...
    def axes(self, orientation: PySide6.QtCore.Qt.Orientation = ..., series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> List[PySide6.QtCharts.QAbstractAxis]: ...
    def axisX(self, series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> PySide6.QtCharts.QAbstractAxis: ...
    def axisY(self, series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> PySide6.QtCharts.QAbstractAxis: ...
    def backgroundBrush(self) -> PySide6.QtGui.QBrush: ...
    def backgroundPen(self) -> PySide6.QtGui.QPen: ...
    def backgroundRoundness(self) -> float: ...
    def chartType(self) -> PySide6.QtCharts.QChart.ChartType: ...
    def createDefaultAxes(self) -> None: ...
    def isBackgroundVisible(self) -> bool: ...
    def isDropShadowEnabled(self) -> bool: ...
    def isPlotAreaBackgroundVisible(self) -> bool: ...
    def isZoomed(self) -> bool: ...
    def legend(self) -> PySide6.QtCharts.QLegend: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def localizeNumbers(self) -> bool: ...
    def mapToPosition(self, value: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> PySide6.QtCore.QPointF: ...
    def mapToValue(self, position: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> PySide6.QtCore.QPointF: ...
    def margins(self) -> PySide6.QtCore.QMargins: ...
    def plotArea(self) -> PySide6.QtCore.QRectF: ...
    def plotAreaBackgroundBrush(self) -> PySide6.QtGui.QBrush: ...
    def plotAreaBackgroundPen(self) -> PySide6.QtGui.QPen: ...
    def removeAllSeries(self) -> None: ...
    def removeAxis(self, axis: PySide6.QtCharts.QAbstractAxis) -> None: ...
    def removeSeries(self, series: PySide6.QtCharts.QAbstractSeries) -> None: ...
    def scroll(self, dx: float, dy: float) -> None: ...
    def series(self) -> List[PySide6.QtCharts.QAbstractSeries]: ...
    def setAnimationDuration(self, msecs: int) -> None: ...
    def setAnimationEasingCurve(self, curve: Union[PySide6.QtCore.QEasingCurve, PySide6.QtCore.QEasingCurve.Type]) -> None: ...
    def setAnimationOptions(self, options: PySide6.QtCharts.QChart.AnimationOption) -> None: ...
    def setAxisX(self, axis: PySide6.QtCharts.QAbstractAxis, series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> None: ...
    def setAxisY(self, axis: PySide6.QtCharts.QAbstractAxis, series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> None: ...
    def setBackgroundBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setBackgroundPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setBackgroundRoundness(self, diameter: float) -> None: ...
    def setBackgroundVisible(self, visible: bool = ...) -> None: ...
    def setDropShadowEnabled(self, enabled: bool = ...) -> None: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def setLocalizeNumbers(self, localize: bool) -> None: ...
    def setMargins(self, margins: PySide6.QtCore.QMargins) -> None: ...
    def setPlotArea(self, rect: Union[PySide6.QtCore.QRectF, PySide6.QtCore.QRect]) -> None: ...
    def setPlotAreaBackgroundBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setPlotAreaBackgroundPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setPlotAreaBackgroundVisible(self, visible: bool = ...) -> None: ...
    def setTheme(self, theme: PySide6.QtCharts.QChart.ChartTheme) -> None: ...
    def setTitle(self, title: str) -> None: ...
    def setTitleBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setTitleFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def theme(self) -> PySide6.QtCharts.QChart.ChartTheme: ...
    def title(self) -> str: ...
    def titleBrush(self) -> PySide6.QtGui.QBrush: ...
    def titleFont(self) -> PySide6.QtGui.QFont: ...
    def zoom(self, factor: float) -> None: ...
    @overload
    def zoomIn(self) -> None: ...
    @overload
    def zoomIn(self, rect: Union[PySide6.QtCore.QRectF, PySide6.QtCore.QRect]) -> None: ...
    def zoomOut(self) -> None: ...
    def zoomReset(self) -> None: ...


class QChartView(PySide6.QtWidgets.QGraphicsView):

    class RubberBand(enum.Flag):

        NoRubberBand             : QChartView.RubberBand = ... # 0x0
        VerticalRubberBand       : QChartView.RubberBand = ... # 0x1
        HorizontalRubberBand     : QChartView.RubberBand = ... # 0x2
        RectangleRubberBand      : QChartView.RubberBand = ... # 0x3
        ClickThroughRubberBand   : QChartView.RubberBand = ... # 0x80


    @overload
    def __init__(self, chart: PySide6.QtCharts.QChart, parent: Optional[PySide6.QtWidgets.QWidget] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtWidgets.QWidget] = ...) -> None: ...

    def chart(self) -> PySide6.QtCharts.QChart: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def resizeEvent(self, event: PySide6.QtGui.QResizeEvent) -> None: ...
    def rubberBand(self) -> PySide6.QtCharts.QChartView.RubberBand: ...
    def setChart(self, chart: PySide6.QtCharts.QChart) -> None: ...
    def setRubberBand(self, rubberBands: PySide6.QtCharts.QChartView.RubberBand) -> None: ...


class QDateTimeAxis(PySide6.QtCharts.QAbstractAxis):

    formatChanged            : ClassVar[Signal] = ... # formatChanged(QString)
    maxChanged               : ClassVar[Signal] = ... # maxChanged(QDateTime)
    minChanged               : ClassVar[Signal] = ... # minChanged(QDateTime)
    rangeChanged             : ClassVar[Signal] = ... # rangeChanged(QDateTime,QDateTime)
    tickCountChanged         : ClassVar[Signal] = ... # tickCountChanged(int)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def format(self) -> str: ...
    def max(self) -> PySide6.QtCore.QDateTime: ...
    def min(self) -> PySide6.QtCore.QDateTime: ...
    def setFormat(self, format: str) -> None: ...
    def setMax(self, max: PySide6.QtCore.QDateTime) -> None: ...
    def setMin(self, min: PySide6.QtCore.QDateTime) -> None: ...
    def setRange(self, min: PySide6.QtCore.QDateTime, max: PySide6.QtCore.QDateTime) -> None: ...
    def setTickCount(self, count: int) -> None: ...
    def tickCount(self) -> int: ...
    def type(self) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QHBarModelMapper(PySide6.QtCharts.QBarModelMapper):

    columnCountChanged       : ClassVar[Signal] = ... # columnCountChanged()
    firstBarSetRowChanged    : ClassVar[Signal] = ... # firstBarSetRowChanged()
    firstColumnChanged       : ClassVar[Signal] = ... # firstColumnChanged()
    lastBarSetRowChanged     : ClassVar[Signal] = ... # lastBarSetRowChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def columnCount(self) -> int: ...
    def firstBarSetRow(self) -> int: ...
    def firstColumn(self) -> int: ...
    def lastBarSetRow(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def setColumnCount(self, columnCount: int) -> None: ...
    def setFirstBarSetRow(self, firstBarSetRow: int) -> None: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def setLastBarSetRow(self, lastBarSetRow: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QAbstractBarSeries) -> None: ...


class QHBoxPlotModelMapper(PySide6.QtCharts.QBoxPlotModelMapper):

    columnCountChanged       : ClassVar[Signal] = ... # columnCountChanged()
    firstBoxSetRowChanged    : ClassVar[Signal] = ... # firstBoxSetRowChanged()
    firstColumnChanged       : ClassVar[Signal] = ... # firstColumnChanged()
    lastBoxSetRowChanged     : ClassVar[Signal] = ... # lastBoxSetRowChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def columnCount(self) -> int: ...
    def firstBoxSetRow(self) -> int: ...
    def firstColumn(self) -> int: ...
    def lastBoxSetRow(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def setColumnCount(self, rowCount: int) -> None: ...
    def setFirstBoxSetRow(self, firstBoxSetRow: int) -> None: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def setLastBoxSetRow(self, lastBoxSetRow: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QBoxPlotSeries) -> None: ...


class QHCandlestickModelMapper(PySide6.QtCharts.QCandlestickModelMapper):

    closeColumnChanged       : ClassVar[Signal] = ... # closeColumnChanged()
    firstSetRowChanged       : ClassVar[Signal] = ... # firstSetRowChanged()
    highColumnChanged        : ClassVar[Signal] = ... # highColumnChanged()
    lastSetRowChanged        : ClassVar[Signal] = ... # lastSetRowChanged()
    lowColumnChanged         : ClassVar[Signal] = ... # lowColumnChanged()
    openColumnChanged        : ClassVar[Signal] = ... # openColumnChanged()
    timestampColumnChanged   : ClassVar[Signal] = ... # timestampColumnChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def closeColumn(self) -> int: ...
    def firstSetRow(self) -> int: ...
    def highColumn(self) -> int: ...
    def lastSetRow(self) -> int: ...
    def lowColumn(self) -> int: ...
    def openColumn(self) -> int: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def setCloseColumn(self, closeColumn: int) -> None: ...
    def setFirstSetRow(self, firstSetRow: int) -> None: ...
    def setHighColumn(self, highColumn: int) -> None: ...
    def setLastSetRow(self, lastSetRow: int) -> None: ...
    def setLowColumn(self, lowColumn: int) -> None: ...
    def setOpenColumn(self, openColumn: int) -> None: ...
    def setTimestampColumn(self, timestampColumn: int) -> None: ...
    def timestampColumn(self) -> int: ...


class QHPieModelMapper(PySide6.QtCharts.QPieModelMapper):

    columnCountChanged       : ClassVar[Signal] = ... # columnCountChanged()
    firstColumnChanged       : ClassVar[Signal] = ... # firstColumnChanged()
    labelsRowChanged         : ClassVar[Signal] = ... # labelsRowChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()
    valuesRowChanged         : ClassVar[Signal] = ... # valuesRowChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def columnCount(self) -> int: ...
    def firstColumn(self) -> int: ...
    def labelsRow(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self) -> PySide6.QtCharts.QPieSeries: ...
    def setColumnCount(self, columnCount: int) -> None: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def setLabelsRow(self, labelsRow: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QPieSeries) -> None: ...
    def setValuesRow(self, valuesRow: int) -> None: ...
    def valuesRow(self) -> int: ...


class QHXYModelMapper(PySide6.QtCharts.QXYModelMapper):

    columnCountChanged       : ClassVar[Signal] = ... # columnCountChanged()
    firstColumnChanged       : ClassVar[Signal] = ... # firstColumnChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()
    xRowChanged              : ClassVar[Signal] = ... # xRowChanged()
    yRowChanged              : ClassVar[Signal] = ... # yRowChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def columnCount(self) -> int: ...
    def firstColumn(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self) -> PySide6.QtCharts.QXYSeries: ...
    def setColumnCount(self, columnCount: int) -> None: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QXYSeries) -> None: ...
    def setXRow(self, xRow: int) -> None: ...
    def setYRow(self, yRow: int) -> None: ...
    def xRow(self) -> int: ...
    def yRow(self) -> int: ...


class QHorizontalBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QHorizontalPercentBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QHorizontalStackedBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QIntList(object): ...


class QLegend(PySide6.QtWidgets.QGraphicsWidget):

    attachedToChartChanged   : ClassVar[Signal] = ... # attachedToChartChanged(bool)
    backgroundVisibleChanged : ClassVar[Signal] = ... # backgroundVisibleChanged(bool)
    borderColorChanged       : ClassVar[Signal] = ... # borderColorChanged(QColor)
    colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
    fontChanged              : ClassVar[Signal] = ... # fontChanged(QFont)
    interactiveChanged       : ClassVar[Signal] = ... # interactiveChanged(bool)
    labelColorChanged        : ClassVar[Signal] = ... # labelColorChanged(QColor)
    markerShapeChanged       : ClassVar[Signal] = ... # markerShapeChanged(MarkerShape)
    reverseMarkersChanged    : ClassVar[Signal] = ... # reverseMarkersChanged(bool)
    showToolTipsChanged      : ClassVar[Signal] = ... # showToolTipsChanged(bool)

    class MarkerShape(enum.Enum):

        MarkerShapeDefault       : QLegend.MarkerShape = ... # 0x0
        MarkerShapeRectangle     : QLegend.MarkerShape = ... # 0x1
        MarkerShapeCircle        : QLegend.MarkerShape = ... # 0x2
        MarkerShapeFromSeries    : QLegend.MarkerShape = ... # 0x3
        MarkerShapeRotatedRectangle: QLegend.MarkerShape = ... # 0x4
        MarkerShapeTriangle      : QLegend.MarkerShape = ... # 0x5
        MarkerShapeStar          : QLegend.MarkerShape = ... # 0x6
        MarkerShapePentagon      : QLegend.MarkerShape = ... # 0x7


    def alignment(self) -> PySide6.QtCore.Qt.AlignmentFlag: ...
    def attachToChart(self) -> None: ...
    def borderColor(self) -> PySide6.QtGui.QColor: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def color(self) -> PySide6.QtGui.QColor: ...
    def detachFromChart(self) -> None: ...
    def font(self) -> PySide6.QtGui.QFont: ...
    def hideEvent(self, event: PySide6.QtGui.QHideEvent) -> None: ...
    def isAttachedToChart(self) -> bool: ...
    def isBackgroundVisible(self) -> bool: ...
    def isInteractive(self) -> bool: ...
    def labelBrush(self) -> PySide6.QtGui.QBrush: ...
    def labelColor(self) -> PySide6.QtGui.QColor: ...
    def markerShape(self) -> PySide6.QtCharts.QLegend.MarkerShape: ...
    def markers(self, series: Optional[PySide6.QtCharts.QAbstractSeries] = ...) -> List[PySide6.QtCharts.QLegendMarker]: ...
    def paint(self, painter: PySide6.QtGui.QPainter, option: PySide6.QtWidgets.QStyleOptionGraphicsItem, widget: Optional[PySide6.QtWidgets.QWidget] = ...) -> None: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def reverseMarkers(self) -> bool: ...
    def setAlignment(self, alignment: PySide6.QtCore.Qt.AlignmentFlag) -> None: ...
    def setBackgroundVisible(self, visible: bool = ...) -> None: ...
    def setBorderColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setInteractive(self, interactive: bool) -> None: ...
    def setLabelBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setLabelColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setMarkerShape(self, shape: PySide6.QtCharts.QLegend.MarkerShape) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setReverseMarkers(self, reverseMarkers: bool = ...) -> None: ...
    def setShowToolTips(self, show: bool) -> None: ...
    def showEvent(self, event: PySide6.QtGui.QShowEvent) -> None: ...
    def showToolTips(self) -> bool: ...


class QLegendMarker(PySide6.QtCore.QObject):

    brushChanged             : ClassVar[Signal] = ... # brushChanged()
    clicked                  : ClassVar[Signal] = ... # clicked()
    fontChanged              : ClassVar[Signal] = ... # fontChanged()
    hovered                  : ClassVar[Signal] = ... # hovered(bool)
    labelBrushChanged        : ClassVar[Signal] = ... # labelBrushChanged()
    labelChanged             : ClassVar[Signal] = ... # labelChanged()
    penChanged               : ClassVar[Signal] = ... # penChanged()
    shapeChanged             : ClassVar[Signal] = ... # shapeChanged()
    visibleChanged           : ClassVar[Signal] = ... # visibleChanged()

    class LegendMarkerType(enum.Enum):

        LegendMarkerTypeArea     : QLegendMarker.LegendMarkerType = ... # 0x0
        LegendMarkerTypeBar      : QLegendMarker.LegendMarkerType = ... # 0x1
        LegendMarkerTypePie      : QLegendMarker.LegendMarkerType = ... # 0x2
        LegendMarkerTypeXY       : QLegendMarker.LegendMarkerType = ... # 0x3
        LegendMarkerTypeBoxPlot  : QLegendMarker.LegendMarkerType = ... # 0x4
        LegendMarkerTypeCandlestick: QLegendMarker.LegendMarkerType = ... # 0x5


    def brush(self) -> PySide6.QtGui.QBrush: ...
    def font(self) -> PySide6.QtGui.QFont: ...
    def isVisible(self) -> bool: ...
    def label(self) -> str: ...
    def labelBrush(self) -> PySide6.QtGui.QBrush: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def series(self) -> PySide6.QtCharts.QAbstractSeries: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setLabel(self, label: str) -> None: ...
    def setLabelBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setShape(self, shape: PySide6.QtCharts.QLegend.MarkerShape) -> None: ...
    def setVisible(self, visible: bool) -> None: ...
    def shape(self) -> PySide6.QtCharts.QLegend.MarkerShape: ...
    def type(self) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QLineSeries(PySide6.QtCharts.QXYSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QLogValueAxis(PySide6.QtCharts.QAbstractAxis):

    baseChanged              : ClassVar[Signal] = ... # baseChanged(double)
    labelFormatChanged       : ClassVar[Signal] = ... # labelFormatChanged(QString)
    maxChanged               : ClassVar[Signal] = ... # maxChanged(double)
    minChanged               : ClassVar[Signal] = ... # minChanged(double)
    minorTickCountChanged    : ClassVar[Signal] = ... # minorTickCountChanged(int)
    rangeChanged             : ClassVar[Signal] = ... # rangeChanged(double,double)
    tickCountChanged         : ClassVar[Signal] = ... # tickCountChanged(int)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def base(self) -> float: ...
    def labelFormat(self) -> str: ...
    def max(self) -> float: ...
    def min(self) -> float: ...
    def minorTickCount(self) -> int: ...
    def setBase(self, base: float) -> None: ...
    def setLabelFormat(self, format: str) -> None: ...
    def setMax(self, max: float) -> None: ...
    def setMin(self, min: float) -> None: ...
    def setMinorTickCount(self, minorTickCount: int) -> None: ...
    def setRange(self, min: float, max: float) -> None: ...
    def tickCount(self) -> int: ...
    def type(self) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QPercentBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QPieLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QPieSeries, slice: PySide6.QtCharts.QPieSlice, legend: PySide6.QtCharts.QLegend, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def series(self) -> PySide6.QtCharts.QPieSeries: ...
    def slice(self) -> PySide6.QtCharts.QPieSlice: ...
    def type(self) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QPieModelMapper(PySide6.QtCore.QObject):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def count(self) -> int: ...
    def first(self) -> int: ...
    def labelsSection(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self) -> PySide6.QtCharts.QPieSeries: ...
    def setCount(self, count: int) -> None: ...
    def setFirst(self, first: int) -> None: ...
    def setLabelsSection(self, labelsSection: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QPieSeries) -> None: ...
    def setValuesSection(self, valuesSection: int) -> None: ...
    def valuesSection(self) -> int: ...


class QPieSeries(PySide6.QtCharts.QAbstractSeries):

    added                    : ClassVar[Signal] = ... # added(QList<QPieSlice*>)
    clicked                  : ClassVar[Signal] = ... # clicked(QPieSlice*)
    countChanged             : ClassVar[Signal] = ... # countChanged()
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked(QPieSlice*)
    hovered                  : ClassVar[Signal] = ... # hovered(QPieSlice*,bool)
    pressed                  : ClassVar[Signal] = ... # pressed(QPieSlice*)
    released                 : ClassVar[Signal] = ... # released(QPieSlice*)
    removed                  : ClassVar[Signal] = ... # removed(QList<QPieSlice*>)
    sumChanged               : ClassVar[Signal] = ... # sumChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def __lshift__(self, slice: PySide6.QtCharts.QPieSlice) -> PySide6.QtCharts.QPieSeries: ...
    @overload
    def append(self, label: str, value: float) -> PySide6.QtCharts.QPieSlice: ...
    @overload
    def append(self, slice: PySide6.QtCharts.QPieSlice) -> bool: ...
    @overload
    def append(self, slices: Sequence[PySide6.QtCharts.QPieSlice]) -> bool: ...
    def clear(self) -> None: ...
    def count(self) -> int: ...
    def holeSize(self) -> float: ...
    def horizontalPosition(self) -> float: ...
    def insert(self, index: int, slice: PySide6.QtCharts.QPieSlice) -> bool: ...
    def isEmpty(self) -> bool: ...
    def pieEndAngle(self) -> float: ...
    def pieSize(self) -> float: ...
    def pieStartAngle(self) -> float: ...
    def remove(self, slice: PySide6.QtCharts.QPieSlice) -> bool: ...
    def setHoleSize(self, holeSize: float) -> None: ...
    def setHorizontalPosition(self, relativePosition: float) -> None: ...
    def setLabelsPosition(self, position: PySide6.QtCharts.QPieSlice.LabelPosition) -> None: ...
    def setLabelsVisible(self, visible: bool = ...) -> None: ...
    def setPieEndAngle(self, endAngle: float) -> None: ...
    def setPieSize(self, relativeSize: float) -> None: ...
    def setPieStartAngle(self, startAngle: float) -> None: ...
    def setVerticalPosition(self, relativePosition: float) -> None: ...
    def slices(self) -> List[PySide6.QtCharts.QPieSlice]: ...
    def sum(self) -> float: ...
    def take(self, slice: PySide6.QtCharts.QPieSlice) -> bool: ...
    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...
    def verticalPosition(self) -> float: ...


class QPieSlice(PySide6.QtCore.QObject):

    angleSpanChanged         : ClassVar[Signal] = ... # angleSpanChanged()
    borderColorChanged       : ClassVar[Signal] = ... # borderColorChanged()
    borderWidthChanged       : ClassVar[Signal] = ... # borderWidthChanged()
    brushChanged             : ClassVar[Signal] = ... # brushChanged()
    clicked                  : ClassVar[Signal] = ... # clicked()
    colorChanged             : ClassVar[Signal] = ... # colorChanged()
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked()
    hovered                  : ClassVar[Signal] = ... # hovered(bool)
    labelBrushChanged        : ClassVar[Signal] = ... # labelBrushChanged()
    labelChanged             : ClassVar[Signal] = ... # labelChanged()
    labelColorChanged        : ClassVar[Signal] = ... # labelColorChanged()
    labelFontChanged         : ClassVar[Signal] = ... # labelFontChanged()
    labelVisibleChanged      : ClassVar[Signal] = ... # labelVisibleChanged()
    penChanged               : ClassVar[Signal] = ... # penChanged()
    percentageChanged        : ClassVar[Signal] = ... # percentageChanged()
    pressed                  : ClassVar[Signal] = ... # pressed()
    released                 : ClassVar[Signal] = ... # released()
    startAngleChanged        : ClassVar[Signal] = ... # startAngleChanged()
    valueChanged             : ClassVar[Signal] = ... # valueChanged()

    class LabelPosition(enum.Enum):

        LabelOutside             : QPieSlice.LabelPosition = ... # 0x0
        LabelInsideHorizontal    : QPieSlice.LabelPosition = ... # 0x1
        LabelInsideTangential    : QPieSlice.LabelPosition = ... # 0x2
        LabelInsideNormal        : QPieSlice.LabelPosition = ... # 0x3


    @overload
    def __init__(self, label: str, value: float, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def angleSpan(self) -> float: ...
    def borderColor(self) -> PySide6.QtGui.QColor: ...
    def borderWidth(self) -> int: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def color(self) -> PySide6.QtGui.QColor: ...
    def explodeDistanceFactor(self) -> float: ...
    def isExploded(self) -> bool: ...
    def isLabelVisible(self) -> bool: ...
    def label(self) -> str: ...
    def labelArmLengthFactor(self) -> float: ...
    def labelBrush(self) -> PySide6.QtGui.QBrush: ...
    def labelColor(self) -> PySide6.QtGui.QColor: ...
    def labelFont(self) -> PySide6.QtGui.QFont: ...
    def labelPosition(self) -> PySide6.QtCharts.QPieSlice.LabelPosition: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def percentage(self) -> float: ...
    def series(self) -> PySide6.QtCharts.QPieSeries: ...
    def setBorderColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBorderWidth(self, width: int) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setExplodeDistanceFactor(self, factor: float) -> None: ...
    def setExploded(self, exploded: bool = ...) -> None: ...
    def setLabel(self, label: str) -> None: ...
    def setLabelArmLengthFactor(self, factor: float) -> None: ...
    def setLabelBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setLabelColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLabelFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setLabelPosition(self, position: PySide6.QtCharts.QPieSlice.LabelPosition) -> None: ...
    def setLabelVisible(self, visible: bool = ...) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setValue(self, value: float) -> None: ...
    def startAngle(self) -> float: ...
    def value(self) -> float: ...


class QPointFList(object): ...


class QPolarChart(PySide6.QtCharts.QChart):

    class PolarOrientation(enum.Flag):

        PolarOrientationRadial   : QPolarChart.PolarOrientation = ... # 0x1
        PolarOrientationAngular  : QPolarChart.PolarOrientation = ... # 0x2


    def __init__(self, parent: Optional[PySide6.QtWidgets.QGraphicsItem] = ..., wFlags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtCharts.QAbstractAxis, polarOrientation: PySide6.QtCharts.QPolarChart.PolarOrientation) -> None: ...
    @staticmethod
    def axisPolarOrientation(axis: PySide6.QtCharts.QAbstractAxis) -> PySide6.QtCharts.QPolarChart.PolarOrientation: ...


class QScatterSeries(PySide6.QtCharts.QXYSeries):

    borderColorChanged       : ClassVar[Signal] = ... # borderColorChanged(QColor)
    colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
    markerShapeChanged       : ClassVar[Signal] = ... # markerShapeChanged(MarkerShape)
    markerSizeChanged        : ClassVar[Signal] = ... # markerSizeChanged(double)

    class MarkerShape(enum.Enum):

        MarkerShapeCircle        : QScatterSeries.MarkerShape = ... # 0x0
        MarkerShapeRectangle     : QScatterSeries.MarkerShape = ... # 0x1
        MarkerShapeRotatedRectangle: QScatterSeries.MarkerShape = ... # 0x2
        MarkerShapeTriangle      : QScatterSeries.MarkerShape = ... # 0x3
        MarkerShapeStar          : QScatterSeries.MarkerShape = ... # 0x4
        MarkerShapePentagon      : QScatterSeries.MarkerShape = ... # 0x5


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def borderColor(self) -> PySide6.QtGui.QColor: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def color(self) -> PySide6.QtGui.QColor: ...
    def markerShape(self) -> PySide6.QtCharts.QScatterSeries.MarkerShape: ...
    def markerSize(self) -> float: ...
    def setBorderColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setMarkerShape(self, shape: PySide6.QtCharts.QScatterSeries.MarkerShape) -> None: ...
    def setMarkerSize(self, size: float) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QSplineSeries(PySide6.QtCharts.QLineSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QStackedBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def type(self) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QVBarModelMapper(PySide6.QtCharts.QBarModelMapper):

    firstBarSetColumnChanged : ClassVar[Signal] = ... # firstBarSetColumnChanged()
    firstRowChanged          : ClassVar[Signal] = ... # firstRowChanged()
    lastBarSetColumnChanged  : ClassVar[Signal] = ... # lastBarSetColumnChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def firstBarSetColumn(self) -> int: ...
    def firstRow(self) -> int: ...
    def lastBarSetColumn(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self) -> int: ...
    def series(self) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def setFirstBarSetColumn(self, firstBarSetColumn: int) -> None: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def setLastBarSetColumn(self, lastBarSetColumn: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setRowCount(self, rowCount: int) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QAbstractBarSeries) -> None: ...


class QVBoxPlotModelMapper(PySide6.QtCharts.QBoxPlotModelMapper):

    firstBoxSetColumnChanged : ClassVar[Signal] = ... # firstBoxSetColumnChanged()
    firstRowChanged          : ClassVar[Signal] = ... # firstRowChanged()
    lastBoxSetColumnChanged  : ClassVar[Signal] = ... # lastBoxSetColumnChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def firstBoxSetColumn(self) -> int: ...
    def firstRow(self) -> int: ...
    def lastBoxSetColumn(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self) -> int: ...
    def series(self) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def setFirstBoxSetColumn(self, firstBoxSetColumn: int) -> None: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def setLastBoxSetColumn(self, lastBoxSetColumn: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setRowCount(self, rowCount: int) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QBoxPlotSeries) -> None: ...


class QVCandlestickModelMapper(PySide6.QtCharts.QCandlestickModelMapper):

    closeRowChanged          : ClassVar[Signal] = ... # closeRowChanged()
    firstSetColumnChanged    : ClassVar[Signal] = ... # firstSetColumnChanged()
    highRowChanged           : ClassVar[Signal] = ... # highRowChanged()
    lastSetColumnChanged     : ClassVar[Signal] = ... # lastSetColumnChanged()
    lowRowChanged            : ClassVar[Signal] = ... # lowRowChanged()
    openRowChanged           : ClassVar[Signal] = ... # openRowChanged()
    timestampRowChanged      : ClassVar[Signal] = ... # timestampRowChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def closeRow(self) -> int: ...
    def firstSetColumn(self) -> int: ...
    def highRow(self) -> int: ...
    def lastSetColumn(self) -> int: ...
    def lowRow(self) -> int: ...
    def openRow(self) -> int: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def setCloseRow(self, closeRow: int) -> None: ...
    def setFirstSetColumn(self, firstSetColumn: int) -> None: ...
    def setHighRow(self, highRow: int) -> None: ...
    def setLastSetColumn(self, lastSetColumn: int) -> None: ...
    def setLowRow(self, lowRow: int) -> None: ...
    def setOpenRow(self, openRow: int) -> None: ...
    def setTimestampRow(self, timestampRow: int) -> None: ...
    def timestampRow(self) -> int: ...


class QVPieModelMapper(PySide6.QtCharts.QPieModelMapper):

    firstRowChanged          : ClassVar[Signal] = ... # firstRowChanged()
    labelsColumnChanged      : ClassVar[Signal] = ... # labelsColumnChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()
    valuesColumnChanged      : ClassVar[Signal] = ... # valuesColumnChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def firstRow(self) -> int: ...
    def labelsColumn(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self) -> int: ...
    def series(self) -> PySide6.QtCharts.QPieSeries: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def setLabelsColumn(self, labelsColumn: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setRowCount(self, rowCount: int) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QPieSeries) -> None: ...
    def setValuesColumn(self, valuesColumn: int) -> None: ...
    def valuesColumn(self) -> int: ...


class QVXYModelMapper(PySide6.QtCharts.QXYModelMapper):

    firstRowChanged          : ClassVar[Signal] = ... # firstRowChanged()
    modelReplaced            : ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : ClassVar[Signal] = ... # seriesReplaced()
    xColumnChanged           : ClassVar[Signal] = ... # xColumnChanged()
    yColumnChanged           : ClassVar[Signal] = ... # yColumnChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def firstRow(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self) -> int: ...
    def series(self) -> PySide6.QtCharts.QXYSeries: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setRowCount(self, rowCount: int) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QXYSeries) -> None: ...
    def setXColumn(self, xColumn: int) -> None: ...
    def setYColumn(self, yColumn: int) -> None: ...
    def xColumn(self) -> int: ...
    def yColumn(self) -> int: ...


class QValueAxis(PySide6.QtCharts.QAbstractAxis):

    labelFormatChanged       : ClassVar[Signal] = ... # labelFormatChanged(QString)
    maxChanged               : ClassVar[Signal] = ... # maxChanged(double)
    minChanged               : ClassVar[Signal] = ... # minChanged(double)
    minorTickCountChanged    : ClassVar[Signal] = ... # minorTickCountChanged(int)
    rangeChanged             : ClassVar[Signal] = ... # rangeChanged(double,double)
    tickAnchorChanged        : ClassVar[Signal] = ... # tickAnchorChanged(double)
    tickCountChanged         : ClassVar[Signal] = ... # tickCountChanged(int)
    tickIntervalChanged      : ClassVar[Signal] = ... # tickIntervalChanged(double)
    tickTypeChanged          : ClassVar[Signal] = ... # tickTypeChanged(QValueAxis::TickType)

    class TickType(enum.Enum):

        TicksDynamic             : QValueAxis.TickType = ... # 0x0
        TicksFixed               : QValueAxis.TickType = ... # 0x1


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def applyNiceNumbers(self) -> None: ...
    def labelFormat(self) -> str: ...
    def max(self) -> float: ...
    def min(self) -> float: ...
    def minorTickCount(self) -> int: ...
    def setLabelFormat(self, format: str) -> None: ...
    def setMax(self, max: float) -> None: ...
    def setMin(self, min: float) -> None: ...
    def setMinorTickCount(self, count: int) -> None: ...
    def setRange(self, min: float, max: float) -> None: ...
    def setTickAnchor(self, anchor: float) -> None: ...
    def setTickCount(self, count: int) -> None: ...
    def setTickInterval(self, insterval: float) -> None: ...
    def setTickType(self, type: PySide6.QtCharts.QValueAxis.TickType) -> None: ...
    def tickAnchor(self) -> float: ...
    def tickCount(self) -> int: ...
    def tickInterval(self) -> float: ...
    def tickType(self) -> PySide6.QtCharts.QValueAxis.TickType: ...
    def type(self) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QXYLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QXYSeries, legend: PySide6.QtCharts.QLegend, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def series(self) -> PySide6.QtCharts.QXYSeries: ...
    def type(self) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QXYModelMapper(PySide6.QtCore.QObject):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def count(self) -> int: ...
    def first(self) -> int: ...
    def model(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self) -> PySide6.QtCharts.QXYSeries: ...
    def setCount(self, count: int) -> None: ...
    def setFirst(self, first: int) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QXYSeries) -> None: ...
    def setXSection(self, xSection: int) -> None: ...
    def setYSection(self, ySection: int) -> None: ...
    def xSection(self) -> int: ...
    def ySection(self) -> int: ...


class QXYSeries(PySide6.QtCharts.QAbstractSeries):

    bestFitLineColorChanged  : ClassVar[Signal] = ... # bestFitLineColorChanged(QColor)
    bestFitLinePenChanged    : ClassVar[Signal] = ... # bestFitLinePenChanged(QPen)
    bestFitLineVisibilityChanged: ClassVar[Signal] = ... # bestFitLineVisibilityChanged(bool)
    clicked                  : ClassVar[Signal] = ... # clicked(QPointF)
    colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
    doubleClicked            : ClassVar[Signal] = ... # doubleClicked(QPointF)
    hovered                  : ClassVar[Signal] = ... # hovered(QPointF,bool)
    lightMarkerChanged       : ClassVar[Signal] = ... # lightMarkerChanged(QImage)
    markerSizeChanged        : ClassVar[Signal] = ... # markerSizeChanged(double)
    penChanged               : ClassVar[Signal] = ... # penChanged(QPen)
    pointAdded               : ClassVar[Signal] = ... # pointAdded(int)
    pointLabelsClippingChanged: ClassVar[Signal] = ... # pointLabelsClippingChanged(bool)
    pointLabelsColorChanged  : ClassVar[Signal] = ... # pointLabelsColorChanged(QColor)
    pointLabelsFontChanged   : ClassVar[Signal] = ... # pointLabelsFontChanged(QFont)
    pointLabelsFormatChanged : ClassVar[Signal] = ... # pointLabelsFormatChanged(QString)
    pointLabelsVisibilityChanged: ClassVar[Signal] = ... # pointLabelsVisibilityChanged(bool)
    pointRemoved             : ClassVar[Signal] = ... # pointRemoved(int)
    pointReplaced            : ClassVar[Signal] = ... # pointReplaced(int)
    pointsConfigurationChanged: ClassVar[Signal] = ... # pointsConfigurationChanged(QHash<int,QHash<PointConfiguration,QVariant>>)
    pointsRemoved            : ClassVar[Signal] = ... # pointsRemoved(int,int)
    pointsReplaced           : ClassVar[Signal] = ... # pointsReplaced()
    pressed                  : ClassVar[Signal] = ... # pressed(QPointF)
    released                 : ClassVar[Signal] = ... # released(QPointF)
    selectedColorChanged     : ClassVar[Signal] = ... # selectedColorChanged(QColor)
    selectedLightMarkerChanged: ClassVar[Signal] = ... # selectedLightMarkerChanged(QImage)
    selectedPointsChanged    : ClassVar[Signal] = ... # selectedPointsChanged()

    class PointConfiguration(enum.Enum):

        Color                    : QXYSeries.PointConfiguration = ... # 0x0
        Size                     : QXYSeries.PointConfiguration = ... # 0x1
        Visibility               : QXYSeries.PointConfiguration = ... # 0x2
        LabelVisibility          : QXYSeries.PointConfiguration = ... # 0x3
        LabelFormat              : QXYSeries.PointConfiguration = ... # 0x4


    @overload
    def __lshift__(self, point: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element]) -> PySide6.QtCharts.QXYSeries: ...
    @overload
    def __lshift__(self, points: Sequence[PySide6.QtCore.QPointF]) -> PySide6.QtCharts.QXYSeries: ...
    @overload
    def append(self, point: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element]) -> None: ...
    @overload
    def append(self, points: Sequence[PySide6.QtCore.QPointF]) -> None: ...
    @overload
    def append(self, x: float, y: float) -> None: ...
    def appendNp(self, x: shibokensupport.signature.mapping.ArrayLikeVariable, y: shibokensupport.signature.mapping.ArrayLikeVariable) -> None: ...
    def at(self, index: int) -> PySide6.QtCore.QPointF: ...
    def bestFitLineColor(self) -> PySide6.QtGui.QColor: ...
    def bestFitLineEquation(self, ok: bool) -> Tuple[float, float]: ...
    def bestFitLinePen(self) -> PySide6.QtGui.QPen: ...
    def bestFitLineVisible(self) -> bool: ...
    def brush(self) -> PySide6.QtGui.QBrush: ...
    def clear(self) -> None: ...
    @overload
    def clearPointConfiguration(self, index: int) -> None: ...
    @overload
    def clearPointConfiguration(self, index: int, key: PySide6.QtCharts.QXYSeries.PointConfiguration) -> None: ...
    @overload
    def clearPointsConfiguration(self) -> None: ...
    @overload
    def clearPointsConfiguration(self, key: PySide6.QtCharts.QXYSeries.PointConfiguration) -> None: ...
    def color(self) -> PySide6.QtGui.QColor: ...
    def colorBy(self, sourceData: Sequence[float], gradient: PySide6.QtGui.QLinearGradient = ...) -> None: ...
    def count(self) -> int: ...
    def deselectAllPoints(self) -> None: ...
    def deselectPoint(self, index: int) -> None: ...
    def deselectPoints(self, indexes: Sequence[int]) -> None: ...
    def insert(self, index: int, point: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element]) -> None: ...
    def isPointSelected(self, index: int) -> bool: ...
    def lightMarker(self) -> PySide6.QtGui.QImage: ...
    def markerSize(self) -> float: ...
    def pen(self) -> PySide6.QtGui.QPen: ...
    def pointConfiguration(self, index: int) -> Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, Any]: ...
    def pointLabelsClipping(self) -> bool: ...
    def pointLabelsColor(self) -> PySide6.QtGui.QColor: ...
    def pointLabelsFont(self) -> PySide6.QtGui.QFont: ...
    def pointLabelsFormat(self) -> str: ...
    def pointLabelsVisible(self) -> bool: ...
    def points(self) -> List[PySide6.QtCore.QPointF]: ...
    def pointsConfiguration(self) -> Dict[int, Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, Any]]: ...
    def pointsVector(self) -> List[PySide6.QtCore.QPointF]: ...
    def pointsVisible(self) -> bool: ...
    @overload
    def remove(self, index: int) -> None: ...
    @overload
    def remove(self, point: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element]) -> None: ...
    @overload
    def remove(self, x: float, y: float) -> None: ...
    def removePoints(self, index: int, count: int) -> None: ...
    @overload
    def replace(self, index: int, newPoint: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element]) -> None: ...
    @overload
    def replace(self, index: int, newX: float, newY: float) -> None: ...
    @overload
    def replace(self, oldPoint: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], newPoint: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element]) -> None: ...
    @overload
    def replace(self, oldX: float, oldY: float, newX: float, newY: float) -> None: ...
    @overload
    def replace(self, points: Sequence[PySide6.QtCore.QPointF]) -> None: ...
    def replaceNp(self, x: shibokensupport.signature.mapping.ArrayLikeVariable, y: shibokensupport.signature.mapping.ArrayLikeVariable) -> None: ...
    def selectAllPoints(self) -> None: ...
    def selectPoint(self, index: int) -> None: ...
    def selectPoints(self, indexes: Sequence[int]) -> None: ...
    def selectedColor(self) -> PySide6.QtGui.QColor: ...
    def selectedLightMarker(self) -> PySide6.QtGui.QImage: ...
    def selectedPoints(self) -> List[int]: ...
    def setBestFitLineColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBestFitLinePen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    def setBestFitLineVisible(self, visible: bool = ...) -> None: ...
    def setBrush(self, brush: Union[PySide6.QtGui.QBrush, PySide6.QtCore.Qt.BrushStyle, PySide6.QtCore.Qt.GlobalColor, PySide6.QtGui.QColor, PySide6.QtGui.QGradient, PySide6.QtGui.QImage, PySide6.QtGui.QPixmap]) -> None: ...
    def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLightMarker(self, lightMarker: Union[PySide6.QtGui.QImage, str]) -> None: ...
    def setMarkerSize(self, size: float) -> None: ...
    def setPen(self, pen: Union[PySide6.QtGui.QPen, PySide6.QtCore.Qt.PenStyle, PySide6.QtGui.QColor]) -> None: ...
    @overload
    def setPointConfiguration(self, index: int, configuration: Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, Any]) -> None: ...
    @overload
    def setPointConfiguration(self, index: int, key: PySide6.QtCharts.QXYSeries.PointConfiguration, value: Any) -> None: ...
    def setPointLabelsClipping(self, enabled: bool = ...) -> None: ...
    def setPointLabelsColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setPointLabelsFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setPointLabelsFormat(self, format: str) -> None: ...
    def setPointLabelsVisible(self, visible: bool = ...) -> None: ...
    def setPointSelected(self, index: int, selected: bool) -> None: ...
    def setPointsConfiguration(self, pointsConfiguration: Dict[int, Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, Any]]) -> None: ...
    def setPointsVisible(self, visible: bool = ...) -> None: ...
    def setSelectedColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setSelectedLightMarker(self, selectedLightMarker: Union[PySide6.QtGui.QImage, str]) -> None: ...
    def sizeBy(self, sourceData: Sequence[float], minSize: float, maxSize: float) -> None: ...
    def toggleSelection(self, indexes: Sequence[int]) -> None: ...


# eof
