[{"classes": [{"className": "QBluetoothDeviceDiscoveryAgentPrivate", "object": true, "qualifiedClassName": "QBluetoothDeviceDiscoveryAgentPrivate", "slots": [{"access": "private", "arguments": [{"name": "info", "type": "QBluetoothDeviceInfo"}], "name": "registerDevice", "returnType": "void"}, {"access": "private", "arguments": [{"name": "address", "type": "QBluetoothAddress"}, {"name": "fields", "type": "QBluetoothDeviceInfo::Fields"}, {"name": "rssi", "type": "qint16"}, {"name": "manufacturerData", "type": "ManufacturerData"}, {"name": "serviceData", "type": "ServiceData"}], "name": "updateDeviceData", "returnType": "void"}, {"access": "private", "arguments": [{"name": "e", "type": "QBluetoothDeviceDiscoveryAgent::Error"}], "name": "onErrorOccured", "returnType": "void"}, {"access": "private", "name": "onScanFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothdevicediscoveryagent_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothDeviceWatcherWinRT", "object": true, "qualifiedClassName": "QBluetoothDeviceWatcherWinRT", "signals": [{"access": "public", "arguments": [{"name": "deviceId", "type": "winrt::hstring"}, {"name": "id", "type": "int"}], "name": "deviceAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "deviceId", "type": "winrt::hstring"}, {"name": "id", "type": "int"}], "name": "deviceRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "deviceId", "type": "winrt::hstring"}, {"name": "id", "type": "int"}], "name": "deviceUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}], "name": "enumerationCompleted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}], "name": "watcherStopped", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "std::enable_shared_from_this<QBluetoothDeviceWatcherWinRT>"}]}], "inputFile": "qbluetoothdevicewatcher_winrt_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothLocalDevicePrivate", "object": true, "qualifiedClassName": "QBluetoothLocalDevicePrivate", "signals": [{"access": "public", "arguments": [{"name": "id", "type": "winrt::hstring"}, {"name": "mode", "type": "QBluetoothLocalDevice::HostMode"}], "name": "updateMode", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "onAdapterRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "onAdapterAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "winrt::hstring"}, {"name": "mode", "type": "QBluetoothLocalDevice::HostMode"}], "name": "radioModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "name": "onDeviceAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "name": "onDeviceRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothlocaldevice_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothServiceDiscoveryAgentPrivate", "object": true, "qualifiedClassName": "QBluetoothServiceDiscoveryAgentPrivate", "slots": [{"access": "private", "arguments": [{"name": "deviceAddress", "type": "quint64"}, {"name": "info", "type": "QBluetoothServiceInfo"}], "name": "processFoundService", "returnType": "void"}, {"access": "private", "arguments": [{"name": "deviceAddress", "type": "quint64"}], "name": "onScanFinished", "returnType": "void"}, {"access": "private", "name": "onError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothservicediscoveryagent_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothServiceInfoPrivate", "object": true, "qualifiedClassName": "QBluetoothServiceInfoPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothserviceinfo_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothSocketPrivateWinRT", "methods": [{"access": "public", "arguments": [{"name": "data", "type": "QList<QByteArray>"}], "name": "addToPendingData", "returnType": "void"}], "object": true, "qualifiedClassName": "QBluetoothSocketPrivateWinRT", "slots": [{"access": "private", "arguments": [{"name": "data", "type": "QList<QByteArray>"}], "name": "handleNewData", "returnType": "void"}, {"access": "private", "arguments": [{"name": "error", "type": "QBluetoothSocket::SocketError"}], "name": "handleError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBluetoothSocketBasePrivate"}]}], "inputFile": "qbluetoothsocket_winrt_p.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyControllerPrivateWinRT", "object": true, "qualifiedClassName": "QLowEnergyControllerPrivateWinRT", "signals": [{"access": "public", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "quint16"}, {"name": "data", "type": "QByteArray"}], "name": "characteristicChanged", "returnType": "void"}, {"access": "public", "name": "abortConnection", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "quint16"}, {"name": "data", "type": "QByteArray"}], "name": "handleCharacteristicChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "error", "type": "QString"}], "name": "handleServiceHandlerError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLowEnergyControllerPrivate"}]}], "inputFile": "qlowenergycontroller_winrt_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetooth", "enums": [{"isClass": true, "isFlag": false, "name": "Security", "values": ["NoSecurity", "Authorization", "Authentication", "Encryption", "Secure"]}, {"isClass": true, "isFlag": false, "name": "AttAccessConstraint", "values": ["AttAuthorizationRequired", "AttAuthenticationRequired", "AttEncryptionRequired"]}], "namespace": true, "qualifiedClassName": "QBluetooth"}], "inputFile": "qbluetooth.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothDeviceDiscoveryAgent", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InputOutputError", "PoweredOffError", "InvalidBluetoothAdapterError", "UnsupportedPlatformError", "UnsupportedDiscoveryMethod", "LocationServiceTurnedOffError", "MissingPermissionsError", "UnknownE<PERSON>r"]}, {"alias": "DiscoveryMethod", "isClass": false, "isFlag": true, "name": "DiscoveryMethods", "values": ["NoMethod", "ClassicMethod", "LowEnergyMethod"]}], "object": true, "qualifiedClassName": "QBluetoothDeviceDiscoveryAgent", "signals": [{"access": "public", "arguments": [{"name": "info", "type": "QBluetoothDeviceInfo"}], "name": "deviceDiscovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QBluetoothDeviceInfo"}, {"name": "<PERSON><PERSON><PERSON>s", "type": "QBluetoothDeviceInfo::Fields"}], "name": "deviceUpdated", "returnType": "void"}, {"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothDeviceDiscoveryAgent::Error"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "name": "canceled", "returnType": "void"}], "slots": [{"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "arguments": [{"name": "method", "type": "DiscoveryMethods"}], "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothdevicediscoveryagent.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothLocalDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Pairing", "values": ["Unpaired", "Paired", "AuthorizedPaired"]}, {"isClass": false, "isFlag": false, "name": "HostMode", "values": ["HostPoweredOff", "HostConnectable", "HostDiscoverable", "HostDiscoverableLimitedInquiry"]}, {"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "PairingError", "MissingPermissionsError", "UnknownE<PERSON>r"]}], "object": true, "qualifiedClassName": "QBluetoothLocalDevice", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QBluetoothLocalDevice::HostMode"}], "name": "hostModeStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "name": "deviceConnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "name": "deviceDisconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}, {"name": "pairing", "type": "QBluetoothLocalDevice::Pairing"}], "name": "pairingFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothLocalDevice::Error"}], "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothlocaldevice.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothServer", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "UnknownE<PERSON>r", "PoweredOffError", "InputOutputError", "ServiceAlreadyRegisteredError", "UnsupportedProtocolError", "MissingPermissionsError"]}], "object": true, "qualifiedClassName": "QBluetoothServer", "signals": [{"access": "public", "name": "newConnection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothServer::Error"}], "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothserver.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothServiceDiscoveryAgent", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InputOutputError", "PoweredOffError", "InvalidBluetoothAdapterError", "MissingPermissionsError", "UnknownE<PERSON>r"]}, {"isClass": false, "isFlag": false, "name": "DiscoveryMode", "values": ["MinimalDiscovery", "FullDiscovery"]}], "object": true, "qualifiedClassName": "QBluetoothServiceDiscoveryAgent", "signals": [{"access": "public", "arguments": [{"name": "info", "type": "QBluetoothServiceInfo"}], "name": "serviceDiscovered", "returnType": "void"}, {"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "name": "canceled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothServiceDiscoveryAgent::Error"}], "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "mode", "type": "DiscoveryMode"}], "name": "start", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "public", "name": "clear", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothservicediscoveryagent.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothSocket", "enums": [{"isClass": true, "isFlag": false, "name": "SocketState", "values": ["UnconnectedState", "ServiceLookupState", "ConnectingState", "ConnectedState", "BoundState", "ClosingState", "ListeningState"]}, {"isClass": true, "isFlag": false, "name": "SocketError", "values": ["NoSocketError", "UnknownSocketError", "RemoteHostClosedError", "HostNotFoundError", "ServiceNotFoundError", "NetworkError", "UnsupportedProtocolError", "OperationError", "MissingPermissionsError"]}], "object": true, "qualifiedClassName": "QBluetoothSocket", "signals": [{"access": "public", "name": "connected", "returnType": "void"}, {"access": "public", "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothSocket::SocketError"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QBluetoothSocket::SocketState"}], "name": "stateChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "service", "type": "QBluetoothServiceInfo"}], "name": "serviceDiscovered", "returnType": "void"}, {"access": "private", "name": "discoveryFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qbluetoothsocket.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothSocketBasePrivate", "object": true, "qualifiedClassName": "QBluetoothSocketBasePrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothsocketbase_p.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyController", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "UnknownE<PERSON>r", "UnknownRemoteDeviceError", "NetworkError", "InvalidBluetoothAdapterError", "ConnectionError", "AdvertisingError", "RemoteHostClosedError", "AuthorizationError", "MissingPermissionsError", "RssiReadError"]}, {"isClass": false, "isFlag": false, "name": "ControllerState", "values": ["UnconnectedState", "ConnectingState", "ConnectedState", "DiscoveringState", "DiscoveredState", "ClosingState", "AdvertisingState"]}, {"isClass": false, "isFlag": false, "name": "RemoteAddressType", "values": ["PublicAddress", "Random<PERSON>dd<PERSON>"]}, {"isClass": false, "isFlag": false, "name": "Role", "values": ["CentralRole", "Peripher<PERSON><PERSON><PERSON>"]}], "object": true, "qualifiedClassName": "QLowEnergyController", "signals": [{"access": "public", "name": "connected", "returnType": "void"}, {"access": "public", "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QLowEnergyController::ControllerState"}], "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newError", "type": "QLowEnergyController::<PERSON><PERSON><PERSON>"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mtu", "type": "int"}], "name": "mtuChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rssi", "type": "qint16"}], "name": "rssiRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newService", "type": "QBluetoothUuid"}], "name": "serviceDiscovered", "returnType": "void"}, {"access": "public", "name": "discoveryFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parameters", "type": "QLowEnergyConnectionParameters"}], "name": "connectionUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergycontroller.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyControllerPrivate", "object": true, "qualifiedClassName": "QLowEnergyControllerPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergycontrollerbase_p.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyService", "enums": [{"isClass": false, "isFlag": false, "name": "ServiceType", "values": ["PrimaryService", "IncludedService"]}, {"isClass": false, "isFlag": false, "name": "ServiceError", "values": ["NoError", "OperationError", "CharacteristicWriteError", "DescriptorWriteError", "UnknownE<PERSON>r", "CharacteristicReadError", "DescriptorReadError"]}, {"isClass": false, "isFlag": false, "name": "ServiceState", "values": ["InvalidService", "RemoteService", "RemoteServiceDiscovering", "RemoteServiceDiscovered", "LocalService", "DiscoveryRequired", "DiscoveringService", "ServiceDiscovered"]}, {"isClass": false, "isFlag": false, "name": "DiscoveryMode", "values": ["FullDiscovery", "SkipValueDiscovery"]}, {"isClass": false, "isFlag": false, "name": "WriteMode", "values": ["WriteWithResponse", "WriteWithoutResponse", "WriteSigned"]}], "object": true, "qualifiedClassName": "QLowEnergyService", "signals": [{"access": "public", "arguments": [{"name": "newState", "type": "QLowEnergyService::ServiceState"}], "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "name": "characteristicChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "name": "characteristicRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyDescriptor"}, {"name": "value", "type": "QByteArray"}], "name": "descriptorRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyDescriptor"}, {"name": "value", "type": "QByteArray"}], "name": "descriptor<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QLowEnergyService::ServiceError"}], "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergyservice.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyServicePrivate", "object": true, "qualifiedClassName": "QLowEnergyServicePrivate", "signals": [{"access": "public", "arguments": [{"name": "newState", "type": "QLowEnergyService::ServiceState"}], "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QLowEnergyService::ServiceError"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "characteristic", "type": "QLowEnergyCharacteristic"}, {"name": "newValue", "type": "QByteArray"}], "name": "characteristicChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "name": "characteristicRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "characteristic", "type": "QLowEnergyCharacteristic"}, {"name": "newValue", "type": "QByteArray"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyDescriptor"}, {"name": "value", "type": "QByteArray"}], "name": "descriptorRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "descriptor", "type": "QLowEnergyDescriptor"}, {"name": "newValue", "type": "QByteArray"}], "name": "descriptor<PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergyserviceprivate_p.h", "outputRevision": 68}, {"classes": [{"className": "AdvertisementWatcherWrapper", "object": true, "qualifiedClassName": "AdvertisementWatcherWrapper", "signals": [{"access": "public", "arguments": [{"name": "address", "type": "quint64"}, {"name": "rssi", "type": "qint16"}, {"name": "manufacturerData", "type": "ManufacturerData"}, {"name": "serviceData", "type": "ServiceData"}, {"name": "uuids", "type": "QList<QBluetoothUuid>"}], "name": "advertisementDataReceived", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "std::enable_shared_from_this<AdvertisementWatcherWrapper>"}]}, {"className": "QWinRTBluetoothDeviceDiscoveryWorker", "methods": [{"access": "private", "arguments": [{"name": "worker", "type": "std::shared_ptr<QWinRTBluetoothDeviceDiscoveryWorker>"}], "name": "decrementPendingDevicesCountAndCheckFinished", "returnType": "void"}], "object": true, "qualifiedClassName": "QWinRTBluetoothDeviceDiscoveryWorker", "signals": [{"access": "public", "arguments": [{"name": "info", "type": "QBluetoothDeviceInfo"}], "name": "deviceFound", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}, {"type": "QBluetoothDeviceInfo::Fields"}, {"name": "rssi", "type": "qint16"}, {"name": "manufacturerData", "type": "ManufacturerData"}, {"name": "serviceData", "type": "ServiceData"}], "name": "deviceDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothDeviceDiscoveryAgent::Error"}], "name": "errorOccured", "returnType": "void"}, {"access": "public", "name": "scanFinished", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "deviceId", "type": "winrt::hstring"}, {"name": "watcherId", "type": "int"}], "name": "onBluetoothDeviceFound", "returnType": "void"}, {"access": "private", "arguments": [{"name": "watcherId", "type": "int"}], "name": "onDeviceEnumerationCompleted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "address", "type": "quint64"}, {"name": "rssi", "type": "qint16"}, {"name": "manufacturerData", "type": "ManufacturerData"}, {"name": "serviceData", "type": "ServiceData"}, {"name": "uuids", "type": "QList<QBluetoothUuid>"}], "name": "onAdvertisementDataReceived", "returnType": "void"}, {"access": "private", "name": "stopAdvertisementWatcher", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "std::enable_shared_from_this<QWinRTBluetoothDeviceDiscoveryWorker>"}]}], "inputFile": "qbluetoothdevicediscoveryagent_winrt.cpp", "outputRevision": 68}, {"classes": [{"className": "AdapterManager", "object": true, "qualifiedClassName": "AdapterManager", "signals": [{"access": "public", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "adapterAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "adapterRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "winrt::hstring"}, {"name": "mode", "type": "QBluetoothLocalDevice::HostMode"}], "name": "modeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "name": "deviceAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "name": "deviceRemoved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "client", "type": "QBluetoothLocalDevicePrivate*"}], "name": "addClient", "returnType": "QBluetoothLocalDevice::HostMode"}, {"access": "public", "arguments": [{"name": "adapterId", "type": "winrt::hstring"}], "name": "removeClient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "adapterId", "type": "winrt::hstring"}, {"name": "mode", "type": "QBluetoothLocalDevice::HostMode"}], "name": "updateMode", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "onAdapterAdded", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "onAdapterRemoved", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "winrt::hstring"}, {"name": "numAttempts", "type": "int"}], "name": "tryResubscribeToStateChanges", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "onDeviceAdded", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "winrt::hstring"}], "name": "onDeviceRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothlocaldevice_winrt.cpp", "outputRevision": 68}, {"classes": [{"className": "QWinRTBluetoothServiceDiscoveryWorker", "object": true, "qualifiedClassName": "QWinRTBluetoothServiceDiscoveryWorker", "signals": [{"access": "public", "arguments": [{"name": "deviceAddress", "type": "quint64"}, {"name": "info", "type": "QBluetoothServiceInfo"}], "name": "serviceFound", "returnType": "void"}, {"access": "public", "arguments": [{"name": "deviceAddress", "type": "quint64"}], "name": "scanFinished", "returnType": "void"}, {"access": "public", "name": "errorOccured", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothservicediscoveryagent_winrt.cpp", "outputRevision": 68}, {"classes": [{"className": "SocketWorker", "object": true, "qualifiedClassName": "SocketWorker", "signals": [{"access": "public", "arguments": [{"name": "data", "type": "QList<QByteArray>"}], "name": "newDataReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothSocket::SocketError"}], "name": "socketErrorOccured", "returnType": "void"}], "slots": [{"access": "public", "name": "notifyAboutNewData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothsocket_winrt.cpp", "outputRevision": 68}, {"classes": [{"className": "QWinRTLowEnergyServiceHandler", "object": true, "qualifiedClassName": "QWinRTLowEnergyServiceHandler", "signals": [{"access": "public", "arguments": [{"name": "service", "type": "QBluetoothUuid"}, {"name": "charList", "type": "QHash<QLowEnergyHandle,QLowEnergyServicePrivate::CharData>"}, {"name": "indicateChars", "type": "QList<QBluetoothUuid>"}, {"name": "startHandle", "type": "QLowEnergyHandle"}, {"name": "endHandle", "type": "QLowEnergyHandle"}], "name": "charListObtained", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QString"}], "name": "errorOccured", "returnType": "void"}], "slots": [{"access": "public", "name": "obtainCharList", "returnType": "void"}, {"access": "public", "name": "setAbortRequested", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QWinRTLowEnergyConnectionHandler", "object": true, "qualifiedClassName": "QWinRTLowEnergyConnectionHandler", "signals": [{"access": "public", "arguments": [{"name": "device", "type": "ComPtr<IBluetoothLEDevice>"}, {"name": "session", "type": "ComPtr<IGattSession>"}], "name": "deviceConnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "name": "connectToDevice", "returnType": "void"}, {"access": "public", "name": "handleDeviceDisconnectRequest", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergycontroller_winrt.cpp", "outputRevision": 68}]