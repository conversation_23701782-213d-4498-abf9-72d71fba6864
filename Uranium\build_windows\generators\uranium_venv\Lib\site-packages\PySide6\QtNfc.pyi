# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtNfc, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtNfc`

import PySide6.QtNfc
import PySide6.QtCore

import enum
from typing import ClassVar, List, Optional, Sequence, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QIntList(object): ...


class QNdefFilter(Shiboken.Object):

    class Record(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, Record: PySide6.QtNfc.QNdefFilter.Record) -> None: ...

        @staticmethod
        def __copy__() -> None: ...


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefFilter) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    @overload
    def appendRecord(self, record: PySide6.QtNfc.QNdefFilter.Record) -> bool: ...
    @overload
    def appendRecord(self, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, type: Union[PySide6.QtCore.QByteArray, bytes], min: int = ..., max: int = ...) -> bool: ...
    def clear(self) -> None: ...
    def match(self, message: Union[PySide6.QtNfc.QNdefMessage, Sequence[PySide6.QtNfc.QNdefRecord]]) -> bool: ...
    def orderMatch(self) -> bool: ...
    def recordAt(self, i: int) -> PySide6.QtNfc.QNdefFilter.Record: ...
    def recordCount(self) -> int: ...
    def setOrderMatch(self, on: bool) -> None: ...


class QNdefMessage(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, message: Union[PySide6.QtNfc.QNdefMessage, Sequence[PySide6.QtNfc.QNdefRecord]]) -> None: ...
    @overload
    def __init__(self, record: PySide6.QtNfc.QNdefRecord) -> None: ...
    @overload
    def __init__(self, records: Sequence[PySide6.QtNfc.QNdefRecord]) -> None: ...

    def __add__(self, l: Sequence[PySide6.QtNfc.QNdefRecord]) -> List[PySide6.QtNfc.QNdefRecord]: ...
    @staticmethod
    def __copy__() -> None: ...
    def __iadd__(self, l: Sequence[PySide6.QtNfc.QNdefRecord]) -> List[PySide6.QtNfc.QNdefRecord]: ...
    def __lshift__(self, l: Sequence[PySide6.QtNfc.QNdefRecord]) -> List[PySide6.QtNfc.QNdefRecord]: ...
    @overload
    def append(self, arg__1: PySide6.QtNfc.QNdefRecord) -> None: ...
    @overload
    def append(self, l: Sequence[PySide6.QtNfc.QNdefRecord]) -> None: ...
    def at(self, i: int) -> PySide6.QtNfc.QNdefRecord: ...
    def back(self) -> PySide6.QtNfc.QNdefRecord: ...
    def capacity(self) -> int: ...
    def clear(self) -> None: ...
    def constData(self) -> PySide6.QtNfc.QNdefRecord: ...
    def constFirst(self) -> PySide6.QtNfc.QNdefRecord: ...
    def constLast(self) -> PySide6.QtNfc.QNdefRecord: ...
    def count(self) -> int: ...
    def data(self) -> PySide6.QtNfc.QNdefRecord: ...
    def empty(self) -> bool: ...
    @overload
    def first(self) -> PySide6.QtNfc.QNdefRecord: ...
    @overload
    def first(self, n: int) -> List[PySide6.QtNfc.QNdefRecord]: ...
    @staticmethod
    def fromByteArray(message: Union[PySide6.QtCore.QByteArray, bytes]) -> PySide6.QtNfc.QNdefMessage: ...
    @staticmethod
    def fromList(list: Sequence[PySide6.QtNfc.QNdefRecord]) -> List[PySide6.QtNfc.QNdefRecord]: ...
    @staticmethod
    def fromVector(vector: Sequence[PySide6.QtNfc.QNdefRecord]) -> List[PySide6.QtNfc.QNdefRecord]: ...
    def front(self) -> PySide6.QtNfc.QNdefRecord: ...
    def insert(self, arg__1: int, arg__2: PySide6.QtNfc.QNdefRecord) -> None: ...
    def isEmpty(self) -> bool: ...
    def isSharedWith(self, other: Sequence[PySide6.QtNfc.QNdefRecord]) -> bool: ...
    @overload
    def last(self) -> PySide6.QtNfc.QNdefRecord: ...
    @overload
    def last(self, n: int) -> List[PySide6.QtNfc.QNdefRecord]: ...
    def length(self) -> int: ...
    def mid(self, pos: int, len: int = ...) -> List[PySide6.QtNfc.QNdefRecord]: ...
    def move(self, from_: int, to: int) -> None: ...
    def pop_back(self) -> None: ...
    def pop_front(self) -> None: ...
    def prepend(self, arg__1: PySide6.QtNfc.QNdefRecord) -> None: ...
    def push_back(self, arg__1: PySide6.QtNfc.QNdefRecord) -> None: ...
    def push_front(self, arg__1: PySide6.QtNfc.QNdefRecord) -> None: ...
    def remove(self, i: int, n: int = ...) -> None: ...
    def removeAll(self, arg__1: PySide6.QtNfc.QNdefRecord) -> None: ...
    def removeAt(self, i: int) -> None: ...
    def removeFirst(self) -> None: ...
    def removeLast(self) -> None: ...
    def removeOne(self, arg__1: PySide6.QtNfc.QNdefRecord) -> None: ...
    def reserve(self, size: int) -> None: ...
    def resize(self, size: int) -> None: ...
    def shrink_to_fit(self) -> None: ...
    def size(self) -> int: ...
    @overload
    def sliced(self, pos: int) -> List[PySide6.QtNfc.QNdefRecord]: ...
    @overload
    def sliced(self, pos: int, n: int) -> List[PySide6.QtNfc.QNdefRecord]: ...
    def squeeze(self) -> None: ...
    def swap(self, other: Sequence[PySide6.QtNfc.QNdefRecord]) -> None: ...
    def swapItemsAt(self, i: int, j: int) -> None: ...
    def takeAt(self, i: int) -> PySide6.QtNfc.QNdefRecord: ...
    def toByteArray(self) -> PySide6.QtCore.QByteArray: ...
    def toList(self) -> List[PySide6.QtNfc.QNdefRecord]: ...
    def toVector(self) -> List[PySide6.QtNfc.QNdefRecord]: ...
    def value(self, i: int) -> PySide6.QtNfc.QNdefRecord: ...


class QNdefNfcIconRecord(PySide6.QtNfc.QNdefRecord):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, QNdefNfcIconRecord: Union[PySide6.QtNfc.QNdefNfcIconRecord, PySide6.QtNfc.QNdefRecord]) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def data(self) -> PySide6.QtCore.QByteArray: ...
    def setData(self, data: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...


class QNdefNfcSmartPosterRecord(PySide6.QtNfc.QNdefRecord):

    class Action(enum.Enum):

        UnspecifiedAction        : QNdefNfcSmartPosterRecord.Action = ... # -0x1
        DoAction                 : QNdefNfcSmartPosterRecord.Action = ... # 0x0
        SaveAction               : QNdefNfcSmartPosterRecord.Action = ... # 0x1
        EditAction               : QNdefNfcSmartPosterRecord.Action = ... # 0x2


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord) -> None: ...
    @overload
    def __init__(self, other: Union[PySide6.QtNfc.QNdefNfcSmartPosterRecord, PySide6.QtNfc.QNdefRecord]) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def action(self) -> PySide6.QtNfc.QNdefNfcSmartPosterRecord.Action: ...
    @overload
    def addIcon(self, icon: Union[PySide6.QtNfc.QNdefNfcIconRecord, PySide6.QtNfc.QNdefRecord]) -> None: ...
    @overload
    def addIcon(self, type: Union[PySide6.QtCore.QByteArray, bytes], data: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    @overload
    def addTitle(self, text: str, locale: str, encoding: PySide6.QtNfc.QNdefNfcTextRecord.Encoding) -> bool: ...
    @overload
    def addTitle(self, text: Union[PySide6.QtNfc.QNdefNfcTextRecord, PySide6.QtNfc.QNdefRecord]) -> bool: ...
    def hasAction(self) -> bool: ...
    def hasIcon(self, mimetype: Union[PySide6.QtCore.QByteArray, bytes] = ...) -> bool: ...
    def hasSize(self) -> bool: ...
    def hasTitle(self, locale: str = ...) -> bool: ...
    def hasTypeInfo(self) -> bool: ...
    def icon(self, mimetype: Union[PySide6.QtCore.QByteArray, bytes] = ...) -> PySide6.QtCore.QByteArray: ...
    def iconCount(self) -> int: ...
    def iconRecord(self, index: int) -> PySide6.QtNfc.QNdefNfcIconRecord: ...
    def iconRecords(self) -> List[PySide6.QtNfc.QNdefNfcIconRecord]: ...
    @overload
    def removeIcon(self, icon: Union[PySide6.QtNfc.QNdefNfcIconRecord, PySide6.QtNfc.QNdefRecord]) -> bool: ...
    @overload
    def removeIcon(self, type: Union[PySide6.QtCore.QByteArray, bytes]) -> bool: ...
    @overload
    def removeTitle(self, locale: str) -> bool: ...
    @overload
    def removeTitle(self, text: Union[PySide6.QtNfc.QNdefNfcTextRecord, PySide6.QtNfc.QNdefRecord]) -> bool: ...
    def setAction(self, act: PySide6.QtNfc.QNdefNfcSmartPosterRecord.Action) -> None: ...
    def setIcons(self, icons: Sequence[PySide6.QtNfc.QNdefNfcIconRecord]) -> None: ...
    def setPayload(self, payload: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def setSize(self, size: int) -> None: ...
    def setTitles(self, titles: Sequence[PySide6.QtNfc.QNdefNfcTextRecord]) -> None: ...
    def setTypeInfo(self, type: str) -> None: ...
    @overload
    def setUri(self, url: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    @overload
    def setUri(self, url: Union[PySide6.QtNfc.QNdefNfcUriRecord, PySide6.QtNfc.QNdefRecord]) -> None: ...
    def size(self) -> int: ...
    def title(self, locale: str = ...) -> str: ...
    def titleCount(self) -> int: ...
    def titleRecord(self, index: int) -> PySide6.QtNfc.QNdefNfcTextRecord: ...
    def titleRecords(self) -> List[PySide6.QtNfc.QNdefNfcTextRecord]: ...
    def typeInfo(self) -> str: ...
    def uri(self) -> PySide6.QtCore.QUrl: ...
    def uriRecord(self) -> PySide6.QtNfc.QNdefNfcUriRecord: ...


class QNdefNfcTextRecord(PySide6.QtNfc.QNdefRecord):

    class Encoding(enum.Enum):

        Utf8                     : QNdefNfcTextRecord.Encoding = ... # 0x0
        Utf16                    : QNdefNfcTextRecord.Encoding = ... # 0x1


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, QNdefNfcTextRecord: Union[PySide6.QtNfc.QNdefNfcTextRecord, PySide6.QtNfc.QNdefRecord]) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def encoding(self) -> PySide6.QtNfc.QNdefNfcTextRecord.Encoding: ...
    def locale(self) -> str: ...
    def setEncoding(self, encoding: PySide6.QtNfc.QNdefNfcTextRecord.Encoding) -> None: ...
    def setLocale(self, locale: str) -> None: ...
    def setText(self, text: str) -> None: ...
    def text(self) -> str: ...


class QNdefNfcUriRecord(PySide6.QtNfc.QNdefRecord):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, QNdefNfcUriRecord: Union[PySide6.QtNfc.QNdefNfcUriRecord, PySide6.QtNfc.QNdefRecord]) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def setUri(self, uri: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def uri(self) -> PySide6.QtCore.QUrl: ...


class QNdefRecord(Shiboken.Object):

    class TypeNameFormat(enum.Enum):

        Empty                    : QNdefRecord.TypeNameFormat = ... # 0x0
        NfcRtd                   : QNdefRecord.TypeNameFormat = ... # 0x1
        Mime                     : QNdefRecord.TypeNameFormat = ... # 0x2
        Uri                      : QNdefRecord.TypeNameFormat = ... # 0x3
        ExternalRtd              : QNdefRecord.TypeNameFormat = ... # 0x4
        Unknown                  : QNdefRecord.TypeNameFormat = ... # 0x5


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, type: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    @overload
    def __init__(self, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, type: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def clear(self) -> None: ...
    def id(self) -> PySide6.QtCore.QByteArray: ...
    def isEmpty(self) -> bool: ...
    def payload(self) -> PySide6.QtCore.QByteArray: ...
    def setId(self, id: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def setPayload(self, payload: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def setType(self, type: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def setTypeNameFormat(self, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat) -> None: ...
    def type(self) -> PySide6.QtCore.QByteArray: ...
    def typeNameFormat(self) -> PySide6.QtNfc.QNdefRecord.TypeNameFormat: ...


class QNearFieldManager(PySide6.QtCore.QObject):

    adapterStateChanged      : ClassVar[Signal] = ... # adapterStateChanged(QNearFieldManager::AdapterState)
    targetDetected           : ClassVar[Signal] = ... # targetDetected(QNearFieldTarget*)
    targetDetectionStopped   : ClassVar[Signal] = ... # targetDetectionStopped()
    targetLost               : ClassVar[Signal] = ... # targetLost(QNearFieldTarget*)

    class AdapterState(enum.Enum):

        Offline                  : QNearFieldManager.AdapterState = ... # 0x1
        TurningOn                : QNearFieldManager.AdapterState = ... # 0x2
        Online                   : QNearFieldManager.AdapterState = ... # 0x3
        TurningOff               : QNearFieldManager.AdapterState = ... # 0x4


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def isEnabled(self) -> bool: ...
    def isSupported(self, accessMethod: PySide6.QtNfc.QNearFieldTarget.AccessMethod = ...) -> bool: ...
    def setUserInformation(self, message: str) -> None: ...
    def startTargetDetection(self, accessMethod: PySide6.QtNfc.QNearFieldTarget.AccessMethod) -> bool: ...
    def stopTargetDetection(self, errorMessage: str = ...) -> None: ...


class QNearFieldTarget(PySide6.QtCore.QObject):

    disconnected             : ClassVar[Signal] = ... # disconnected()
    error                    : ClassVar[Signal] = ... # error(QNearFieldTarget::Error,QNearFieldTarget::RequestId)
    ndefMessageRead          : ClassVar[Signal] = ... # ndefMessageRead(QNdefMessage)
    requestCompleted         : ClassVar[Signal] = ... # requestCompleted(QNearFieldTarget::RequestId)

    class AccessMethod(enum.Flag):

        UnknownAccess            : QNearFieldTarget.AccessMethod = ... # 0x0
        NdefAccess               : QNearFieldTarget.AccessMethod = ... # 0x1
        TagTypeSpecificAccess    : QNearFieldTarget.AccessMethod = ... # 0x2
        AnyAccess                : QNearFieldTarget.AccessMethod = ... # 0xff

    class Error(enum.Enum):

        NoError                  : QNearFieldTarget.Error = ... # 0x0
        UnknownError             : QNearFieldTarget.Error = ... # 0x1
        UnsupportedError         : QNearFieldTarget.Error = ... # 0x2
        TargetOutOfRangeError    : QNearFieldTarget.Error = ... # 0x3
        NoResponseError          : QNearFieldTarget.Error = ... # 0x4
        ChecksumMismatchError    : QNearFieldTarget.Error = ... # 0x5
        InvalidParametersError   : QNearFieldTarget.Error = ... # 0x6
        ConnectionError          : QNearFieldTarget.Error = ... # 0x7
        NdefReadError            : QNearFieldTarget.Error = ... # 0x8
        NdefWriteError           : QNearFieldTarget.Error = ... # 0x9
        CommandError             : QNearFieldTarget.Error = ... # 0xa
        TimeoutError             : QNearFieldTarget.Error = ... # 0xb

    class Type(enum.Enum):

        ProprietaryTag           : QNearFieldTarget.Type = ... # 0x0
        NfcTagType1              : QNearFieldTarget.Type = ... # 0x1
        NfcTagType2              : QNearFieldTarget.Type = ... # 0x2
        NfcTagType3              : QNearFieldTarget.Type = ... # 0x3
        NfcTagType4              : QNearFieldTarget.Type = ... # 0x4
        NfcTagType4A             : QNearFieldTarget.Type = ... # 0x5
        NfcTagType4B             : QNearFieldTarget.Type = ... # 0x6
        MifareTag                : QNearFieldTarget.Type = ... # 0x7


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def accessMethods(self) -> PySide6.QtNfc.QNearFieldTarget.AccessMethod: ...
    def disconnect(self) -> bool: ...
    def hasNdefMessage(self) -> bool: ...
    def maxCommandLength(self) -> int: ...
    def type(self) -> PySide6.QtNfc.QNearFieldTarget.Type: ...
    def uid(self) -> PySide6.QtCore.QByteArray: ...


# eof
