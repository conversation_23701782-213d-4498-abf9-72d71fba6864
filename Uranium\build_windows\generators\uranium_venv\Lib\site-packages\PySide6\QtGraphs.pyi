# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtGraphs, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtGraphs`

import PySide6.QtGraphs
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtQuickWidgets

import enum
from typing import Any, ClassVar, List, Optional, Sequence, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class Q3DBars(PySide6.QtGraphs.QAbstract3DGraph):

    barSeriesMarginChanged   : ClassVar[Signal] = ... # barSeriesMarginChanged(QSizeF)
    barSpacingChanged        : ClassVar[Signal] = ... # barSpacingChanged(QSizeF)
    barSpacingRelativeChanged: ClassVar[Signal] = ... # barSpacingRelativeChanged(bool)
    barThicknessChanged      : ClassVar[Signal] = ... # barThicknessChanged(float)
    columnAxisChanged        : ClassVar[Signal] = ... # columnAxisChanged(QCategory3DAxis*)
    floorLevelChanged        : ClassVar[Signal] = ... # floorLevelChanged(float)
    multiSeriesUniformChanged: ClassVar[Signal] = ... # multiSeriesUniformChanged(bool)
    primarySeriesChanged     : ClassVar[Signal] = ... # primarySeriesChanged(QBar3DSeries*)
    rowAxisChanged           : ClassVar[Signal] = ... # rowAxisChanged(QCategory3DAxis*)
    selectedSeriesChanged    : ClassVar[Signal] = ... # selectedSeriesChanged(QBar3DSeries*)
    valueAxisChanged         : ClassVar[Signal] = ... # valueAxisChanged(QValue3DAxis*)

    def __init__(self) -> None: ...

    def addAxis(self, axis: PySide6.QtGraphs.QAbstract3DAxis) -> None: ...
    def addSeries(self, series: PySide6.QtGraphs.QBar3DSeries) -> None: ...
    def axes(self) -> List[PySide6.QtGraphs.QAbstract3DAxis]: ...
    def barSeriesMargin(self) -> PySide6.QtCore.QSizeF: ...
    def barSpacing(self) -> PySide6.QtCore.QSizeF: ...
    def barThickness(self) -> float: ...
    def columnAxis(self) -> PySide6.QtGraphs.QCategory3DAxis: ...
    def floorLevel(self) -> float: ...
    def insertSeries(self, index: int, series: PySide6.QtGraphs.QBar3DSeries) -> None: ...
    def isBarSpacingRelative(self) -> bool: ...
    def isMultiSeriesUniform(self) -> bool: ...
    def primarySeries(self) -> PySide6.QtGraphs.QBar3DSeries: ...
    def releaseAxis(self, axis: PySide6.QtGraphs.QAbstract3DAxis) -> None: ...
    def removeSeries(self, series: PySide6.QtGraphs.QBar3DSeries) -> None: ...
    def rowAxis(self) -> PySide6.QtGraphs.QCategory3DAxis: ...
    def selectedSeries(self) -> PySide6.QtGraphs.QBar3DSeries: ...
    def seriesList(self) -> List[PySide6.QtGraphs.QBar3DSeries]: ...
    def setBarSeriesMargin(self, margin: Union[PySide6.QtCore.QSizeF, PySide6.QtCore.QSize]) -> None: ...
    def setBarSpacing(self, spacing: Union[PySide6.QtCore.QSizeF, PySide6.QtCore.QSize]) -> None: ...
    def setBarSpacingRelative(self, relative: bool) -> None: ...
    def setBarThickness(self, thicknessRatio: float) -> None: ...
    def setColumnAxis(self, axis: PySide6.QtGraphs.QCategory3DAxis) -> None: ...
    def setFloorLevel(self, level: float) -> None: ...
    def setMultiSeriesUniform(self, uniform: bool) -> None: ...
    def setPrimarySeries(self, series: PySide6.QtGraphs.QBar3DSeries) -> None: ...
    def setRowAxis(self, axis: PySide6.QtGraphs.QCategory3DAxis) -> None: ...
    def setValueAxis(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def valueAxis(self) -> PySide6.QtGraphs.QValue3DAxis: ...


class Q3DCamera(PySide6.QtGraphs.Q3DObject):

    cameraPresetChanged      : ClassVar[Signal] = ... # cameraPresetChanged(Q3DCamera::CameraPreset)
    maxXRotationChanged      : ClassVar[Signal] = ... # maxXRotationChanged(float)
    maxYRotationChanged      : ClassVar[Signal] = ... # maxYRotationChanged(float)
    maxZoomLevelChanged      : ClassVar[Signal] = ... # maxZoomLevelChanged(float)
    minXRotationChanged      : ClassVar[Signal] = ... # minXRotationChanged(float)
    minYRotationChanged      : ClassVar[Signal] = ... # minYRotationChanged(float)
    minZoomLevelChanged      : ClassVar[Signal] = ... # minZoomLevelChanged(float)
    targetChanged            : ClassVar[Signal] = ... # targetChanged(QVector3D)
    viewMatrixAutoUpdateChanged: ClassVar[Signal] = ... # viewMatrixAutoUpdateChanged(bool)
    viewMatrixChanged        : ClassVar[Signal] = ... # viewMatrixChanged(QMatrix4x4)
    wrapXRotationChanged     : ClassVar[Signal] = ... # wrapXRotationChanged(bool)
    wrapYRotationChanged     : ClassVar[Signal] = ... # wrapYRotationChanged(bool)
    xRotationChanged         : ClassVar[Signal] = ... # xRotationChanged(float)
    yRotationChanged         : ClassVar[Signal] = ... # yRotationChanged(float)
    zoomLevelChanged         : ClassVar[Signal] = ... # zoomLevelChanged(float)

    class CameraPreset(enum.Enum):

        CameraPresetNone         : Q3DCamera.CameraPreset = ... # -0x1
        CameraPresetFrontLow     : Q3DCamera.CameraPreset = ... # 0x0
        CameraPresetFront        : Q3DCamera.CameraPreset = ... # 0x1
        CameraPresetFrontHigh    : Q3DCamera.CameraPreset = ... # 0x2
        CameraPresetLeftLow      : Q3DCamera.CameraPreset = ... # 0x3
        CameraPresetLeft         : Q3DCamera.CameraPreset = ... # 0x4
        CameraPresetLeftHigh     : Q3DCamera.CameraPreset = ... # 0x5
        CameraPresetRightLow     : Q3DCamera.CameraPreset = ... # 0x6
        CameraPresetRight        : Q3DCamera.CameraPreset = ... # 0x7
        CameraPresetRightHigh    : Q3DCamera.CameraPreset = ... # 0x8
        CameraPresetBehindLow    : Q3DCamera.CameraPreset = ... # 0x9
        CameraPresetBehind       : Q3DCamera.CameraPreset = ... # 0xa
        CameraPresetBehindHigh   : Q3DCamera.CameraPreset = ... # 0xb
        CameraPresetIsometricLeft: Q3DCamera.CameraPreset = ... # 0xc
        CameraPresetIsometricLeftHigh: Q3DCamera.CameraPreset = ... # 0xd
        CameraPresetIsometricRight: Q3DCamera.CameraPreset = ... # 0xe
        CameraPresetIsometricRightHigh: Q3DCamera.CameraPreset = ... # 0xf
        CameraPresetDirectlyAbove: Q3DCamera.CameraPreset = ... # 0x10
        CameraPresetDirectlyAboveCW45: Q3DCamera.CameraPreset = ... # 0x11
        CameraPresetDirectlyAboveCCW45: Q3DCamera.CameraPreset = ... # 0x12
        CameraPresetFrontBelow   : Q3DCamera.CameraPreset = ... # 0x13
        CameraPresetLeftBelow    : Q3DCamera.CameraPreset = ... # 0x14
        CameraPresetRightBelow   : Q3DCamera.CameraPreset = ... # 0x15
        CameraPresetBehindBelow  : Q3DCamera.CameraPreset = ... # 0x16
        CameraPresetDirectlyBelow: Q3DCamera.CameraPreset = ... # 0x17


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def cameraPreset(self) -> PySide6.QtGraphs.Q3DCamera.CameraPreset: ...
    def copyValuesFrom(self, source: PySide6.QtGraphs.Q3DObject) -> None: ...
    def maxZoomLevel(self) -> float: ...
    def minZoomLevel(self) -> float: ...
    def setCameraPosition(self, horizontal: float, vertical: float, zoom: float = ...) -> None: ...
    def setCameraPreset(self, preset: PySide6.QtGraphs.Q3DCamera.CameraPreset) -> None: ...
    def setMaxZoomLevel(self, zoomLevel: float) -> None: ...
    def setMinZoomLevel(self, zoomLevel: float) -> None: ...
    def setTarget(self, target: PySide6.QtGui.QVector3D) -> None: ...
    def setWrapXRotation(self, isEnabled: bool) -> None: ...
    def setWrapYRotation(self, isEnabled: bool) -> None: ...
    def setXRotation(self, rotation: float) -> None: ...
    def setYRotation(self, rotation: float) -> None: ...
    def setZoomLevel(self, zoomLevel: float) -> None: ...
    def target(self) -> PySide6.QtGui.QVector3D: ...
    def wrapXRotation(self) -> bool: ...
    def wrapYRotation(self) -> bool: ...
    def xRotation(self) -> float: ...
    def yRotation(self) -> float: ...
    def zoomLevel(self) -> float: ...


class Q3DInputHandler(PySide6.QtGraphs.QAbstract3DInputHandler):

    rotationEnabledChanged   : ClassVar[Signal] = ... # rotationEnabledChanged(bool)
    selectionEnabledChanged  : ClassVar[Signal] = ... # selectionEnabledChanged(bool)
    zoomAtTargetEnabledChanged: ClassVar[Signal] = ... # zoomAtTargetEnabledChanged(bool)
    zoomEnabledChanged       : ClassVar[Signal] = ... # zoomEnabledChanged(bool)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def isRotationEnabled(self) -> bool: ...
    def isSelectionEnabled(self) -> bool: ...
    def isZoomAtTargetEnabled(self) -> bool: ...
    def isZoomEnabled(self) -> bool: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint) -> None: ...
    def setRotationEnabled(self, enable: bool) -> None: ...
    def setSelectionEnabled(self, enable: bool) -> None: ...
    def setZoomAtTargetEnabled(self, enable: bool) -> None: ...
    def setZoomEnabled(self, enable: bool) -> None: ...
    def wheelEvent(self, event: PySide6.QtGui.QWheelEvent) -> None: ...


class Q3DLight(PySide6.QtGraphs.Q3DObject):

    autoPositionChanged      : ClassVar[Signal] = ... # autoPositionChanged(bool)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def isAutoPosition(self) -> bool: ...
    def setAutoPosition(self, enabled: bool) -> None: ...


class Q3DObject(PySide6.QtCore.QObject):

    positionChanged          : ClassVar[Signal] = ... # positionChanged(QVector3D)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def copyValuesFrom(self, source: PySide6.QtGraphs.Q3DObject) -> None: ...
    def isDirty(self) -> bool: ...
    def parentScene(self) -> PySide6.QtGraphs.Q3DScene: ...
    def position(self) -> PySide6.QtGui.QVector3D: ...
    def setDirty(self, dirty: bool) -> None: ...
    def setPosition(self, position: PySide6.QtGui.QVector3D) -> None: ...


class Q3DScatter(PySide6.QtGraphs.QAbstract3DGraph):

    axisXChanged             : ClassVar[Signal] = ... # axisXChanged(QValue3DAxis*)
    axisYChanged             : ClassVar[Signal] = ... # axisYChanged(QValue3DAxis*)
    axisZChanged             : ClassVar[Signal] = ... # axisZChanged(QValue3DAxis*)
    selectedSeriesChanged    : ClassVar[Signal] = ... # selectedSeriesChanged(QScatter3DSeries*)

    def __init__(self) -> None: ...

    def addAxis(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def addSeries(self, series: PySide6.QtGraphs.QScatter3DSeries) -> None: ...
    def axes(self) -> List[PySide6.QtGraphs.QValue3DAxis]: ...
    def axisX(self) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisY(self) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisZ(self) -> PySide6.QtGraphs.QValue3DAxis: ...
    def releaseAxis(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def removeSeries(self, series: PySide6.QtGraphs.QScatter3DSeries) -> None: ...
    def selectedSeries(self) -> PySide6.QtGraphs.QScatter3DSeries: ...
    def seriesList(self) -> List[PySide6.QtGraphs.QScatter3DSeries]: ...
    def setAxisX(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def setAxisY(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def setAxisZ(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...


class Q3DScene(PySide6.QtCore.QObject):

    activeCameraChanged      : ClassVar[Signal] = ... # activeCameraChanged(Q3DCamera*)
    activeLightChanged       : ClassVar[Signal] = ... # activeLightChanged(Q3DLight*)
    devicePixelRatioChanged  : ClassVar[Signal] = ... # devicePixelRatioChanged(float)
    graphPositionQueryChanged: ClassVar[Signal] = ... # graphPositionQueryChanged(QPoint)
    primarySubViewportChanged: ClassVar[Signal] = ... # primarySubViewportChanged(QRect)
    secondarySubViewportChanged: ClassVar[Signal] = ... # secondarySubViewportChanged(QRect)
    secondarySubviewOnTopChanged: ClassVar[Signal] = ... # secondarySubviewOnTopChanged(bool)
    selectionQueryPositionChanged: ClassVar[Signal] = ... # selectionQueryPositionChanged(QPoint)
    slicingActiveChanged     : ClassVar[Signal] = ... # slicingActiveChanged(bool)
    viewportChanged          : ClassVar[Signal] = ... # viewportChanged(QRect)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def activeCamera(self) -> PySide6.QtGraphs.Q3DCamera: ...
    def activeLight(self) -> PySide6.QtGraphs.Q3DLight: ...
    def devicePixelRatio(self) -> float: ...
    def graphPositionQuery(self) -> PySide6.QtCore.QPoint: ...
    @staticmethod
    def invalidSelectionPoint() -> PySide6.QtCore.QPoint: ...
    def isPointInPrimarySubView(self, point: PySide6.QtCore.QPoint) -> bool: ...
    def isPointInSecondarySubView(self, point: PySide6.QtCore.QPoint) -> bool: ...
    def isSecondarySubviewOnTop(self) -> bool: ...
    def isSlicingActive(self) -> bool: ...
    def primarySubViewport(self) -> PySide6.QtCore.QRect: ...
    def secondarySubViewport(self) -> PySide6.QtCore.QRect: ...
    def selectionQueryPosition(self) -> PySide6.QtCore.QPoint: ...
    def setActiveCamera(self, camera: PySide6.QtGraphs.Q3DCamera) -> None: ...
    def setActiveLight(self, light: PySide6.QtGraphs.Q3DLight) -> None: ...
    def setDevicePixelRatio(self, pixelRatio: float) -> None: ...
    def setGraphPositionQuery(self, point: PySide6.QtCore.QPoint) -> None: ...
    def setPrimarySubViewport(self, primarySubViewport: PySide6.QtCore.QRect) -> None: ...
    def setSecondarySubViewport(self, secondarySubViewport: PySide6.QtCore.QRect) -> None: ...
    def setSecondarySubviewOnTop(self, isSecondaryOnTop: bool) -> None: ...
    def setSelectionQueryPosition(self, point: PySide6.QtCore.QPoint) -> None: ...
    def setSlicingActive(self, isSlicing: bool) -> None: ...
    def viewport(self) -> PySide6.QtCore.QRect: ...


class Q3DSurface(PySide6.QtGraphs.QAbstract3DGraph):

    axisXChanged             : ClassVar[Signal] = ... # axisXChanged(QValue3DAxis*)
    axisYChanged             : ClassVar[Signal] = ... # axisYChanged(QValue3DAxis*)
    axisZChanged             : ClassVar[Signal] = ... # axisZChanged(QValue3DAxis*)
    flipHorizontalGridChanged: ClassVar[Signal] = ... # flipHorizontalGridChanged(bool)
    selectedSeriesChanged    : ClassVar[Signal] = ... # selectedSeriesChanged(QSurface3DSeries*)

    def __init__(self) -> None: ...

    def addAxis(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def addSeries(self, series: PySide6.QtGraphs.QSurface3DSeries) -> None: ...
    def axes(self) -> List[PySide6.QtGraphs.QValue3DAxis]: ...
    def axisX(self) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisY(self) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisZ(self) -> PySide6.QtGraphs.QValue3DAxis: ...
    def flipHorizontalGrid(self) -> bool: ...
    def releaseAxis(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def removeSeries(self, series: PySide6.QtGraphs.QSurface3DSeries) -> None: ...
    def selectedSeries(self) -> PySide6.QtGraphs.QSurface3DSeries: ...
    def seriesList(self) -> List[PySide6.QtGraphs.QSurface3DSeries]: ...
    def setAxisX(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def setAxisY(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def setAxisZ(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def setFlipHorizontalGrid(self, flip: bool) -> None: ...


class Q3DTheme(PySide6.QtCore.QObject):

    ambientLightStrengthChanged: ClassVar[Signal] = ... # ambientLightStrengthChanged(float)
    backgroundColorChanged   : ClassVar[Signal] = ... # backgroundColorChanged(QColor)
    backgroundEnabledChanged : ClassVar[Signal] = ... # backgroundEnabledChanged(bool)
    baseColorsChanged        : ClassVar[Signal] = ... # baseColorsChanged(QList<QColor>)
    baseGradientsChanged     : ClassVar[Signal] = ... # baseGradientsChanged(QList<QLinearGradient>)
    colorStyleChanged        : ClassVar[Signal] = ... # colorStyleChanged(Q3DTheme::ColorStyle)
    fontChanged              : ClassVar[Signal] = ... # fontChanged(QFont)
    gridEnabledChanged       : ClassVar[Signal] = ... # gridEnabledChanged(bool)
    gridLineColorChanged     : ClassVar[Signal] = ... # gridLineColorChanged(QColor)
    highlightLightStrengthChanged: ClassVar[Signal] = ... # highlightLightStrengthChanged(float)
    labelBackgroundColorChanged: ClassVar[Signal] = ... # labelBackgroundColorChanged(QColor)
    labelBackgroundEnabledChanged: ClassVar[Signal] = ... # labelBackgroundEnabledChanged(bool)
    labelBorderEnabledChanged: ClassVar[Signal] = ... # labelBorderEnabledChanged(bool)
    labelTextColorChanged    : ClassVar[Signal] = ... # labelTextColorChanged(QColor)
    labelsEnabledChanged     : ClassVar[Signal] = ... # labelsEnabledChanged(bool)
    lightColorChanged        : ClassVar[Signal] = ... # lightColorChanged(QColor)
    lightStrengthChanged     : ClassVar[Signal] = ... # lightStrengthChanged(float)
    multiHighlightColorChanged: ClassVar[Signal] = ... # multiHighlightColorChanged(QColor)
    multiHighlightGradientChanged: ClassVar[Signal] = ... # multiHighlightGradientChanged(QLinearGradient)
    shadowStrengthChanged    : ClassVar[Signal] = ... # shadowStrengthChanged(float)
    singleHighlightColorChanged: ClassVar[Signal] = ... # singleHighlightColorChanged(QColor)
    singleHighlightGradientChanged: ClassVar[Signal] = ... # singleHighlightGradientChanged(QLinearGradient)
    typeChanged              : ClassVar[Signal] = ... # typeChanged(Q3DTheme::Theme)
    windowColorChanged       : ClassVar[Signal] = ... # windowColorChanged(QColor)

    class ColorStyle(enum.Enum):

        ColorStyleUniform        : Q3DTheme.ColorStyle = ... # 0x0
        ColorStyleObjectGradient : Q3DTheme.ColorStyle = ... # 0x1
        ColorStyleRangeGradient  : Q3DTheme.ColorStyle = ... # 0x2

    class Theme(enum.Enum):

        ThemeQt                  : Q3DTheme.Theme = ... # 0x0
        ThemePrimaryColors       : Q3DTheme.Theme = ... # 0x1
        ThemeDigia               : Q3DTheme.Theme = ... # 0x2
        ThemeStoneMoss           : Q3DTheme.Theme = ... # 0x3
        ThemeArmyBlue            : Q3DTheme.Theme = ... # 0x4
        ThemeRetro               : Q3DTheme.Theme = ... # 0x5
        ThemeEbony               : Q3DTheme.Theme = ... # 0x6
        ThemeIsabelle            : Q3DTheme.Theme = ... # 0x7
        ThemeUserDefined         : Q3DTheme.Theme = ... # 0x8


    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, themeType: PySide6.QtGraphs.Q3DTheme.Theme, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def ambientLightStrength(self) -> float: ...
    def backgroundColor(self) -> PySide6.QtGui.QColor: ...
    def baseColors(self) -> List[PySide6.QtGui.QColor]: ...
    def baseGradients(self) -> List[PySide6.QtGui.QLinearGradient]: ...
    def colorStyle(self) -> PySide6.QtGraphs.Q3DTheme.ColorStyle: ...
    def font(self) -> PySide6.QtGui.QFont: ...
    def gridLineColor(self) -> PySide6.QtGui.QColor: ...
    def highlightLightStrength(self) -> float: ...
    def isBackgroundEnabled(self) -> bool: ...
    def isGridEnabled(self) -> bool: ...
    def isLabelBackgroundEnabled(self) -> bool: ...
    def isLabelBorderEnabled(self) -> bool: ...
    def isLabelsEnabled(self) -> bool: ...
    def labelBackgroundColor(self) -> PySide6.QtGui.QColor: ...
    def labelTextColor(self) -> PySide6.QtGui.QColor: ...
    def lightColor(self) -> PySide6.QtGui.QColor: ...
    def lightStrength(self) -> float: ...
    def multiHighlightColor(self) -> PySide6.QtGui.QColor: ...
    def multiHighlightGradient(self) -> PySide6.QtGui.QLinearGradient: ...
    def setAmbientLightStrength(self, strength: float) -> None: ...
    def setBackgroundColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBackgroundEnabled(self, enabled: bool) -> None: ...
    def setBaseColors(self, colors: Sequence[PySide6.QtGui.QColor]) -> None: ...
    def setBaseGradients(self, gradients: Sequence[PySide6.QtGui.QLinearGradient]) -> None: ...
    def setColorStyle(self, style: PySide6.QtGraphs.Q3DTheme.ColorStyle) -> None: ...
    def setFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setGridEnabled(self, enabled: bool) -> None: ...
    def setGridLineColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setHighlightLightStrength(self, strength: float) -> None: ...
    def setLabelBackgroundColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLabelBackgroundEnabled(self, enabled: bool) -> None: ...
    def setLabelBorderEnabled(self, enabled: bool) -> None: ...
    def setLabelTextColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLabelsEnabled(self, enabled: bool) -> None: ...
    def setLightColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setLightStrength(self, strength: float) -> None: ...
    def setMultiHighlightColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setMultiHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient) -> None: ...
    def setShadowStrength(self, strength: float) -> None: ...
    def setSingleHighlightColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setSingleHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient) -> None: ...
    def setType(self, themeType: PySide6.QtGraphs.Q3DTheme.Theme) -> None: ...
    def setWindowColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def shadowStrength(self) -> float: ...
    def singleHighlightColor(self) -> PySide6.QtGui.QColor: ...
    def singleHighlightGradient(self) -> PySide6.QtGui.QLinearGradient: ...
    def type(self) -> PySide6.QtGraphs.Q3DTheme.Theme: ...
    def windowColor(self) -> PySide6.QtGui.QColor: ...


class QAbstract3DAxis(PySide6.QtCore.QObject):

    autoAdjustRangeChanged   : ClassVar[Signal] = ... # autoAdjustRangeChanged(bool)
    labelAutoRotationChanged : ClassVar[Signal] = ... # labelAutoRotationChanged(float)
    labelsChanged            : ClassVar[Signal] = ... # labelsChanged()
    maxChanged               : ClassVar[Signal] = ... # maxChanged(float)
    minChanged               : ClassVar[Signal] = ... # minChanged(float)
    orientationChanged       : ClassVar[Signal] = ... # orientationChanged(QAbstract3DAxis::AxisOrientation)
    rangeChanged             : ClassVar[Signal] = ... # rangeChanged(float,float)
    titleChanged             : ClassVar[Signal] = ... # titleChanged(QString)
    titleFixedChanged        : ClassVar[Signal] = ... # titleFixedChanged(bool)
    titleVisibilityChanged   : ClassVar[Signal] = ... # titleVisibilityChanged(bool)

    class AxisOrientation(enum.Enum):

        AxisOrientationNone      : QAbstract3DAxis.AxisOrientation = ... # 0x0
        AxisOrientationX         : QAbstract3DAxis.AxisOrientation = ... # 0x1
        AxisOrientationY         : QAbstract3DAxis.AxisOrientation = ... # 0x2
        AxisOrientationZ         : QAbstract3DAxis.AxisOrientation = ... # 0x4

    class AxisType(enum.Enum):

        AxisTypeNone             : QAbstract3DAxis.AxisType = ... # 0x0
        AxisTypeCategory         : QAbstract3DAxis.AxisType = ... # 0x1
        AxisTypeValue            : QAbstract3DAxis.AxisType = ... # 0x2


    def isAutoAdjustRange(self) -> bool: ...
    def isTitleFixed(self) -> bool: ...
    def isTitleVisible(self) -> bool: ...
    def labelAutoRotation(self) -> float: ...
    def labels(self) -> List[str]: ...
    def max(self) -> float: ...
    def min(self) -> float: ...
    def orientation(self) -> PySide6.QtGraphs.QAbstract3DAxis.AxisOrientation: ...
    def setAutoAdjustRange(self, autoAdjust: bool) -> None: ...
    def setLabelAutoRotation(self, angle: float) -> None: ...
    def setLabels(self, labels: Sequence[str]) -> None: ...
    def setMax(self, max: float) -> None: ...
    def setMin(self, min: float) -> None: ...
    def setRange(self, min: float, max: float) -> None: ...
    def setTitle(self, title: str) -> None: ...
    def setTitleFixed(self, fixed: bool) -> None: ...
    def setTitleVisible(self, visible: bool) -> None: ...
    def title(self) -> str: ...
    def type(self) -> PySide6.QtGraphs.QAbstract3DAxis.AxisType: ...


class QAbstract3DGraph(PySide6.QtQuickWidgets.QQuickWidget):

    activeInputHandlerChanged: ClassVar[Signal] = ... # activeInputHandlerChanged(QAbstract3DInputHandler*)
    activeThemeChanged       : ClassVar[Signal] = ... # activeThemeChanged(Q3DTheme*)
    aspectRatioChanged       : ClassVar[Signal] = ... # aspectRatioChanged(double)
    currentFpsChanged        : ClassVar[Signal] = ... # currentFpsChanged(int)
    horizontalAspectRatioChanged: ClassVar[Signal] = ... # horizontalAspectRatioChanged(double)
    localeChanged            : ClassVar[Signal] = ... # localeChanged(QLocale)
    marginChanged            : ClassVar[Signal] = ... # marginChanged(double)
    measureFpsChanged        : ClassVar[Signal] = ... # measureFpsChanged(bool)
    optimizationHintsChanged : ClassVar[Signal] = ... # optimizationHintsChanged(QAbstract3DGraph::OptimizationHints)
    orthoProjectionChanged   : ClassVar[Signal] = ... # orthoProjectionChanged(bool)
    polarChanged             : ClassVar[Signal] = ... # polarChanged(bool)
    queriedGraphPositionChanged: ClassVar[Signal] = ... # queriedGraphPositionChanged(QVector3D)
    radialLabelOffsetChanged : ClassVar[Signal] = ... # radialLabelOffsetChanged(float)
    reflectionChanged        : ClassVar[Signal] = ... # reflectionChanged(bool)
    reflectivityChanged      : ClassVar[Signal] = ... # reflectivityChanged(double)
    selectedElementChanged   : ClassVar[Signal] = ... # selectedElementChanged(QAbstract3DGraph::ElementType)
    selectionModeChanged     : ClassVar[Signal] = ... # selectionModeChanged(QAbstract3DGraph::SelectionFlags)
    shadowQualityChanged     : ClassVar[Signal] = ... # shadowQualityChanged(QAbstract3DGraph::ShadowQuality)

    class ElementType(enum.Enum):

        ElementNone              : QAbstract3DGraph.ElementType = ... # 0x0
        ElementSeries            : QAbstract3DGraph.ElementType = ... # 0x1
        ElementAxisXLabel        : QAbstract3DGraph.ElementType = ... # 0x2
        ElementAxisYLabel        : QAbstract3DGraph.ElementType = ... # 0x3
        ElementAxisZLabel        : QAbstract3DGraph.ElementType = ... # 0x4
        ElementCustomItem        : QAbstract3DGraph.ElementType = ... # 0x5

    class OptimizationHint(enum.Flag):

        OptimizationDefault      : QAbstract3DGraph.OptimizationHint = ... # 0x0
        OptimizationStatic       : QAbstract3DGraph.OptimizationHint = ... # 0x1
        OptimizationLegacy       : QAbstract3DGraph.OptimizationHint = ... # 0x2

    class RenderingMode(enum.Enum):

        RenderDirectToBackground : QAbstract3DGraph.RenderingMode = ... # 0x0
        RenderIndirect           : QAbstract3DGraph.RenderingMode = ... # 0x1

    class SelectionFlag(enum.Flag):

        SelectionNone            : QAbstract3DGraph.SelectionFlag = ... # 0x0
        SelectionItem            : QAbstract3DGraph.SelectionFlag = ... # 0x1
        SelectionRow             : QAbstract3DGraph.SelectionFlag = ... # 0x2
        SelectionItemAndRow      : QAbstract3DGraph.SelectionFlag = ... # 0x3
        SelectionColumn          : QAbstract3DGraph.SelectionFlag = ... # 0x4
        SelectionItemAndColumn   : QAbstract3DGraph.SelectionFlag = ... # 0x5
        SelectionRowAndColumn    : QAbstract3DGraph.SelectionFlag = ... # 0x6
        SelectionItemRowAndColumn: QAbstract3DGraph.SelectionFlag = ... # 0x7
        SelectionSlice           : QAbstract3DGraph.SelectionFlag = ... # 0x8
        SelectionMultiSeries     : QAbstract3DGraph.SelectionFlag = ... # 0x10

    class ShadowQuality(enum.Enum):

        ShadowQualityNone        : QAbstract3DGraph.ShadowQuality = ... # 0x0
        ShadowQualityLow         : QAbstract3DGraph.ShadowQuality = ... # 0x1
        ShadowQualityMedium      : QAbstract3DGraph.ShadowQuality = ... # 0x2
        ShadowQualityHigh        : QAbstract3DGraph.ShadowQuality = ... # 0x3
        ShadowQualitySoftLow     : QAbstract3DGraph.ShadowQuality = ... # 0x4
        ShadowQualitySoftMedium  : QAbstract3DGraph.ShadowQuality = ... # 0x5
        ShadowQualitySoftHigh    : QAbstract3DGraph.ShadowQuality = ... # 0x6


    def __init__(self) -> None: ...

    def activeInputHandler(self) -> PySide6.QtGraphs.QAbstract3DInputHandler: ...
    def activeTheme(self) -> PySide6.QtGraphs.Q3DTheme: ...
    def addCustomItem(self, item: PySide6.QtGraphs.QCustom3DItem) -> int: ...
    def addInputHandler(self, inputHandler: PySide6.QtGraphs.QAbstract3DInputHandler) -> None: ...
    def addTheme(self, theme: PySide6.QtGraphs.Q3DTheme) -> None: ...
    def aspectRatio(self) -> float: ...
    def clearSelection(self) -> None: ...
    def currentFps(self) -> int: ...
    def customItems(self) -> List[PySide6.QtGraphs.QCustom3DItem]: ...
    def event(self, event: PySide6.QtCore.QEvent) -> bool: ...
    def hasSeries(self, series: PySide6.QtGraphs.QAbstract3DSeries) -> bool: ...
    def horizontalAspectRatio(self) -> float: ...
    def inputHandlers(self) -> List[PySide6.QtGraphs.QAbstract3DInputHandler]: ...
    def isOrthoProjection(self) -> bool: ...
    def isPolar(self) -> bool: ...
    def isReflection(self) -> bool: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def margin(self) -> float: ...
    def measureFps(self) -> bool: ...
    def mouseDoubleClickEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def optimizationHints(self) -> PySide6.QtGraphs.QAbstract3DGraph.OptimizationHint: ...
    def queriedGraphPosition(self) -> PySide6.QtGui.QVector3D: ...
    def radialLabelOffset(self) -> float: ...
    def reflectivity(self) -> float: ...
    def releaseCustomItem(self, item: PySide6.QtGraphs.QCustom3DItem) -> None: ...
    def releaseInputHandler(self, inputHandler: PySide6.QtGraphs.QAbstract3DInputHandler) -> None: ...
    def releaseTheme(self, theme: PySide6.QtGraphs.Q3DTheme) -> None: ...
    def removeCustomItem(self, item: PySide6.QtGraphs.QCustom3DItem) -> None: ...
    def removeCustomItemAt(self, position: PySide6.QtGui.QVector3D) -> None: ...
    def removeCustomItems(self) -> None: ...
    def renderToImage(self, msaaSamples: int = ..., imageSize: PySide6.QtCore.QSize = ...) -> PySide6.QtGui.QImage: ...
    def resizeEvent(self, event: PySide6.QtGui.QResizeEvent) -> None: ...
    def scene(self) -> PySide6.QtGraphs.Q3DScene: ...
    def selectedAxis(self) -> PySide6.QtGraphs.QAbstract3DAxis: ...
    def selectedCustomItem(self) -> PySide6.QtGraphs.QCustom3DItem: ...
    def selectedCustomItemIndex(self) -> int: ...
    def selectedElement(self) -> PySide6.QtGraphs.QAbstract3DGraph.ElementType: ...
    def selectedLabelIndex(self) -> int: ...
    def selectionMode(self) -> PySide6.QtGraphs.QAbstract3DGraph.SelectionFlag: ...
    def setActiveInputHandler(self, inputHandler: PySide6.QtGraphs.QAbstract3DInputHandler) -> None: ...
    def setActiveTheme(self, activeTheme: PySide6.QtGraphs.Q3DTheme) -> None: ...
    def setAspectRatio(self, ratio: float) -> None: ...
    def setHorizontalAspectRatio(self, ratio: float) -> None: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def setMargin(self, margin: float) -> None: ...
    def setMeasureFps(self, enable: bool) -> None: ...
    def setOptimizationHints(self, hints: PySide6.QtGraphs.QAbstract3DGraph.OptimizationHint) -> None: ...
    def setOrthoProjection(self, enable: bool) -> None: ...
    def setPolar(self, enable: bool) -> None: ...
    def setRadialLabelOffset(self, offset: float) -> None: ...
    def setReflection(self, enable: bool) -> None: ...
    def setReflectivity(self, reflectivity: float) -> None: ...
    def setSelectionMode(self, selectionMode: PySide6.QtGraphs.QAbstract3DGraph.SelectionFlag) -> None: ...
    def setShadowQuality(self, shadowQuality: PySide6.QtGraphs.QAbstract3DGraph.ShadowQuality) -> None: ...
    def shadowQuality(self) -> PySide6.QtGraphs.QAbstract3DGraph.ShadowQuality: ...
    def themes(self) -> List[PySide6.QtGraphs.Q3DTheme]: ...
    def wheelEvent(self, event: PySide6.QtGui.QWheelEvent) -> None: ...


class QAbstract3DInputHandler(PySide6.QtCore.QObject):

    inputViewChanged         : ClassVar[Signal] = ... # inputViewChanged(QAbstract3DInputHandler::InputView)
    positionChanged          : ClassVar[Signal] = ... # positionChanged(QPoint)
    sceneChanged             : ClassVar[Signal] = ... # sceneChanged(Q3DScene*)

    class InputView(enum.Enum):

        InputViewNone            : QAbstract3DInputHandler.InputView = ... # 0x0
        InputViewOnPrimary       : QAbstract3DInputHandler.InputView = ... # 0x1
        InputViewOnSecondary     : QAbstract3DInputHandler.InputView = ... # 0x2


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def handleSelection(self, position: PySide6.QtCore.QPoint) -> None: ...
    def inputPosition(self) -> PySide6.QtCore.QPoint: ...
    def inputView(self) -> PySide6.QtGraphs.QAbstract3DInputHandler.InputView: ...
    def mouseDoubleClickEvent(self, event: PySide6.QtGui.QMouseEvent) -> None: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint) -> None: ...
    def prevDistance(self) -> int: ...
    def previousInputPos(self) -> PySide6.QtCore.QPoint: ...
    def scene(self) -> PySide6.QtGraphs.Q3DScene: ...
    def setInputPosition(self, position: PySide6.QtCore.QPoint, forceSelection: bool = ...) -> None: ...
    def setInputView(self, inputView: PySide6.QtGraphs.QAbstract3DInputHandler.InputView) -> None: ...
    def setPrevDistance(self, distance: int) -> None: ...
    def setPreviousInputPos(self, position: PySide6.QtCore.QPoint) -> None: ...
    def setScene(self, scene: PySide6.QtGraphs.Q3DScene) -> None: ...
    def touchEvent(self, event: PySide6.QtGui.QTouchEvent) -> None: ...
    def wheelEvent(self, event: PySide6.QtGui.QWheelEvent) -> None: ...


class QAbstract3DSeries(PySide6.QtCore.QObject):

    baseColorChanged         : ClassVar[Signal] = ... # baseColorChanged(QColor)
    baseGradientChanged      : ClassVar[Signal] = ... # baseGradientChanged(QLinearGradient)
    colorStyleChanged        : ClassVar[Signal] = ... # colorStyleChanged(Q3DTheme::ColorStyle)
    itemLabelChanged         : ClassVar[Signal] = ... # itemLabelChanged(QString)
    itemLabelFormatChanged   : ClassVar[Signal] = ... # itemLabelFormatChanged(QString)
    itemLabelVisibilityChanged: ClassVar[Signal] = ... # itemLabelVisibilityChanged(bool)
    meshChanged              : ClassVar[Signal] = ... # meshChanged(QAbstract3DSeries::Mesh)
    meshRotationChanged      : ClassVar[Signal] = ... # meshRotationChanged(QQuaternion)
    meshSmoothChanged        : ClassVar[Signal] = ... # meshSmoothChanged(bool)
    multiHighlightColorChanged: ClassVar[Signal] = ... # multiHighlightColorChanged(QColor)
    multiHighlightGradientChanged: ClassVar[Signal] = ... # multiHighlightGradientChanged(QLinearGradient)
    nameChanged              : ClassVar[Signal] = ... # nameChanged(QString)
    singleHighlightColorChanged: ClassVar[Signal] = ... # singleHighlightColorChanged(QColor)
    singleHighlightGradientChanged: ClassVar[Signal] = ... # singleHighlightGradientChanged(QLinearGradient)
    userDefinedMeshChanged   : ClassVar[Signal] = ... # userDefinedMeshChanged(QString)
    visibilityChanged        : ClassVar[Signal] = ... # visibilityChanged(bool)

    class Mesh(enum.Enum):

        MeshUserDefined          : QAbstract3DSeries.Mesh = ... # 0x0
        MeshBar                  : QAbstract3DSeries.Mesh = ... # 0x1
        MeshCube                 : QAbstract3DSeries.Mesh = ... # 0x2
        MeshPyramid              : QAbstract3DSeries.Mesh = ... # 0x3
        MeshCone                 : QAbstract3DSeries.Mesh = ... # 0x4
        MeshCylinder             : QAbstract3DSeries.Mesh = ... # 0x5
        MeshBevelBar             : QAbstract3DSeries.Mesh = ... # 0x6
        MeshBevelCube            : QAbstract3DSeries.Mesh = ... # 0x7
        MeshSphere               : QAbstract3DSeries.Mesh = ... # 0x8
        MeshMinimal              : QAbstract3DSeries.Mesh = ... # 0x9
        MeshArrow                : QAbstract3DSeries.Mesh = ... # 0xa
        MeshPoint                : QAbstract3DSeries.Mesh = ... # 0xb

    class SeriesType(enum.Enum):

        SeriesTypeNone           : QAbstract3DSeries.SeriesType = ... # 0x0
        SeriesTypeBar            : QAbstract3DSeries.SeriesType = ... # 0x1
        SeriesTypeScatter        : QAbstract3DSeries.SeriesType = ... # 0x2
        SeriesTypeSurface        : QAbstract3DSeries.SeriesType = ... # 0x4


    def baseColor(self) -> PySide6.QtGui.QColor: ...
    def baseGradient(self) -> PySide6.QtGui.QLinearGradient: ...
    def colorStyle(self) -> PySide6.QtGraphs.Q3DTheme.ColorStyle: ...
    def isItemLabelVisible(self) -> bool: ...
    def isMeshSmooth(self) -> bool: ...
    def isVisible(self) -> bool: ...
    def itemLabel(self) -> str: ...
    def itemLabelFormat(self) -> str: ...
    def mesh(self) -> PySide6.QtGraphs.QAbstract3DSeries.Mesh: ...
    def meshRotation(self) -> PySide6.QtGui.QQuaternion: ...
    def multiHighlightColor(self) -> PySide6.QtGui.QColor: ...
    def multiHighlightGradient(self) -> PySide6.QtGui.QLinearGradient: ...
    def name(self) -> str: ...
    def setBaseColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBaseGradient(self, gradient: PySide6.QtGui.QLinearGradient) -> None: ...
    def setColorStyle(self, style: PySide6.QtGraphs.Q3DTheme.ColorStyle) -> None: ...
    def setItemLabelFormat(self, format: str) -> None: ...
    def setItemLabelVisible(self, visible: bool) -> None: ...
    def setMesh(self, mesh: PySide6.QtGraphs.QAbstract3DSeries.Mesh) -> None: ...
    def setMeshAxisAndAngle(self, axis: PySide6.QtGui.QVector3D, angle: float) -> None: ...
    def setMeshRotation(self, rotation: PySide6.QtGui.QQuaternion) -> None: ...
    def setMeshSmooth(self, enable: bool) -> None: ...
    def setMultiHighlightColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setMultiHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient) -> None: ...
    def setName(self, name: str) -> None: ...
    def setSingleHighlightColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setSingleHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient) -> None: ...
    def setUserDefinedMesh(self, fileName: str) -> None: ...
    def setVisible(self, visible: bool) -> None: ...
    def singleHighlightColor(self) -> PySide6.QtGui.QColor: ...
    def singleHighlightGradient(self) -> PySide6.QtGui.QLinearGradient: ...
    def type(self) -> PySide6.QtGraphs.QAbstract3DSeries.SeriesType: ...
    def userDefinedMesh(self) -> str: ...


class QAbstractDataProxy(PySide6.QtCore.QObject):

    class DataType(enum.Enum):

        DataTypeNone             : QAbstractDataProxy.DataType = ... # 0x0
        DataTypeBar              : QAbstractDataProxy.DataType = ... # 0x1
        DataTypeScatter          : QAbstractDataProxy.DataType = ... # 0x2
        DataTypeSurface          : QAbstractDataProxy.DataType = ... # 0x4


    def type(self) -> PySide6.QtGraphs.QAbstractDataProxy.DataType: ...


class QBar3DSeries(PySide6.QtGraphs.QAbstract3DSeries):

    dataProxyChanged         : ClassVar[Signal] = ... # dataProxyChanged(QBarDataProxy*)
    meshAngleChanged         : ClassVar[Signal] = ... # meshAngleChanged(float)
    rowColorsChanged         : ClassVar[Signal] = ... # rowColorsChanged(QList<QColor>)
    selectedBarChanged       : ClassVar[Signal] = ... # selectedBarChanged(QPoint)

    @overload
    def __init__(self, dataProxy: PySide6.QtGraphs.QBarDataProxy, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def dataProxy(self) -> PySide6.QtGraphs.QBarDataProxy: ...
    @staticmethod
    def invalidSelectionPosition() -> PySide6.QtCore.QPoint: ...
    def meshAngle(self) -> float: ...
    def rowColors(self) -> List[PySide6.QtGui.QColor]: ...
    def selectedBar(self) -> PySide6.QtCore.QPoint: ...
    def setDataProxy(self, proxy: PySide6.QtGraphs.QBarDataProxy) -> None: ...
    def setMeshAngle(self, angle: float) -> None: ...
    def setRowColors(self, colors: Sequence[PySide6.QtGui.QColor]) -> None: ...
    def setSelectedBar(self, position: PySide6.QtCore.QPoint) -> None: ...


class QBarDataItem(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: Union[PySide6.QtGraphs.QBarDataItem, float]) -> None: ...
    @overload
    def __init__(self, value: float) -> None: ...
    @overload
    def __init__(self, value: float, angle: float) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def createExtraData(self) -> None: ...
    def rotation(self) -> float: ...
    def setRotation(self, angle: float) -> None: ...
    def setValue(self, val: float) -> None: ...
    def value(self) -> float: ...


class QBarDataProxy(PySide6.QtGraphs.QAbstractDataProxy):

    arrayReset               : ClassVar[Signal] = ... # arrayReset()
    colCountChanged          : ClassVar[Signal] = ... # colCountChanged(int)
    columnLabelsChanged      : ClassVar[Signal] = ... # columnLabelsChanged()
    itemChanged              : ClassVar[Signal] = ... # itemChanged(int,int)
    rowCountChanged          : ClassVar[Signal] = ... # rowCountChanged(int)
    rowLabelsChanged         : ClassVar[Signal] = ... # rowLabelsChanged()
    rowsAdded                : ClassVar[Signal] = ... # rowsAdded(int,int)
    rowsChanged              : ClassVar[Signal] = ... # rowsChanged(int,int)
    rowsInserted             : ClassVar[Signal] = ... # rowsInserted(int,int)
    rowsRemoved              : ClassVar[Signal] = ... # rowsRemoved(int,int)
    seriesChanged            : ClassVar[Signal] = ... # seriesChanged(QBar3DSeries*)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    @overload
    def addRow(self, arg__1: Sequence[PySide6.QtGraphs.QBarDataItem]) -> int: ...
    @overload
    def addRow(self, arg__1: Sequence[PySide6.QtGraphs.QBarDataItem], arg__2: str) -> int: ...
    @overload
    def addRows(self, rows: List[List[PySide6.QtGraphs.QBarDataItem]]) -> int: ...
    @overload
    def addRows(self, rows: List[List[PySide6.QtGraphs.QBarDataItem]], labels: Sequence[str]) -> int: ...
    def array(self) -> List[List[PySide6.QtGraphs.QBarDataItem]]: ...
    def colCount(self) -> int: ...
    def columnLabels(self) -> List[str]: ...
    @overload
    def insertRow(self, arg__1: int, arg__2: Sequence[PySide6.QtGraphs.QBarDataItem]) -> None: ...
    @overload
    def insertRow(self, arg__1: int, arg__2: Sequence[PySide6.QtGraphs.QBarDataItem], arg__3: str) -> None: ...
    @overload
    def insertRows(self, rowIndex: int, rows: List[List[PySide6.QtGraphs.QBarDataItem]]) -> None: ...
    @overload
    def insertRows(self, rowIndex: int, rows: List[List[PySide6.QtGraphs.QBarDataItem]], labels: Sequence[str]) -> None: ...
    @overload
    def itemAt(self, position: PySide6.QtCore.QPoint) -> PySide6.QtGraphs.QBarDataItem: ...
    @overload
    def itemAt(self, rowIndex: int, columnIndex: int) -> PySide6.QtGraphs.QBarDataItem: ...
    def removeRows(self, rowIndex: int, removeCount: int, removeLabels: bool = ...) -> None: ...
    @overload
    def resetArray(self) -> None: ...
    @overload
    def resetArray(self, arg__1: List[List[PySide6.QtGraphs.QBarDataItem]]) -> None: ...
    @overload
    def resetArray(self, arg__1: List[List[PySide6.QtGraphs.QBarDataItem]], arg__2: Sequence[str], arg__3: Sequence[str]) -> None: ...
    def rowAt(self, rowIndex: int) -> List[PySide6.QtGraphs.QBarDataItem]: ...
    def rowCount(self) -> int: ...
    def rowLabels(self) -> List[str]: ...
    def series(self) -> PySide6.QtGraphs.QBar3DSeries: ...
    def setColumnLabels(self, labels: Sequence[str]) -> None: ...
    @overload
    def setItem(self, position: PySide6.QtCore.QPoint, item: Union[PySide6.QtGraphs.QBarDataItem, float]) -> None: ...
    @overload
    def setItem(self, rowIndex: int, columnIndex: int, item: Union[PySide6.QtGraphs.QBarDataItem, float]) -> None: ...
    @overload
    def setRow(self, arg__1: int, arg__2: Sequence[PySide6.QtGraphs.QBarDataItem]) -> None: ...
    @overload
    def setRow(self, arg__1: int, arg__2: Sequence[PySide6.QtGraphs.QBarDataItem], arg__3: str) -> None: ...
    def setRowLabels(self, labels: Sequence[str]) -> None: ...
    @overload
    def setRows(self, rowIndex: int, rows: List[List[PySide6.QtGraphs.QBarDataItem]]) -> None: ...
    @overload
    def setRows(self, rowIndex: int, rows: List[List[PySide6.QtGraphs.QBarDataItem]], labels: Sequence[str]) -> None: ...


class QCategory3DAxis(PySide6.QtGraphs.QAbstract3DAxis):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def labels(self) -> List[str]: ...
    def setLabels(self, labels: Sequence[str]) -> None: ...


class QCustom3DItem(PySide6.QtCore.QObject):

    meshFileChanged          : ClassVar[Signal] = ... # meshFileChanged(QString)
    needUpdate               : ClassVar[Signal] = ... # needUpdate()
    positionAbsoluteChanged  : ClassVar[Signal] = ... # positionAbsoluteChanged(bool)
    positionChanged          : ClassVar[Signal] = ... # positionChanged(QVector3D)
    rotationChanged          : ClassVar[Signal] = ... # rotationChanged(QQuaternion)
    scalingAbsoluteChanged   : ClassVar[Signal] = ... # scalingAbsoluteChanged(bool)
    scalingChanged           : ClassVar[Signal] = ... # scalingChanged(QVector3D)
    shadowCastingChanged     : ClassVar[Signal] = ... # shadowCastingChanged(bool)
    textureFileChanged       : ClassVar[Signal] = ... # textureFileChanged(QString)
    visibleChanged           : ClassVar[Signal] = ... # visibleChanged(bool)

    @overload
    def __init__(self, meshFile: str, position: PySide6.QtGui.QVector3D, scaling: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, texture: Union[PySide6.QtGui.QImage, str], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def isPositionAbsolute(self) -> bool: ...
    def isScalingAbsolute(self) -> bool: ...
    def isShadowCasting(self) -> bool: ...
    def isVisible(self) -> bool: ...
    def meshFile(self) -> str: ...
    def position(self) -> PySide6.QtGui.QVector3D: ...
    def rotation(self) -> PySide6.QtGui.QQuaternion: ...
    def scaling(self) -> PySide6.QtGui.QVector3D: ...
    def setMeshFile(self, meshFile: str) -> None: ...
    def setPosition(self, position: PySide6.QtGui.QVector3D) -> None: ...
    def setPositionAbsolute(self, positionAbsolute: bool) -> None: ...
    def setRotation(self, rotation: PySide6.QtGui.QQuaternion) -> None: ...
    def setRotationAxisAndAngle(self, axis: PySide6.QtGui.QVector3D, angle: float) -> None: ...
    def setScaling(self, scaling: PySide6.QtGui.QVector3D) -> None: ...
    def setScalingAbsolute(self, scalingAbsolute: bool) -> None: ...
    def setShadowCasting(self, enabled: bool) -> None: ...
    def setTextureFile(self, textureFile: str) -> None: ...
    def setTextureImage(self, textureImage: Union[PySide6.QtGui.QImage, str]) -> None: ...
    def setVisible(self, visible: bool) -> None: ...
    def textureFile(self) -> str: ...


class QCustom3DLabel(PySide6.QtGraphs.QCustom3DItem):

    backgroundColorChanged   : ClassVar[Signal] = ... # backgroundColorChanged(QColor)
    backgroundEnabledChanged : ClassVar[Signal] = ... # backgroundEnabledChanged(bool)
    borderEnabledChanged     : ClassVar[Signal] = ... # borderEnabledChanged(bool)
    facingCameraChanged      : ClassVar[Signal] = ... # facingCameraChanged(bool)
    fontChanged              : ClassVar[Signal] = ... # fontChanged(QFont)
    textChanged              : ClassVar[Signal] = ... # textChanged(QString)
    textColorChanged         : ClassVar[Signal] = ... # textColorChanged(QColor)

    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, text: str, font: Union[PySide6.QtGui.QFont, str, Sequence[str]], position: PySide6.QtGui.QVector3D, scaling: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def backgroundColor(self) -> PySide6.QtGui.QColor: ...
    def font(self) -> PySide6.QtGui.QFont: ...
    def isBackgroundEnabled(self) -> bool: ...
    def isBorderEnabled(self) -> bool: ...
    def isFacingCamera(self) -> bool: ...
    def setBackgroundColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setBackgroundEnabled(self, enabled: bool) -> None: ...
    def setBorderEnabled(self, enabled: bool) -> None: ...
    def setFacingCamera(self, enabled: bool) -> None: ...
    def setFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
    def setText(self, text: str) -> None: ...
    def setTextColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def text(self) -> str: ...
    def textColor(self) -> PySide6.QtGui.QColor: ...


class QCustom3DVolume(PySide6.QtGraphs.QCustom3DItem):

    alphaMultiplierChanged   : ClassVar[Signal] = ... # alphaMultiplierChanged(float)
    colorTableChanged        : ClassVar[Signal] = ... # colorTableChanged()
    drawSliceFramesChanged   : ClassVar[Signal] = ... # drawSliceFramesChanged(bool)
    drawSlicesChanged        : ClassVar[Signal] = ... # drawSlicesChanged(bool)
    preserveOpacityChanged   : ClassVar[Signal] = ... # preserveOpacityChanged(bool)
    sliceFrameColorChanged   : ClassVar[Signal] = ... # sliceFrameColorChanged(QColor)
    sliceFrameGapsChanged    : ClassVar[Signal] = ... # sliceFrameGapsChanged(QVector3D)
    sliceFrameThicknessesChanged: ClassVar[Signal] = ... # sliceFrameThicknessesChanged(QVector3D)
    sliceFrameWidthsChanged  : ClassVar[Signal] = ... # sliceFrameWidthsChanged(QVector3D)
    sliceIndexXChanged       : ClassVar[Signal] = ... # sliceIndexXChanged(int)
    sliceIndexYChanged       : ClassVar[Signal] = ... # sliceIndexYChanged(int)
    sliceIndexZChanged       : ClassVar[Signal] = ... # sliceIndexZChanged(int)
    textureDataChanged       : ClassVar[Signal] = ... # textureDataChanged(QList<uchar>*)
    textureDepthChanged      : ClassVar[Signal] = ... # textureDepthChanged(int)
    textureFormatChanged     : ClassVar[Signal] = ... # textureFormatChanged(QImage::Format)
    textureHeightChanged     : ClassVar[Signal] = ... # textureHeightChanged(int)
    textureWidthChanged      : ClassVar[Signal] = ... # textureWidthChanged(int)
    useHighDefShaderChanged  : ClassVar[Signal] = ... # useHighDefShaderChanged(bool)

    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, position: PySide6.QtGui.QVector3D, scaling: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, textureWidth: int, textureHeight: int, textureDepth: int, textureData: Sequence[int], textureFormat: PySide6.QtGui.QImage.Format, colorTable: Sequence[int], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def alphaMultiplier(self) -> float: ...
    def colorTable(self) -> List[int]: ...
    def createTextureData(self, images: Sequence[PySide6.QtGui.QImage]) -> List[int]: ...
    def drawSliceFrames(self) -> bool: ...
    def drawSlices(self) -> bool: ...
    def preserveOpacity(self) -> bool: ...
    def renderSlice(self, axis: PySide6.QtCore.Qt.Axis, index: int) -> PySide6.QtGui.QImage: ...
    def setAlphaMultiplier(self, mult: float) -> None: ...
    def setColorTable(self, colors: Sequence[int]) -> None: ...
    def setDrawSliceFrames(self, enable: bool) -> None: ...
    def setDrawSlices(self, enable: bool) -> None: ...
    def setPreserveOpacity(self, enable: bool) -> None: ...
    def setSliceFrameColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def setSliceFrameGaps(self, values: PySide6.QtGui.QVector3D) -> None: ...
    def setSliceFrameThicknesses(self, values: PySide6.QtGui.QVector3D) -> None: ...
    def setSliceFrameWidths(self, values: PySide6.QtGui.QVector3D) -> None: ...
    def setSliceIndexX(self, value: int) -> None: ...
    def setSliceIndexY(self, value: int) -> None: ...
    def setSliceIndexZ(self, value: int) -> None: ...
    def setSliceIndices(self, x: int, y: int, z: int) -> None: ...
    @overload
    def setSubTextureData(self, axis: PySide6.QtCore.Qt.Axis, index: int, data: bytes) -> None: ...
    @overload
    def setSubTextureData(self, axis: PySide6.QtCore.Qt.Axis, index: int, image: Union[PySide6.QtGui.QImage, str]) -> None: ...
    def setTextureData(self, arg__1: Sequence[int]) -> None: ...
    def setTextureDepth(self, value: int) -> None: ...
    def setTextureDimensions(self, width: int, height: int, depth: int) -> None: ...
    def setTextureFormat(self, format: PySide6.QtGui.QImage.Format) -> None: ...
    def setTextureHeight(self, value: int) -> None: ...
    def setTextureWidth(self, value: int) -> None: ...
    def setUseHighDefShader(self, enable: bool) -> None: ...
    def sliceFrameColor(self) -> PySide6.QtGui.QColor: ...
    def sliceFrameGaps(self) -> PySide6.QtGui.QVector3D: ...
    def sliceFrameThicknesses(self) -> PySide6.QtGui.QVector3D: ...
    def sliceFrameWidths(self) -> PySide6.QtGui.QVector3D: ...
    def sliceIndexX(self) -> int: ...
    def sliceIndexY(self) -> int: ...
    def sliceIndexZ(self) -> int: ...
    def textureData(self) -> List[int]: ...
    def textureDataWidth(self) -> int: ...
    def textureDepth(self) -> int: ...
    def textureFormat(self) -> PySide6.QtGui.QImage.Format: ...
    def textureHeight(self) -> int: ...
    def textureWidth(self) -> int: ...
    def useHighDefShader(self) -> bool: ...


class QHeightMapSurfaceDataProxy(PySide6.QtGraphs.QSurfaceDataProxy):

    autoScaleYChanged        : ClassVar[Signal] = ... # autoScaleYChanged(bool)
    heightMapChanged         : ClassVar[Signal] = ... # heightMapChanged(QImage)
    heightMapFileChanged     : ClassVar[Signal] = ... # heightMapFileChanged(QString)
    maxXValueChanged         : ClassVar[Signal] = ... # maxXValueChanged(float)
    maxYValueChanged         : ClassVar[Signal] = ... # maxYValueChanged(float)
    maxZValueChanged         : ClassVar[Signal] = ... # maxZValueChanged(float)
    minXValueChanged         : ClassVar[Signal] = ... # minXValueChanged(float)
    minYValueChanged         : ClassVar[Signal] = ... # minYValueChanged(float)
    minZValueChanged         : ClassVar[Signal] = ... # minZValueChanged(float)

    @overload
    def __init__(self, filename: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, image: Union[PySide6.QtGui.QImage, str], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def autoScaleY(self) -> bool: ...
    def handlePendingResolve(self) -> None: ...
    def heightMap(self) -> PySide6.QtGui.QImage: ...
    def heightMapFile(self) -> str: ...
    def maxXValue(self) -> float: ...
    def maxYValue(self) -> float: ...
    def maxZValue(self) -> float: ...
    def minXValue(self) -> float: ...
    def minYValue(self) -> float: ...
    def minZValue(self) -> float: ...
    def setAutoScaleY(self, enabled: bool) -> None: ...
    def setHeightMap(self, image: Union[PySide6.QtGui.QImage, str]) -> None: ...
    def setHeightMapFile(self, filename: str) -> None: ...
    def setMaxXValue(self, max: float) -> None: ...
    def setMaxYValue(self, max: float) -> None: ...
    def setMaxZValue(self, max: float) -> None: ...
    def setMinXValue(self, min: float) -> None: ...
    def setMinYValue(self, min: float) -> None: ...
    def setMinZValue(self, min: float) -> None: ...
    def setValueRanges(self, minX: float, maxX: float, minZ: float, maxZ: float) -> None: ...


class QIntList(object): ...


class QItemModelBarDataProxy(PySide6.QtGraphs.QBarDataProxy):

    autoColumnCategoriesChanged: ClassVar[Signal] = ... # autoColumnCategoriesChanged(bool)
    autoRowCategoriesChanged : ClassVar[Signal] = ... # autoRowCategoriesChanged(bool)
    columnCategoriesChanged  : ClassVar[Signal] = ... # columnCategoriesChanged()
    columnRoleChanged        : ClassVar[Signal] = ... # columnRoleChanged(QString)
    columnRolePatternChanged : ClassVar[Signal] = ... # columnRolePatternChanged(QRegularExpression)
    columnRoleReplaceChanged : ClassVar[Signal] = ... # columnRoleReplaceChanged(QString)
    itemModelChanged         : ClassVar[Signal] = ... # itemModelChanged(const QAbstractItemModel*)
    multiMatchBehaviorChanged: ClassVar[Signal] = ... # multiMatchBehaviorChanged(QItemModelBarDataProxy::MultiMatchBehavior)
    rotationRoleChanged      : ClassVar[Signal] = ... # rotationRoleChanged(QString)
    rotationRolePatternChanged: ClassVar[Signal] = ... # rotationRolePatternChanged(QRegularExpression)
    rotationRoleReplaceChanged: ClassVar[Signal] = ... # rotationRoleReplaceChanged(QString)
    rowCategoriesChanged     : ClassVar[Signal] = ... # rowCategoriesChanged()
    rowRoleChanged           : ClassVar[Signal] = ... # rowRoleChanged(QString)
    rowRolePatternChanged    : ClassVar[Signal] = ... # rowRolePatternChanged(QRegularExpression)
    rowRoleReplaceChanged    : ClassVar[Signal] = ... # rowRoleReplaceChanged(QString)
    useModelCategoriesChanged: ClassVar[Signal] = ... # useModelCategoriesChanged(bool)
    valueRoleChanged         : ClassVar[Signal] = ... # valueRoleChanged(QString)
    valueRolePatternChanged  : ClassVar[Signal] = ... # valueRolePatternChanged(QRegularExpression)
    valueRoleReplaceChanged  : ClassVar[Signal] = ... # valueRoleReplaceChanged(QString)

    class MultiMatchBehavior(enum.Enum):

        MMBFirst                 : QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x0
        MMBLast                  : QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x1
        MMBAverage               : QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x2
        MMBCumulative            : QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x3


    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, rotationRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, rotationRole: str, rowCategories: Sequence[str], columnCategories: Sequence[str], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, rowCategories: Sequence[str], columnCategories: Sequence[str], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, valueRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def autoColumnCategories(self) -> bool: ...
    def autoRowCategories(self) -> bool: ...
    def columnCategories(self) -> List[str]: ...
    def columnCategoryIndex(self, category: str) -> int: ...
    def columnRole(self) -> str: ...
    def columnRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def columnRoleReplace(self) -> str: ...
    def itemModel(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def multiMatchBehavior(self) -> PySide6.QtGraphs.QItemModelBarDataProxy.MultiMatchBehavior: ...
    def remap(self, rowRole: str, columnRole: str, valueRole: str, rotationRole: str, rowCategories: Sequence[str], columnCategories: Sequence[str]) -> None: ...
    def rotationRole(self) -> str: ...
    def rotationRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def rotationRoleReplace(self) -> str: ...
    def rowCategories(self) -> List[str]: ...
    def rowCategoryIndex(self, category: str) -> int: ...
    def rowRole(self) -> str: ...
    def rowRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def rowRoleReplace(self) -> str: ...
    def setAutoColumnCategories(self, enable: bool) -> None: ...
    def setAutoRowCategories(self, enable: bool) -> None: ...
    def setColumnCategories(self, categories: Sequence[str]) -> None: ...
    def setColumnRole(self, role: str) -> None: ...
    def setColumnRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setColumnRoleReplace(self, replace: str) -> None: ...
    def setItemModel(self, itemModel: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setMultiMatchBehavior(self, behavior: PySide6.QtGraphs.QItemModelBarDataProxy.MultiMatchBehavior) -> None: ...
    def setRotationRole(self, role: str) -> None: ...
    def setRotationRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setRotationRoleReplace(self, replace: str) -> None: ...
    def setRowCategories(self, categories: Sequence[str]) -> None: ...
    def setRowRole(self, role: str) -> None: ...
    def setRowRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setRowRoleReplace(self, replace: str) -> None: ...
    def setUseModelCategories(self, enable: bool) -> None: ...
    def setValueRole(self, role: str) -> None: ...
    def setValueRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setValueRoleReplace(self, replace: str) -> None: ...
    def useModelCategories(self) -> bool: ...
    def valueRole(self) -> str: ...
    def valueRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def valueRoleReplace(self) -> str: ...


class QItemModelScatterDataProxy(PySide6.QtGraphs.QScatterDataProxy):

    itemModelChanged         : ClassVar[Signal] = ... # itemModelChanged(const QAbstractItemModel*)
    rotationRoleChanged      : ClassVar[Signal] = ... # rotationRoleChanged(QString)
    rotationRolePatternChanged: ClassVar[Signal] = ... # rotationRolePatternChanged(QRegularExpression)
    rotationRoleReplaceChanged: ClassVar[Signal] = ... # rotationRoleReplaceChanged(QString)
    xPosRoleChanged          : ClassVar[Signal] = ... # xPosRoleChanged(QString)
    xPosRolePatternChanged   : ClassVar[Signal] = ... # xPosRolePatternChanged(QRegularExpression)
    xPosRoleReplaceChanged   : ClassVar[Signal] = ... # xPosRoleReplaceChanged(QString)
    yPosRoleChanged          : ClassVar[Signal] = ... # yPosRoleChanged(QString)
    yPosRolePatternChanged   : ClassVar[Signal] = ... # yPosRolePatternChanged(QRegularExpression)
    yPosRoleReplaceChanged   : ClassVar[Signal] = ... # yPosRoleReplaceChanged(QString)
    zPosRoleChanged          : ClassVar[Signal] = ... # zPosRoleChanged(QString)
    zPosRolePatternChanged   : ClassVar[Signal] = ... # zPosRolePatternChanged(QRegularExpression)
    zPosRoleReplaceChanged   : ClassVar[Signal] = ... # zPosRoleReplaceChanged(QString)

    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, xPosRole: str, yPosRole: str, zPosRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, xPosRole: str, yPosRole: str, zPosRole: str, rotationRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def itemModel(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def remap(self, xPosRole: str, yPosRole: str, zPosRole: str, rotationRole: str) -> None: ...
    def rotationRole(self) -> str: ...
    def rotationRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def rotationRoleReplace(self) -> str: ...
    def setItemModel(self, itemModel: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setRotationRole(self, role: str) -> None: ...
    def setRotationRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setRotationRoleReplace(self, replace: str) -> None: ...
    def setXPosRole(self, role: str) -> None: ...
    def setXPosRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setXPosRoleReplace(self, replace: str) -> None: ...
    def setYPosRole(self, role: str) -> None: ...
    def setYPosRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setYPosRoleReplace(self, replace: str) -> None: ...
    def setZPosRole(self, role: str) -> None: ...
    def setZPosRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setZPosRoleReplace(self, replace: str) -> None: ...
    def xPosRole(self) -> str: ...
    def xPosRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def xPosRoleReplace(self) -> str: ...
    def yPosRole(self) -> str: ...
    def yPosRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def yPosRoleReplace(self) -> str: ...
    def zPosRole(self) -> str: ...
    def zPosRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def zPosRoleReplace(self) -> str: ...


class QItemModelSurfaceDataProxy(PySide6.QtGraphs.QSurfaceDataProxy):

    autoColumnCategoriesChanged: ClassVar[Signal] = ... # autoColumnCategoriesChanged(bool)
    autoRowCategoriesChanged : ClassVar[Signal] = ... # autoRowCategoriesChanged(bool)
    columnCategoriesChanged  : ClassVar[Signal] = ... # columnCategoriesChanged()
    columnRoleChanged        : ClassVar[Signal] = ... # columnRoleChanged(QString)
    columnRolePatternChanged : ClassVar[Signal] = ... # columnRolePatternChanged(QRegularExpression)
    columnRoleReplaceChanged : ClassVar[Signal] = ... # columnRoleReplaceChanged(QString)
    itemModelChanged         : ClassVar[Signal] = ... # itemModelChanged(const QAbstractItemModel*)
    multiMatchBehaviorChanged: ClassVar[Signal] = ... # multiMatchBehaviorChanged(QItemModelSurfaceDataProxy::MultiMatchBehavior)
    rowCategoriesChanged     : ClassVar[Signal] = ... # rowCategoriesChanged()
    rowRoleChanged           : ClassVar[Signal] = ... # rowRoleChanged(QString)
    rowRolePatternChanged    : ClassVar[Signal] = ... # rowRolePatternChanged(QRegularExpression)
    rowRoleReplaceChanged    : ClassVar[Signal] = ... # rowRoleReplaceChanged(QString)
    useModelCategoriesChanged: ClassVar[Signal] = ... # useModelCategoriesChanged(bool)
    xPosRoleChanged          : ClassVar[Signal] = ... # xPosRoleChanged(QString)
    xPosRolePatternChanged   : ClassVar[Signal] = ... # xPosRolePatternChanged(QRegularExpression)
    xPosRoleReplaceChanged   : ClassVar[Signal] = ... # xPosRoleReplaceChanged(QString)
    yPosRoleChanged          : ClassVar[Signal] = ... # yPosRoleChanged(QString)
    yPosRolePatternChanged   : ClassVar[Signal] = ... # yPosRolePatternChanged(QRegularExpression)
    yPosRoleReplaceChanged   : ClassVar[Signal] = ... # yPosRoleReplaceChanged(QString)
    zPosRoleChanged          : ClassVar[Signal] = ... # zPosRoleChanged(QString)
    zPosRolePatternChanged   : ClassVar[Signal] = ... # zPosRolePatternChanged(QRegularExpression)
    zPosRoleReplaceChanged   : ClassVar[Signal] = ... # zPosRoleReplaceChanged(QString)

    class MultiMatchBehavior(enum.Enum):

        MMBFirst                 : QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x0
        MMBLast                  : QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x1
        MMBAverage               : QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x2
        MMBCumulativeY           : QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x3


    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, xPosRole: str, yPosRole: str, zPosRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, xPosRole: str, yPosRole: str, zPosRole: str, rowCategories: Sequence[str], columnCategories: Sequence[str], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, yPosRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, yPosRole: str, rowCategories: Sequence[str], columnCategories: Sequence[str], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, yPosRole: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def autoColumnCategories(self) -> bool: ...
    def autoRowCategories(self) -> bool: ...
    def columnCategories(self) -> List[str]: ...
    def columnCategoryIndex(self, category: str) -> int: ...
    def columnRole(self) -> str: ...
    def columnRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def columnRoleReplace(self) -> str: ...
    def itemModel(self) -> PySide6.QtCore.QAbstractItemModel: ...
    def multiMatchBehavior(self) -> PySide6.QtGraphs.QItemModelSurfaceDataProxy.MultiMatchBehavior: ...
    def remap(self, rowRole: str, columnRole: str, xPosRole: str, yPosRole: str, zPosRole: str, rowCategories: Sequence[str], columnCategories: Sequence[str]) -> None: ...
    def rowCategories(self) -> List[str]: ...
    def rowCategoryIndex(self, category: str) -> int: ...
    def rowRole(self) -> str: ...
    def rowRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def rowRoleReplace(self) -> str: ...
    def setAutoColumnCategories(self, enable: bool) -> None: ...
    def setAutoRowCategories(self, enable: bool) -> None: ...
    def setColumnCategories(self, categories: Sequence[str]) -> None: ...
    def setColumnRole(self, role: str) -> None: ...
    def setColumnRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setColumnRoleReplace(self, replace: str) -> None: ...
    def setItemModel(self, itemModel: PySide6.QtCore.QAbstractItemModel) -> None: ...
    def setMultiMatchBehavior(self, behavior: PySide6.QtGraphs.QItemModelSurfaceDataProxy.MultiMatchBehavior) -> None: ...
    def setRowCategories(self, categories: Sequence[str]) -> None: ...
    def setRowRole(self, role: str) -> None: ...
    def setRowRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setRowRoleReplace(self, replace: str) -> None: ...
    def setUseModelCategories(self, enable: bool) -> None: ...
    def setXPosRole(self, role: str) -> None: ...
    def setXPosRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setXPosRoleReplace(self, replace: str) -> None: ...
    def setYPosRole(self, role: str) -> None: ...
    def setYPosRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setYPosRoleReplace(self, replace: str) -> None: ...
    def setZPosRole(self, role: str) -> None: ...
    def setZPosRolePattern(self, pattern: Union[PySide6.QtCore.QRegularExpression, str]) -> None: ...
    def setZPosRoleReplace(self, replace: str) -> None: ...
    def useModelCategories(self) -> bool: ...
    def xPosRole(self) -> str: ...
    def xPosRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def xPosRoleReplace(self) -> str: ...
    def yPosRole(self) -> str: ...
    def yPosRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def yPosRoleReplace(self) -> str: ...
    def zPosRole(self) -> str: ...
    def zPosRolePattern(self) -> PySide6.QtCore.QRegularExpression: ...
    def zPosRoleReplace(self) -> str: ...


class QLogValue3DAxisFormatter(PySide6.QtGraphs.QValue3DAxisFormatter):

    autoSubGridChanged       : ClassVar[Signal] = ... # autoSubGridChanged(bool)
    baseChanged              : ClassVar[Signal] = ... # baseChanged(double)
    showEdgeLabelsChanged    : ClassVar[Signal] = ... # showEdgeLabelsChanged(bool)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def autoSubGrid(self) -> bool: ...
    def base(self) -> float: ...
    def createNewInstance(self) -> PySide6.QtGraphs.QValue3DAxisFormatter: ...
    def populateCopy(self, copy: PySide6.QtGraphs.QValue3DAxisFormatter) -> None: ...
    def positionAt(self, value: float) -> float: ...
    def recalculate(self) -> None: ...
    def setAutoSubGrid(self, enabled: bool) -> None: ...
    def setBase(self, base: float) -> None: ...
    def setShowEdgeLabels(self, enabled: bool) -> None: ...
    def showEdgeLabels(self) -> bool: ...
    def valueAt(self, position: float) -> float: ...


class QScatter3DSeries(PySide6.QtGraphs.QAbstract3DSeries):

    dataProxyChanged         : ClassVar[Signal] = ... # dataProxyChanged(QScatterDataProxy*)
    itemSizeChanged          : ClassVar[Signal] = ... # itemSizeChanged(float)
    selectedItemChanged      : ClassVar[Signal] = ... # selectedItemChanged(int)

    @overload
    def __init__(self, dataProxy: PySide6.QtGraphs.QScatterDataProxy, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def dataProxy(self) -> PySide6.QtGraphs.QScatterDataProxy: ...
    @staticmethod
    def invalidSelectionIndex() -> int: ...
    def itemSize(self) -> float: ...
    def selectedItem(self) -> int: ...
    def setDataProxy(self, proxy: PySide6.QtGraphs.QScatterDataProxy) -> None: ...
    def setItemSize(self, size: float) -> None: ...
    def setSelectedItem(self, index: int) -> None: ...


class QScatterDataItem(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: Union[PySide6.QtGraphs.QScatterDataItem, PySide6.QtGui.QVector3D]) -> None: ...
    @overload
    def __init__(self, position: PySide6.QtGui.QVector3D) -> None: ...
    @overload
    def __init__(self, position: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def createExtraData(self) -> None: ...
    def position(self) -> PySide6.QtGui.QVector3D: ...
    def rotation(self) -> PySide6.QtGui.QQuaternion: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D) -> None: ...
    def setRotation(self, rot: PySide6.QtGui.QQuaternion) -> None: ...
    def setX(self, value: float) -> None: ...
    def setY(self, value: float) -> None: ...
    def setZ(self, value: float) -> None: ...
    def x(self) -> float: ...
    def y(self) -> float: ...
    def z(self) -> float: ...


class QScatterDataProxy(PySide6.QtGraphs.QAbstractDataProxy):

    arrayReset               : ClassVar[Signal] = ... # arrayReset()
    itemCountChanged         : ClassVar[Signal] = ... # itemCountChanged(int)
    itemsAdded               : ClassVar[Signal] = ... # itemsAdded(int,int)
    itemsChanged             : ClassVar[Signal] = ... # itemsChanged(int,int)
    itemsInserted            : ClassVar[Signal] = ... # itemsInserted(int,int)
    itemsRemoved             : ClassVar[Signal] = ... # itemsRemoved(int,int)
    seriesChanged            : ClassVar[Signal] = ... # seriesChanged(QScatter3DSeries*)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def addItem(self, item: Union[PySide6.QtGraphs.QScatterDataItem, PySide6.QtGui.QVector3D]) -> int: ...
    def addItems(self, items: Sequence[PySide6.QtGraphs.QScatterDataItem]) -> int: ...
    def array(self) -> List[PySide6.QtGraphs.QScatterDataItem]: ...
    def insertItem(self, index: int, item: Union[PySide6.QtGraphs.QScatterDataItem, PySide6.QtGui.QVector3D]) -> None: ...
    def insertItems(self, index: int, items: Sequence[PySide6.QtGraphs.QScatterDataItem]) -> None: ...
    def itemAt(self, index: int) -> PySide6.QtGraphs.QScatterDataItem: ...
    def itemCount(self) -> int: ...
    def removeItems(self, index: int, removeCount: int) -> None: ...
    def resetArray(self, arg__1: Sequence[PySide6.QtGraphs.QScatterDataItem]) -> None: ...
    def series(self) -> PySide6.QtGraphs.QScatter3DSeries: ...
    def setItem(self, index: int, item: Union[PySide6.QtGraphs.QScatterDataItem, PySide6.QtGui.QVector3D]) -> None: ...
    def setItems(self, index: int, items: Sequence[PySide6.QtGraphs.QScatterDataItem]) -> None: ...


class QSurface3DSeries(PySide6.QtGraphs.QAbstract3DSeries):

    dataProxyChanged         : ClassVar[Signal] = ... # dataProxyChanged(QSurfaceDataProxy*)
    drawModeChanged          : ClassVar[Signal] = ... # drawModeChanged(QSurface3DSeries::DrawFlags)
    flatShadingEnabledChanged: ClassVar[Signal] = ... # flatShadingEnabledChanged(bool)
    flatShadingSupportedChanged: ClassVar[Signal] = ... # flatShadingSupportedChanged(bool)
    selectedPointChanged     : ClassVar[Signal] = ... # selectedPointChanged(QPoint)
    textureChanged           : ClassVar[Signal] = ... # textureChanged(QImage)
    textureFileChanged       : ClassVar[Signal] = ... # textureFileChanged(QString)
    wireframeColorChanged    : ClassVar[Signal] = ... # wireframeColorChanged(QColor)

    class DrawFlag(enum.Flag):

        DrawWireframe            : QSurface3DSeries.DrawFlag = ... # 0x1
        DrawSurface              : QSurface3DSeries.DrawFlag = ... # 0x2
        DrawSurfaceAndWireframe  : QSurface3DSeries.DrawFlag = ... # 0x3


    @overload
    def __init__(self, dataProxy: PySide6.QtGraphs.QSurfaceDataProxy, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def dataProxy(self) -> PySide6.QtGraphs.QSurfaceDataProxy: ...
    def drawMode(self) -> PySide6.QtGraphs.QSurface3DSeries.DrawFlag: ...
    @staticmethod
    def invalidSelectionPosition() -> PySide6.QtCore.QPoint: ...
    def isFlatShadingEnabled(self) -> bool: ...
    def isFlatShadingSupported(self) -> bool: ...
    def selectedPoint(self) -> PySide6.QtCore.QPoint: ...
    def setDataProxy(self, proxy: PySide6.QtGraphs.QSurfaceDataProxy) -> None: ...
    def setDrawMode(self, mode: PySide6.QtGraphs.QSurface3DSeries.DrawFlag) -> None: ...
    def setFlatShadingEnabled(self, enabled: bool) -> None: ...
    def setSelectedPoint(self, position: PySide6.QtCore.QPoint) -> None: ...
    def setTexture(self, texture: Union[PySide6.QtGui.QImage, str]) -> None: ...
    def setTextureFile(self, filename: str) -> None: ...
    def setWireframeColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
    def texture(self) -> PySide6.QtGui.QImage: ...
    def textureFile(self) -> str: ...
    def wireframeColor(self) -> PySide6.QtGui.QColor: ...


class QSurfaceDataItem(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: Union[PySide6.QtGraphs.QSurfaceDataItem, PySide6.QtGui.QVector3D]) -> None: ...
    @overload
    def __init__(self, position: PySide6.QtGui.QVector3D) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def createExtraData(self) -> None: ...
    def position(self) -> PySide6.QtGui.QVector3D: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D) -> None: ...
    def setX(self, value: float) -> None: ...
    def setY(self, value: float) -> None: ...
    def setZ(self, value: float) -> None: ...
    def x(self) -> float: ...
    def y(self) -> float: ...
    def z(self) -> float: ...


class QSurfaceDataProxy(PySide6.QtGraphs.QAbstractDataProxy):

    arrayReset               : ClassVar[Signal] = ... # arrayReset()
    columnCountChanged       : ClassVar[Signal] = ... # columnCountChanged(int)
    itemChanged              : ClassVar[Signal] = ... # itemChanged(int,int)
    rowCountChanged          : ClassVar[Signal] = ... # rowCountChanged(int)
    rowsAdded                : ClassVar[Signal] = ... # rowsAdded(int,int)
    rowsChanged              : ClassVar[Signal] = ... # rowsChanged(int,int)
    rowsInserted             : ClassVar[Signal] = ... # rowsInserted(int,int)
    rowsRemoved              : ClassVar[Signal] = ... # rowsRemoved(int,int)
    seriesChanged            : ClassVar[Signal] = ... # seriesChanged(QSurface3DSeries*)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def addRow(self, arg__1: Sequence[PySide6.QtGraphs.QSurfaceDataItem]) -> int: ...
    def addRows(self, rows: List[List[PySide6.QtGraphs.QSurfaceDataItem]]) -> int: ...
    def array(self) -> List[List[PySide6.QtGraphs.QSurfaceDataItem]]: ...
    def columnCount(self) -> int: ...
    def insertRow(self, arg__1: int, arg__2: Sequence[PySide6.QtGraphs.QSurfaceDataItem]) -> None: ...
    def insertRows(self, rowIndex: int, rows: List[List[PySide6.QtGraphs.QSurfaceDataItem]]) -> None: ...
    @overload
    def itemAt(self, position: PySide6.QtCore.QPoint) -> PySide6.QtGraphs.QSurfaceDataItem: ...
    @overload
    def itemAt(self, rowIndex: int, columnIndex: int) -> PySide6.QtGraphs.QSurfaceDataItem: ...
    def removeRows(self, rowIndex: int, removeCount: int) -> None: ...
    def resetArray(self, arg__1: List[List[PySide6.QtGraphs.QSurfaceDataItem]]) -> None: ...
    def resetArrayNp(self, x: float, deltaX: float, z: float, deltaZ: float, data: shibokensupport.signature.mapping.ArrayLikeVariable) -> None: ...
    def rowCount(self) -> int: ...
    def series(self) -> PySide6.QtGraphs.QSurface3DSeries: ...
    @overload
    def setItem(self, position: PySide6.QtCore.QPoint, item: Union[PySide6.QtGraphs.QSurfaceDataItem, PySide6.QtGui.QVector3D]) -> None: ...
    @overload
    def setItem(self, rowIndex: int, columnIndex: int, item: Union[PySide6.QtGraphs.QSurfaceDataItem, PySide6.QtGui.QVector3D]) -> None: ...
    def setRow(self, arg__1: int, arg__2: Sequence[PySide6.QtGraphs.QSurfaceDataItem]) -> None: ...
    def setRows(self, rowIndex: int, rows: List[List[PySide6.QtGraphs.QSurfaceDataItem]]) -> None: ...


class QTouch3DInputHandler(PySide6.QtGraphs.Q3DInputHandler):

    rotationEnabledChanged   : ClassVar[Signal] = ... # rotationEnabledChanged(bool)
    selectionEnabledChanged  : ClassVar[Signal] = ... # selectionEnabledChanged(bool)
    zoomAtTargetEnabledChanged: ClassVar[Signal] = ... # zoomAtTargetEnabledChanged(bool)
    zoomEnabledChanged       : ClassVar[Signal] = ... # zoomEnabledChanged(bool)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def touchEvent(self, event: PySide6.QtGui.QTouchEvent) -> None: ...


class QValue3DAxis(PySide6.QtGraphs.QAbstract3DAxis):

    formatterChanged         : ClassVar[Signal] = ... # formatterChanged(QValue3DAxisFormatter*)
    formatterDirty           : ClassVar[Signal] = ... # formatterDirty()
    labelFormatChanged       : ClassVar[Signal] = ... # labelFormatChanged(QString)
    reversedChanged          : ClassVar[Signal] = ... # reversedChanged(bool)
    segmentCountChanged      : ClassVar[Signal] = ... # segmentCountChanged(int)
    subSegmentCountChanged   : ClassVar[Signal] = ... # subSegmentCountChanged(int)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def formatter(self) -> PySide6.QtGraphs.QValue3DAxisFormatter: ...
    def gridPositionAt(self, gridLine: int) -> float: ...
    def gridSize(self) -> int: ...
    def labelFormat(self) -> str: ...
    def labelPositionAt(self, index: int) -> float: ...
    def positionAt(self, x: float) -> float: ...
    def recalculate(self) -> None: ...
    def reversed(self) -> bool: ...
    def segmentCount(self) -> int: ...
    def setFormatter(self, formatter: PySide6.QtGraphs.QValue3DAxisFormatter) -> None: ...
    def setLabelFormat(self, format: str) -> None: ...
    def setReversed(self, enable: bool) -> None: ...
    def setSegmentCount(self, count: int) -> None: ...
    def setSubSegmentCount(self, count: int) -> None: ...
    def stringForValue(self, x: float) -> str: ...
    def subGridPositionAt(self, gridLine: int) -> float: ...
    def subGridSize(self) -> int: ...
    def subSegmentCount(self) -> int: ...


class QValue3DAxisFormatter(PySide6.QtCore.QObject):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def allowNegatives(self) -> bool: ...
    def allowZero(self) -> bool: ...
    def axis(self) -> PySide6.QtGraphs.QValue3DAxis: ...
    def createNewInstance(self) -> PySide6.QtGraphs.QValue3DAxisFormatter: ...
    def gridPositions(self) -> List[float]: ...
    def labelPositions(self) -> List[float]: ...
    def labelStrings(self) -> List[str]: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def markDirty(self, labelsChange: bool = ...) -> None: ...
    def populateCopy(self, copy: PySide6.QtGraphs.QValue3DAxisFormatter) -> None: ...
    def positionAt(self, value: float) -> float: ...
    def recalculate(self) -> None: ...
    def setAllowNegatives(self, allow: bool) -> None: ...
    def setAllowZero(self, allow: bool) -> None: ...
    def setAxis(self, axis: PySide6.QtGraphs.QValue3DAxis) -> None: ...
    def setGridPositions(self, grid_positions: Sequence[float]) -> None: ...
    def setLabelPositions(self, label_positions: Sequence[float]) -> None: ...
    def setLabelStrings(self, label_strings: Sequence[str]) -> None: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def stringForValue(self, value: float, format: str) -> str: ...
    def subGridPositions(self) -> List[float]: ...
    def valueAt(self, position: float) -> float: ...


def qDefaultSurfaceFormat(antialias: bool) -> PySide6.QtGui.QSurfaceFormat: ...


# eof
