@echo off
echo Testing Cura and Uranium development environment
echo ==================================================

echo Activating Uranium virtual environment...
cd /d "C:\Mac\Home\Desktop\CuraProject\Uranium"
call build_windows\generators\virtual_python_env.bat

echo.
echo Testing Python version...
python --version

echo.
echo Testing key modules...
python -c "import PyQt6; print('PyQt6: OK')"
python -c "import numpy; print('numpy: OK')"
python -c "import scipy; print('scipy: OK')"
python -c "import cryptography; print('cryptography: OK')"

echo.
echo Testing Uranium modules...
python -c "import UM; print('UM: OK')"

echo.
echo Testing Cura modules...
cd /d "C:\Mac\Home\Desktop\CuraProject\Cura"
python -c "import cura; print('cura: OK')"

echo.
echo Environment test completed!
pause
