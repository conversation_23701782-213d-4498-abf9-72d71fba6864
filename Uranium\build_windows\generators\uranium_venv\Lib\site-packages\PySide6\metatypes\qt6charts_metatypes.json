[{"classes": [{"className": "GLXYSeriesDataManager", "object": true, "qualifiedClassName": "GLXYSeriesDataManager", "signals": [{"access": "public", "arguments": [{"name": "series", "type": "const QXYSeries*"}], "name": "seriesRemoved", "returnType": "void"}], "slots": [{"access": "public", "name": "cleanup", "returnType": "void"}, {"access": "public", "name": "handleSeriesPenChange", "returnType": "void"}, {"access": "public", "name": "handleSeriesOpenGLChange", "returnType": "void"}, {"access": "public", "name": "handleSeriesVisibilityChange", "returnType": "void"}, {"access": "public", "name": "handleScatterColorChange", "returnType": "void"}, {"access": "public", "name": "handleScatterMarkerSizeChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "glxyseriesdata_p.h", "outputRevision": 68}, {"classes": [{"className": "GLWidget", "object": true, "qualifiedClassName": "GLWidget", "slots": [{"access": "public", "name": "cleanup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "const QXYSeries*"}], "name": "cleanXYSeriesResources", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QOpenGLWidget"}, {"access": "protected", "name": "QOpenGLFunctions"}]}], "inputFile": "glwidget_p.h", "outputRevision": 68}, {"classes": [{"className": "AbstractBarChartItem", "object": true, "qualifiedClassName": "AbstractBarChartItem", "slots": [{"access": "public", "name": "handleDomainUpdated", "returnType": "void"}, {"access": "public", "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleLabelsVisibleChanged", "returnType": "void"}, {"access": "public", "name": "handleDataStructureChanged", "returnType": "void"}, {"access": "public", "name": "handleVisibleChanged", "returnType": "void"}, {"access": "public", "name": "handleOpacityChanged", "returnType": "void"}, {"access": "public", "name": "handleUpdatedBars", "returnType": "void"}, {"access": "public", "name": "handleLabelsPositionChanged", "returnType": "void"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "handleBarValueChange", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "handleBarValueAdd", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "handleBarValueRemove", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartItem"}]}], "inputFile": "abstractbarchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "AbstractDomain", "object": true, "qualifiedClassName": "AbstractDomain", "signals": [{"access": "public", "name": "updated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "rangeHorizontalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "rangeVerticalChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "handleVerticalAxisRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "handleHorizontalAxisRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reverse", "type": "bool"}], "name": "handleReverseXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reverse", "type": "bool"}], "name": "handleReverseYChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractdomain_p.h", "outputRevision": 68}, {"classes": [{"className": "AreaChartItem", "object": true, "qualifiedClassName": "AreaChartItem", "signals": [{"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}, {"name": "state", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "doubleClicked", "returnType": "void"}], "slots": [{"access": "public", "name": "handleUpdated", "returnType": "void"}, {"access": "public", "name": "handleDomainUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartItem"}]}], "inputFile": "areachartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "Bar", "object": true, "qualifiedClassName": "Bar", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "doubleClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QGraphicsRectItem"}]}], "inputFile": "bar_p.h", "outputRevision": 68}, {"classes": [{"className": "BarAnimation", "object": true, "qualifiedClassName": "BarAnimation", "superClasses": [{"access": "public", "name": "ChartAnimation"}]}], "inputFile": "baranimation_p.h", "outputRevision": 68}, {"classes": [{"className": "BarChartItem", "object": true, "qualifiedClassName": "BarChartItem", "slots": [{"access": "private", "name": "handleLabelsPositionChanged", "returnType": "void"}, {"access": "private", "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractBarChartItem"}]}], "inputFile": "barchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "BoxPlotAnimation", "object": true, "qualifiedClassName": "BoxPlotAnimation", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "boxplotanimation_p.h", "outputRevision": 68}, {"classes": [{"className": "BoxPlotChartItem", "object": true, "qualifiedClassName": "BoxPlotChartItem", "slots": [{"access": "public", "name": "handleSeriesVisibleChanged", "returnType": "void"}, {"access": "public", "name": "handleOpacityChanged", "returnType": "void"}, {"access": "public", "name": "handleDataStructureChanged", "returnType": "void"}, {"access": "public", "name": "handleDomainUpdated", "returnType": "void"}, {"access": "public", "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "name": "handleUpdatedBars", "returnType": "void"}, {"access": "public", "arguments": [{"name": "barSets", "type": "QList<QBoxSet*>"}], "name": "handleBoxsetRemove", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartItem"}]}], "inputFile": "boxplotchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "BoxWhiskers", "object": true, "qualifiedClassName": "BoxWhiskers", "signals": [{"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "boxset", "type": "QBoxSet*"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "doubleClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsObject"}]}], "inputFile": "boxwhiskers_p.h", "outputRevision": 68}, {"classes": [{"className": "BoxWhiskersAnimation", "object": true, "qualifiedClassName": "BoxWhiskersAnimation", "superClasses": [{"access": "public", "name": "ChartAnimation"}]}], "inputFile": "boxwhiskersanimation_p.h", "outputRevision": 68}, {"classes": [{"className": "Candlestick", "object": true, "qualifiedClassName": "Candlestick", "signals": [{"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "set", "type": "QCandlestickSet*"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "doubleClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsObject"}]}], "inputFile": "candlestick_p.h", "outputRevision": 68}, {"classes": [{"className": "CandlestickAnimation", "object": true, "qualifiedClassName": "CandlestickAnimation", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "candlestickanimation_p.h", "outputRevision": 68}, {"classes": [{"className": "CandlestickBodyWicksAnimation", "object": true, "qualifiedClassName": "CandlestickBodyWicksAnimation", "superClasses": [{"access": "public", "name": "ChartAnimation"}]}], "inputFile": "candlestickbodywicksanimation_p.h", "outputRevision": 68}, {"classes": [{"className": "CandlestickChartItem", "object": true, "qualifiedClassName": "CandlestickChartItem", "slots": [{"access": "public", "name": "handleDomainUpdated", "returnType": "void"}, {"access": "public", "name": "handleLayoutUpdated", "returnType": "void"}, {"access": "public", "name": "handleCandlesticksUpdated", "returnType": "void"}, {"access": "public", "name": "handleCandlestickSeriesChange", "returnType": "void"}, {"access": "private", "arguments": [{"name": "sets", "type": "QList<QCandlestickSet*>"}], "name": "handleCandlestickSetsAdd", "returnType": "void"}, {"access": "private", "arguments": [{"name": "sets", "type": "QList<QCandlestickSet*>"}], "name": "handleCandlestickSetsRemove", "returnType": "void"}, {"access": "private", "name": "handleDataStructureChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartItem"}]}], "inputFile": "candlestickchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "<PERSON><PERSON>ian<PERSON><PERSON><PERSON><PERSON><PERSON>", "interfaces": [[{"className": "QGraphicsLayoutItem", "id": "\"org.qt-project.Qt.QGraphicsLayoutItem\""}]], "object": true, "qualifiedClassName": "<PERSON><PERSON>ian<PERSON><PERSON><PERSON><PERSON><PERSON>", "slots": [{"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "handleShadesBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleShadesPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleGridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleMinorGridLineColorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartAxisElement"}]}], "inputFile": "cartesianchartaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartAnimation", "object": true, "qualifiedClassName": "ChartAnimation", "slots": [{"access": "public", "name": "startChartAnimation", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QVariantAnimation"}]}], "inputFile": "chartanimation_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartAxisElement", "object": true, "qualifiedClassName": "ChartAxisElement", "signals": [{"access": "public", "name": "clicked", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleArrowVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleGridVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleLabelsVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleShadesVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "int"}], "name": "handleLabelsAngleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "handleShadesBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleShadesPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleMinorGridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleGridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "handleLabelsBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "handleLabelsFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "handleTitleBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "handleTitleFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}], "name": "handleTitleTextChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleTitleVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "handleRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reverse", "type": "bool"}], "name": "handleReverseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleMinorArrowVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleMinorGridVisibleChanged", "returnType": "void"}, {"access": "public", "name": "handleLabelsPositionChanged", "returnType": "void"}, {"access": "public", "name": "handleTruncateLabelsChanged", "returnType": "void"}, {"access": "public", "name": "handleColorScaleSizeChanged", "returnType": "void"}, {"access": "public", "name": "handleColorScaleGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "oldValue", "type": "qreal"}, {"name": "newValue", "type": "qreal"}], "name": "valueLabelEdited", "returnType": "void"}, {"access": "public", "arguments": [{"name": "oldTime", "type": "QDateTime"}, {"name": "newTime", "type": "QDateTime"}], "name": "dateTimeLabelEdited", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartElement"}, {"access": "public", "name": "QGraphicsLayoutItem"}]}], "inputFile": "chartaxiselement_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartBarCategoryAxisX", "object": true, "qualifiedClassName": "ChartBarCategoryAxisX", "slots": [{"access": "public", "name": "handleCategoriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "HorizontalAxis"}]}], "inputFile": "chartbarcategoryaxisx_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartBarCategoryAxisY", "object": true, "qualifiedClassName": "ChartBarCategoryAxisY", "slots": [{"access": "public", "name": "handleCategoriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "VerticalAxis"}]}], "inputFile": "chartbarcategoryaxisy_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartCategoryAxisX", "object": true, "qualifiedClassName": "ChartCategoryAxisX", "slots": [{"access": "public", "name": "handleCategoriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "HorizontalAxis"}]}], "inputFile": "chartcategoryaxisx_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartCategoryAxisY", "object": true, "qualifiedClassName": "ChartCategoryAxisY", "slots": [{"access": "public", "name": "handleCategoriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "VerticalAxis"}]}], "inputFile": "chartcategoryaxisy_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartColorAxisX", "object": true, "qualifiedClassName": "ChartColorAxisX", "superClasses": [{"access": "public", "name": "HorizontalAxis"}]}], "inputFile": "chartcoloraxisx_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartColorAxisY", "object": true, "qualifiedClassName": "ChartColorAxisY", "superClasses": [{"access": "public", "name": "VerticalAxis"}]}], "inputFile": "chartcoloraxisy_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartDataSet", "object": true, "qualifiedClassName": "ChartDataSet", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "name": "axisAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "name": "axisRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "seriesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "seriesRemoved", "returnType": "void"}], "slots": [{"access": "public", "name": "reverseChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "chartdataset_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartDateTimeAxisX", "object": true, "qualifiedClassName": "ChartDateTimeAxisX", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "HorizontalAxis"}]}], "inputFile": "chartdatetimeaxisx_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartDateTimeAxisY", "object": true, "qualifiedClassName": "ChartDateTimeAxisY", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "VerticalAxis"}]}], "inputFile": "chartdatetimeaxisy_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartItem", "object": true, "qualifiedClassName": "ChartItem", "slots": [{"access": "public", "name": "handleDomainUpdated", "returnType": "void"}, {"access": "public", "name": "seriesPrivate", "returnType": "QAbstractSeriesPrivate*"}], "superClasses": [{"access": "public", "name": "ChartElement"}]}], "inputFile": "chartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartLogValueAxisX", "object": true, "qualifiedClassName": "ChartLogValueAxisX", "slots": [{"access": "private", "arguments": [{"name": "base", "type": "qreal"}], "name": "handleBaseChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "HorizontalAxis"}]}], "inputFile": "chartlogvalueaxisx_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartLogValueAxisY", "object": true, "qualifiedClassName": "ChartLogValueAxisY", "slots": [{"access": "private", "arguments": [{"name": "base", "type": "qreal"}], "name": "handleBaseChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "VerticalAxis"}]}], "inputFile": "chartlogvalueaxisy_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartPresenter", "object": true, "qualifiedClassName": "ChartPresenter", "signals": [{"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON>", "type": "QRectF"}], "name": "plotAreaChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "name": "handleAxisAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "name": "handleAxisRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "chartpresenter_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartThemeManager", "object": true, "qualifiedClassName": "ChartThemeManager", "slots": [{"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "name": "handleAxisAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "name": "handleAxisRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "chartthememanager_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartValueAxisX", "object": true, "qualifiedClassName": "ChartValueAxisX", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleMinorTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "interval", "type": "qreal"}], "name": "handleTickIntervalChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "anchor", "type": "qreal"}], "name": "handleTickAnchorChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "type", "type": "QValueAxis::TickType"}], "name": "handleTickTypeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "HorizontalAxis"}]}], "inputFile": "chartvalueaxisx_p.h", "outputRevision": 68}, {"classes": [{"className": "ChartValueAxisY", "object": true, "qualifiedClassName": "ChartValueAxisY", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleMinorTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "interval", "type": "qreal"}], "name": "handleTickIntervalChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "anchor", "type": "qreal"}], "name": "handleTickAnchorChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "type", "type": "QValueAxis::TickType"}], "name": "handleTickTypeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "VerticalAxis"}]}], "inputFile": "chartvalueaxisy_p.h", "outputRevision": 68}, {"classes": [{"className": "DateTimeAxisLabel", "object": true, "qualifiedClassName": "DateTimeAxisLabel", "signals": [{"access": "public", "arguments": [{"name": "oldDateTime", "type": "QDateTime"}, {"name": "newDateTime", "type": "QDateTime"}], "name": "dateTimeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "EditableAxisLabel"}]}], "inputFile": "datetimeaxislabel_p.h", "outputRevision": 68}, {"classes": [{"className": "EditableAxisLabel", "object": true, "qualifiedClassName": "EditableAxisLabel", "superClasses": [{"access": "public", "name": "QGraphicsTextItem"}]}], "inputFile": "editableaxislabel_p.h", "outputRevision": 68}, {"classes": [{"className": "HorizontalBarChartItem", "object": true, "qualifiedClassName": "HorizontalBarChartItem", "superClasses": [{"access": "public", "name": "AbstractBarChartItem"}]}], "inputFile": "horizontalbarchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "HorizontalPercentBarChartItem", "object": true, "qualifiedClassName": "HorizontalPercentBarChartItem", "superClasses": [{"access": "public", "name": "AbstractBarChartItem"}]}], "inputFile": "horizontalpercentbarchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "HorizontalStackedBarChartItem", "object": true, "qualifiedClassName": "HorizontalStackedBarChartItem", "superClasses": [{"access": "public", "name": "AbstractBarChartItem"}]}], "inputFile": "horizontalstackedbarchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "LegendMarkerItem", "interfaces": [[{"className": "QGraphicsLayoutItem", "id": "\"org.qt-project.Qt.QGraphicsLayoutItem\""}]], "object": true, "qualifiedClassName": "LegendMarkerItem", "signals": [{"access": "public", "name": "markerRectChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsObject"}, {"access": "public", "name": "QGraphicsLayoutItem"}]}], "inputFile": "legendmarkeritem_p.h", "outputRevision": 68}, {"classes": [{"className": "LegendScroller", "object": true, "qualifiedClassName": "LegendScroller", "slots": [{"access": "private", "arguments": [{"name": "attached", "type": "bool"}], "name": "handleDetached", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLegend"}, {"access": "public", "name": "<PERSON><PERSON><PERSON>"}]}], "inputFile": "legendscroller_p.h", "outputRevision": 68}, {"classes": [{"className": "LineChartItem", "interfaces": [[{"className": "QGraphicsItem", "id": "\"org.qt-project.Qt.QGraphicsItem\""}]], "object": true, "qualifiedClassName": "LineChartItem", "slots": [{"access": "public", "name": "handleSeriesUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "XYChart"}]}], "inputFile": "linechartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "LogXLogYDomain", "object": true, "qualifiedClassName": "LogXLogYDomain", "slots": [{"access": "public", "arguments": [{"name": "baseY", "type": "qreal"}], "name": "handleVerticalAxisBaseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "baseX", "type": "qreal"}], "name": "handleHorizontalAxisBaseChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractDomain"}]}], "inputFile": "logxlogydomain_p.h", "outputRevision": 68}, {"classes": [{"className": "LogXLogYPolarDomain", "object": true, "qualifiedClassName": "LogXLogYPolarDomain", "slots": [{"access": "public", "arguments": [{"name": "baseY", "type": "qreal"}], "name": "handleVerticalAxisBaseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "baseX", "type": "qreal"}], "name": "handleHorizontalAxisBaseChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarDomain"}]}], "inputFile": "logxlogypolardomain_p.h", "outputRevision": 68}, {"classes": [{"className": "LogXYDomain", "object": true, "qualifiedClassName": "LogXYDomain", "slots": [{"access": "public", "arguments": [{"name": "baseX", "type": "qreal"}], "name": "handleHorizontalAxisBaseChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractDomain"}]}], "inputFile": "logxydomain_p.h", "outputRevision": 68}, {"classes": [{"className": "LogXYPolarDomain", "object": true, "qualifiedClassName": "LogXYPolarDomain", "slots": [{"access": "public", "arguments": [{"name": "baseX", "type": "qreal"}], "name": "handleHorizontalAxisBaseChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarDomain"}]}], "inputFile": "logxypolardomain_p.h", "outputRevision": 68}, {"classes": [{"className": "PercentBarChartItem", "object": true, "qualifiedClassName": "PercentBarChartItem", "slots": [{"access": "private", "name": "handleLabelsPositionChanged", "returnType": "void"}, {"access": "private", "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractBarChartItem"}]}], "inputFile": "percentbarchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "PieAnimation", "object": true, "qualifiedClassName": "PieAnimation", "superClasses": [{"access": "public", "name": "ChartAnimation"}]}], "inputFile": "pieanimation_p.h", "outputRevision": 68}, {"classes": [{"className": "PieChartItem", "object": true, "qualifiedClassName": "PieChartItem", "slots": [{"access": "public", "name": "handleDomainUpdated", "returnType": "void"}, {"access": "public", "name": "updateLayout", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "name": "handleSlicesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "name": "handleSlicesRemoved", "returnType": "void"}, {"access": "public", "name": "handleSliceChanged", "returnType": "void"}, {"access": "public", "name": "handleSeriesVisibleChanged", "returnType": "void"}, {"access": "public", "name": "handleOpacityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "animation", "type": "PieAnimation*"}], "name": "setAnimation", "returnType": "void"}, {"access": "public", "name": "animation", "returnType": "ChartAnimation*"}, {"access": "public", "name": "cleanup", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartItem"}]}], "inputFile": "piechartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "PieSliceItem", "object": true, "qualifiedClassName": "PieSliceItem", "signals": [{"access": "public", "arguments": [{"name": "buttons", "type": "Qt::Mouse<PERSON><PERSON><PERSON>"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttons", "type": "Qt::Mouse<PERSON><PERSON><PERSON>"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttons", "type": "Qt::Mouse<PERSON><PERSON><PERSON>"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttons", "type": "Qt::Mouse<PERSON><PERSON><PERSON>"}], "name": "doubleClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsObject"}]}], "inputFile": "piesliceitem_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartAxis", "interfaces": [[{"className": "QGraphicsLayoutItem", "id": "\"org.qt-project.Qt.QGraphicsLayoutItem\""}]], "object": true, "qualifiedClassName": "PolarChartAxis", "slots": [{"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "handleShadesBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleShadesPenChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartAxisElement"}]}], "inputFile": "polarchartaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartAxisAngular", "object": true, "qualifiedClassName": "PolarChartAxisAngular", "slots": [{"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleGridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleMinorGridLineColorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxis"}]}], "inputFile": "polarchartaxisangular_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartAxisRadial", "object": true, "qualifiedClassName": "PolarChartAxisRadial", "slots": [{"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorArrowPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "handleMinorGridPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleGridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleMinorGridLineColorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxis"}]}], "inputFile": "polarchartaxisradial_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartCategoryAxisAngular", "object": true, "qualifiedClassName": "PolarChartCategoryAxisAngular", "slots": [{"access": "public", "name": "handleCategoriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisAngular"}]}], "inputFile": "polarchartcategoryaxisangular_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartCategoryAxisRadial", "object": true, "qualifiedClassName": "PolarChartCategoryAxisRadial", "slots": [{"access": "public", "name": "handleCategoriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisRadial"}]}], "inputFile": "polarchartcategoryaxisradial_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartDateTimeAxisAngular", "object": true, "qualifiedClassName": "PolarChartDateTimeAxisAngular", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisAngular"}]}], "inputFile": "polarchartdatetimeaxisangular_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartDateTimeAxisRadial", "object": true, "qualifiedClassName": "PolarChartDateTimeAxisRadial", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisRadial"}]}], "inputFile": "polarchartdatetimeaxisradial_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartLogValueAxisAngular", "object": true, "qualifiedClassName": "PolarChartLogValueAxisAngular", "slots": [{"access": "private", "arguments": [{"name": "base", "type": "qreal"}], "name": "handleBaseChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisAngular"}]}], "inputFile": "polarchartlogvalueaxisangular_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartLogValueAxisRadial", "object": true, "qualifiedClassName": "PolarChartLogValueAxisRadial", "slots": [{"access": "private", "arguments": [{"name": "base", "type": "qreal"}], "name": "handleBaseChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisRadial"}]}], "inputFile": "polarchartlogvalueaxisradial_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartValueAxisAngular", "object": true, "qualifiedClassName": "PolarChartValueAxisAngular", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleMinorTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisAngular"}]}], "inputFile": "polarchartvalueaxisangular_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarChartValueAxisRadial", "object": true, "qualifiedClassName": "PolarChartValueAxisRadial", "slots": [{"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "tick", "type": "int"}], "name": "handleMinorTickCountChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "format", "type": "QString"}], "name": "handleLabelFormatChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarChartAxisRadial"}]}], "inputFile": "polarchartvalueaxisradial_p.h", "outputRevision": 68}, {"classes": [{"className": "PolarDomain", "object": true, "qualifiedClassName": "PolarDomain", "superClasses": [{"access": "public", "name": "AbstractDomain"}]}], "inputFile": "polardomain_p.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractAxis", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "lineVisible", "notify": "lineVisibleChanged", "read": "isLineVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLineVisible"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "linePen", "notify": "linePenChanged", "read": "linePen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setLinePen"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "color", "notify": "colorChanged", "read": "linePenColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLinePenColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "labelsVisible", "notify": "labelsVisibleChanged", "read": "labelsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLabelsVisible"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "labelsBrush", "notify": "labelsBrushChanged", "read": "labelsBrush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setLabelsBrush"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "labelsAngle", "notify": "labelsAngleChanged", "read": "labelsAngle", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLabelsAngle"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "labelsFont", "notify": "labelsFontChanged", "read": "labelsFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setLabelsFont"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "labelsColor", "notify": "labelsColorChanged", "read": "labelsColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelsColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "labelsTruncated", "notify": "labelsTruncatedChanged", "read": "labelsTruncated", "required": false, "revision": 1538, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "truncateLabels", "notify": "truncateLabelsChanged", "read": "truncateLabels", "required": false, "revision": 1538, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTruncateLabels"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "gridVisible", "notify": "gridVisibleChanged", "read": "isGridLineVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGridLineVisible"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "gridLinePen", "notify": "gridLinePenChanged", "read": "gridLinePen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setGridLinePen"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "minorGridVisible", "notify": "minorGridVisibleChanged", "read": "isMinorGridLineVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMinorGridLineVisible"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "minorGridLinePen", "notify": "minorGridLinePenChanged", "read": "minorGridLinePen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setMinorGridLinePen"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "gridLineColor", "notify": "gridLineColorChanged", "read": "gridLineColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setGridLineColor"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "minorGridLineColor", "notify": "minorGridLineColorChanged", "read": "minorGridLineColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setMinorGridLineColor"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "shadesVisible", "notify": "shadesVisibleChanged", "read": "shadesVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShadesVisible"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "shadesColor", "notify": "shadesColorChanged", "read": "shadesColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setShadesColor"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "shadesBorderColor", "notify": "shadesBorderColorChanged", "read": "shadesBorderColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setShadesBorderColor"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "shadesPen", "notify": "shadesPenChanged", "read": "shadesPen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setShadesPen"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "shadesBrush", "notify": "shadesBrushChanged", "read": "shadesBrush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setShadesBrush"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "titleText", "notify": "titleTextChanged", "read": "titleText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitleText"}, {"constant": false, "designable": true, "final": false, "index": 23, "name": "titleBrush", "notify": "titleBrushChanged", "read": "titleBrush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setTitleBrush"}, {"constant": false, "designable": true, "final": false, "index": 24, "name": "titleVisible", "notify": "titleVisibleChanged", "read": "isTitleVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTitleVisible"}, {"constant": false, "designable": true, "final": false, "index": 25, "name": "titleFont", "notify": "titleFontChanged", "read": "titleFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setTitleFont"}, {"constant": false, "designable": true, "final": false, "index": 26, "name": "orientation", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::Orientation", "user": false}, {"constant": false, "designable": true, "final": false, "index": 27, "name": "alignment", "read": "alignment", "required": false, "scriptable": true, "stored": true, "type": "Qt::Alignment", "user": false}, {"constant": false, "designable": true, "final": false, "index": 28, "name": "reverse", "notify": "reverseChanged", "read": "isReverse", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReverse"}], "qualifiedClassName": "QAbstractAxis", "signals": [{"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "visibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "linePenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "lineVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "labelsVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "labelsBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QFont"}], "name": "labelsFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "int"}], "name": "labelsAngleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "gridLinePenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "gridVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "minorGridVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "minorGridLinePenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "gridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "minorGridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "labelsColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}], "name": "titleTextChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "titleBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "titleVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "titleFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "shadesVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "shadesColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "shadesBorderColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "shadesPenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brush", "type": "QBrush"}], "name": "shadesBrushChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reverse", "type": "bool"}], "name": "reverseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "editable", "type": "bool"}], "name": "labelsEditableChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "labelsTruncated", "type": "bool"}], "name": "labelsTruncatedChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "truncateLabels", "type": "bool"}], "name": "truncateLabelsChanged", "returnType": "void", "revision": 1538}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractaxis.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractAxisPrivate", "object": true, "qualifiedClassName": "QAbstractAxisPrivate", "signals": [{"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "rangeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "handleRangeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractBarSeries", "enums": [{"isClass": false, "isFlag": false, "name": "LabelsPosition", "values": ["LabelsCenter", "LabelsInsideEnd", "LabelsInsideBase", "LabelsOutsideEnd"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "labelsVisible", "notify": "labelsVisibleChanged", "read": "isLabelsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLabelsVisible"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelsFormat", "notify": "labelsFormatChanged", "read": "labelsFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setLabelsFormat"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "labelsPosition", "notify": "labelsPositionChanged", "read": "labelsPosition", "required": false, "scriptable": true, "stored": true, "type": "LabelsPosition", "user": false, "write": "setLabelsPosition"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "labelsAngle", "notify": "labelsAngleChanged", "read": "labelsAngle", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setLabelsAngle"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "labelsPrecision", "notify": "labelsPrecisionChanged", "read": "labelsPrecision", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLabelsPrecision"}], "qualifiedClassName": "QAbstractBarSeries", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "countChanged", "returnType": "void"}, {"access": "public", "name": "labelsVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "labelsFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QAbstractBarSeries::LabelsPosition"}], "name": "labelsPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "qreal"}], "name": "labelsAngleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "precision", "type": "int"}], "name": "labelsPrecisionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBarSet*>"}], "name": "barsetsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBarSet*>"}], "name": "barsetsRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeries"}]}], "inputFile": "qabstractbarseries.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractBarSeriesPrivate", "object": true, "qualifiedClassName": "QAbstractBarSeriesPrivate", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "updatedBars", "returnType": "void"}, {"access": "public", "name": "updatedLayout", "returnType": "void"}, {"access": "public", "name": "restructuredBars", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "labelsVisibleChanged", "returnType": "void"}, {"access": "public", "name": "visibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "set<PERSON><PERSON><PERSON>Changed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "setValueAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}, {"name": "barset", "type": "QBarSet*"}], "name": "setValueRemoved", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "index", "type": "int"}], "name": "handleSetValueChange", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleSetValueAdd", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleSetValueRemove", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeriesPrivate"}]}], "inputFile": "qabstractbarseries_p.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractSeries", "enums": [{"isClass": false, "isFlag": false, "name": "SeriesType", "values": ["SeriesTypeLine", "SeriesTypeArea", "SeriesTypeBar", "SeriesTypeStackedBar", "SeriesTypePercentBar", "SeriesTypePie", "SeriesTypeScatter", "SeriesTypeSpline", "SeriesTypeHorizontalBar", "SeriesTypeHorizontalStackedBar", "SeriesTypeHorizontalPercentBar", "SeriesTypeBoxPlot", "SeriesTypeCandlestick"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "opacity", "notify": "opacityChanged", "read": "opacity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setOpacity"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "SeriesType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "useOpenGL", "notify": "useOpenGLChanged", "read": "useOpenGL", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseOpenGL"}], "qualifiedClassName": "QAbstractSeries", "signals": [{"access": "public", "name": "nameChanged", "returnType": "void"}, {"access": "public", "name": "visibleChanged", "returnType": "void"}, {"access": "public", "name": "opacityChanged", "returnType": "void"}, {"access": "public", "name": "useOpenGLChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractseries.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractSeriesPrivate", "object": true, "qualifiedClassName": "QAbstractSeriesPrivate", "signals": [{"access": "public", "name": "countChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractseries_p.h", "outputRevision": 68}, {"classes": [{"className": "QAreaLegendMarker", "object": true, "qualifiedClassName": "QAreaLegendMarker", "superClasses": [{"access": "public", "name": "QLegendMarker"}]}], "inputFile": "qarealegendmarker.h", "outputRevision": 68}, {"classes": [{"className": "QAreaLegendMarkerPrivate", "object": true, "qualifiedClassName": "QAreaLegendMarkerPrivate", "slots": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLegendMarkerPrivate"}]}], "inputFile": "qarealegendmarker_p.h", "outputRevision": 68}, {"classes": [{"className": "QAreaSeries", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "upperSeries", "read": "upperSeries", "required": false, "scriptable": true, "stored": true, "type": "QLineSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "lowerSeries", "read": "lowerSeries", "required": false, "scriptable": true, "stored": true, "type": "QLineSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "borderColor", "notify": "borderColorChanged", "read": "borderColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBorderColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pointLabelsFormat", "notify": "pointLabelsFormatChanged", "read": "pointLabelsFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPointLabelsFormat"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pointLabelsVisible", "notify": "pointLabelsVisibilityChanged", "read": "pointLabelsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPointLabelsVisible"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pointLabelsFont", "notify": "pointLabelsFontChanged", "read": "pointLabelsFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setPointLabelsFont"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "pointLabelsColor", "notify": "pointLabelsColorChanged", "read": "pointLabelsColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setPointLabelsColor"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "point<PERSON><PERSON><PERSON><PERSON><PERSON>ping", "notify": "pointLabelsClippingChanged", "read": "point<PERSON><PERSON><PERSON><PERSON><PERSON>ping", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPointLabelsClipping"}], "qualifiedClassName": "QAreaSeries", "signals": [{"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}, {"name": "state", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "selected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "borderColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "pointLabelsFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "pointLabelsVisibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "pointLabelsFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "pointLabelsColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clipping", "type": "bool"}], "name": "pointLabelsClippingChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeries"}]}], "inputFile": "qareaseries.h", "outputRevision": 68}, {"classes": [{"className": "QAreaSeriesPrivate", "object": true, "qualifiedClassName": "QAreaSeriesPrivate", "signals": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeriesPrivate"}]}], "inputFile": "qareaseries_p.h", "outputRevision": 68}, {"classes": [{"className": "QBarCategoryAxis", "methods": [{"access": "public", "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "categories", "notify": "categoriesChanged", "read": "categories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setCategories"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "min", "notify": "minC<PERSON>ed", "read": "min", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setMin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "max", "notify": "max<PERSON><PERSON>ed", "read": "max", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setMax"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QBarCategoryAxis", "signals": [{"access": "public", "name": "categoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "QString"}], "name": "minC<PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "max", "type": "QString"}], "name": "max<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "QString"}, {"name": "max", "type": "QString"}], "name": "rangeChanged", "returnType": "void"}, {"access": "public", "name": "countChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAxis"}]}], "inputFile": "qbarcategoryaxis.h", "outputRevision": 68}, {"classes": [{"className": "QBarCategoryAxisPrivate", "object": true, "qualifiedClassName": "QBarCategoryAxisPrivate", "superClasses": [{"access": "public", "name": "QAbstractAxisPrivate"}]}], "inputFile": "qbarcategoryaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "QBarLegendMarker", "object": true, "qualifiedClassName": "QBarLegendMarker", "superClasses": [{"access": "public", "name": "QLegendMarker"}]}], "inputFile": "qbarlegendmarker.h", "outputRevision": 68}, {"classes": [{"className": "QBarLegendMarkerPrivate", "object": true, "qualifiedClassName": "QBarLegendMarkerPrivate", "slots": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLegendMarkerPrivate"}]}], "inputFile": "qbarlegendmarker_p.h", "outputRevision": 68}, {"classes": [{"className": "QBarModelMapper", "object": true, "qualifiedClassName": "QBarModelMapper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbarmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QBarModelMapperPrivate", "object": true, "qualifiedClassName": "QBarModelMapperPrivate", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "name": "modelUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "Qt::Orientation"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "name": "modelHeaderDataUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsRemoved", "returnType": "void"}, {"access": "public", "name": "handleModelDestroyed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBarSet*>"}], "name": "barSetsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBarSet*>"}], "name": "barSetsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "valuesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "valuesRemoved", "returnType": "void"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "bar<PERSON><PERSON>ueChanged", "returnType": "void"}, {"access": "public", "name": "handleSeriesDestroyed", "returnType": "void"}, {"access": "public", "name": "initializeBarFromModel", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbarmodelmapper_p.h", "outputRevision": 68}, {"classes": [{"className": "QBarSeries", "object": true, "qualifiedClassName": "QBarSeries", "superClasses": [{"access": "public", "name": "QAbstractBarSeries"}]}], "inputFile": "qbarseries.h", "outputRevision": 68}, {"classes": [{"className": "QBarSet", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "label", "notify": "labelChanged", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pen", "notify": "penChanged", "read": "pen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setPen"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelBrush", "notify": "labelBrushChanged", "read": "labelBrush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setLabelBrush"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "labelFont", "notify": "labelFontChanged", "read": "labelFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setLabelFont"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "borderColor", "notify": "borderColorChanged", "read": "borderColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBorderColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "labelColor", "notify": "labelColorChanged", "read": "labelColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelColor"}], "qualifiedClassName": "QBarSet", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "index", "type": "int"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "penChanged", "returnType": "void"}, {"access": "public", "name": "brushChanged", "returnType": "void"}, {"access": "public", "name": "labelChanged", "returnType": "void"}, {"access": "public", "name": "labelBrushChanged", "returnType": "void"}, {"access": "public", "name": "labelFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "borderColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "labelColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "selectedColorChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "valuesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "valuesRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "indexes", "type": "QList<int>"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void", "revision": 1538}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbarset.h", "outputRevision": 68}, {"classes": [{"className": "QBarSetPrivate", "object": true, "qualifiedClassName": "QBarSetPrivate", "signals": [{"access": "public", "name": "updatedBars", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "valueAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "valueRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbarset_p.h", "outputRevision": 68}, {"classes": [{"className": "QBoxPlotLegendMarker", "object": true, "qualifiedClassName": "QBoxPlotLegendMarker", "superClasses": [{"access": "public", "name": "QLegendMarker"}]}], "inputFile": "qboxplotlegendmarker.h", "outputRevision": 68}, {"classes": [{"className": "QBoxPlotLegendMarkerPrivate", "object": true, "qualifiedClassName": "QBoxPlotLegendMarkerPrivate", "slots": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLegendMarkerPrivate"}]}], "inputFile": "qboxplotlegendmarker_p.h", "outputRevision": 68}, {"classes": [{"className": "QBoxPlotModelMapper", "object": true, "qualifiedClassName": "QBoxPlotModelMapper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qboxplotmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QBoxPlotModelMapperPrivate", "object": true, "qualifiedClassName": "QBoxPlotModelMapperPrivate", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "name": "modelUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "Qt::Orientation"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "name": "modelHeaderDataUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsRemoved", "returnType": "void"}, {"access": "public", "name": "handleModelDestroyed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBoxSet*>"}], "name": "boxSetsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBoxSet*>"}], "name": "boxSetsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "boxValueChanged", "returnType": "void"}, {"access": "public", "name": "handleSeriesDestroyed", "returnType": "void"}, {"access": "public", "name": "initializeBoxFromModel", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qboxplotmodelmapper_p.h", "outputRevision": 68}, {"classes": [{"className": "QBoxPlotSeries", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "boxOutlineVisible", "notify": "boxOutlineVisibilityChanged", "read": "boxOutlineVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBoxOutlineVisible"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "boxWidth", "notify": "boxWidthChanged", "read": "boxWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBoxWidth"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pen", "notify": "penChanged", "read": "pen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setPen"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "count", "notify": "countChanged", "read": "count", "required": false, "revision": 512, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QBoxPlotSeries", "signals": [{"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "boxset", "type": "QBoxSet*"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "countChanged", "returnType": "void"}, {"access": "public", "name": "penChanged", "returnType": "void"}, {"access": "public", "name": "brushChanged", "returnType": "void"}, {"access": "public", "name": "boxOutlineVisibilityChanged", "returnType": "void"}, {"access": "public", "name": "boxWidthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBoxSet*>"}], "name": "boxsetsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QBoxSet*>"}], "name": "boxsetsRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeries"}]}], "inputFile": "qboxplotseries.h", "outputRevision": 68}, {"classes": [{"className": "QBoxPlotSeriesPrivate", "object": true, "qualifiedClassName": "QBoxPlotSeriesPrivate", "signals": [{"access": "public", "name": "updated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBoxSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBoxSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBoxSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "barset", "type": "QBoxSet*"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "updatedBoxes", "returnType": "void"}, {"access": "public", "name": "updatedLayout", "returnType": "void"}, {"access": "public", "name": "restructuredBoxes", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesChange", "returnType": "void"}, {"access": "private", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesRemove", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeriesPrivate"}]}], "inputFile": "qboxplotseries_p.h", "outputRevision": 68}, {"classes": [{"className": "QBoxSet", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pen", "notify": "penChanged", "read": "pen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setPen"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}], "qualifiedClassName": "QBoxSet", "signals": [{"access": "public", "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "name": "pressed", "returnType": "void"}, {"access": "public", "name": "released", "returnType": "void"}, {"access": "public", "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "penChanged", "returnType": "void"}, {"access": "public", "name": "brushChanged", "returnType": "void"}, {"access": "public", "name": "valuesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "valueChanged", "returnType": "void"}, {"access": "public", "name": "cleared", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qboxset.h", "outputRevision": 68}, {"classes": [{"className": "QBoxSetPrivate", "object": true, "qualifiedClassName": "QBoxSetPrivate", "signals": [{"access": "public", "name": "restructuredBox", "returnType": "void"}, {"access": "public", "name": "updatedBox", "returnType": "void"}, {"access": "public", "name": "updatedLayout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qboxset_p.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickLegendMarker", "object": true, "qualifiedClassName": "QCandlestickLegendMarker", "superClasses": [{"access": "public", "name": "QLegendMarker"}]}], "inputFile": "qcandlesticklegendmarker.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickLegendMarkerPrivate", "object": true, "qualifiedClassName": "QCandlestickLegendMarkerPrivate", "slots": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLegendMarkerPrivate"}]}], "inputFile": "qcandlesticklegendmarker_p.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QCandlestickSeries*", "user": false, "write": "setSeries"}], "qualifiedClassName": "QCandlestickModelMapper", "signals": [{"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "seriesReplaced", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcandlestickmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickModelMapperPrivate", "object": true, "qualifiedClassName": "QCandlestickModelMapperPrivate", "signals": [{"access": "public", "name": "timestampChanged", "returnType": "void"}, {"access": "public", "name": "openChanged", "returnType": "void"}, {"access": "public", "name": "highChanged", "returnType": "void"}, {"access": "public", "name": "lowChanged", "returnType": "void"}, {"access": "public", "name": "closeChanged", "returnType": "void"}, {"access": "public", "name": "firstSetSectionChanged", "returnType": "void"}, {"access": "public", "name": "lastSetSectionChanged", "returnType": "void"}], "slots": [{"access": "private", "name": "initializeCandlestickFromModel", "returnType": "void"}, {"access": "private", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "name": "modelDataUpdated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "orientation", "type": "Qt::Orientation"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "name": "modelHeaderDataUpdated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsInserted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsRemoved", "returnType": "void"}, {"access": "private", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsInserted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsRemoved", "returnType": "void"}, {"access": "private", "name": "modelDestroyed", "returnType": "void"}, {"access": "private", "arguments": [{"name": "sets", "type": "QList<QCandlestickSet*>"}], "name": "candlestickSetsAdded", "returnType": "void"}, {"access": "private", "arguments": [{"name": "sets", "type": "QList<QCandlestickSet*>"}], "name": "candlestickSetsRemoved", "returnType": "void"}, {"access": "private", "name": "candlestickSetChanged", "returnType": "void"}, {"access": "private", "name": "seriesDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcandlestickmodelmapper_p.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickSeries", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maximumColumnWidth", "notify": "maximumColumnWidthChanged", "read": "maximumColumnWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximumColumnWidth"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "minimumColumnWidth", "notify": "minimumColumnWidthChanged", "read": "minimumColumnWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMinimumColumnWidth"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "bodyWidth", "notify": "bodyWidthC<PERSON>ed", "read": "bodyWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "bodyOutlineVisible", "notify": "bodyOutlineVisibilityChanged", "read": "bodyOutlineVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBodyOutlineVisible"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "capsWidth", "notify": "capsWidthChanged", "read": "capsWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setCapsWidth"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "capsVisible", "notify": "capsVisibilityChanged", "read": "capsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setCapsVisible"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "increasingColor", "notify": "increasingColorChanged", "read": "increasingColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setIncreasingColor"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "decreasingColor", "notify": "decreasingColorChanged", "read": "decreasingColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDecreasingColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "pen", "notify": "penChanged", "read": "pen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setPen"}], "qualifiedClassName": "QCandlestickSeries", "signals": [{"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "set", "type": "QCandlestickSet*"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QCandlestickSet*>"}], "name": "candlestickSetsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sets", "type": "QList<QCandlestickSet*>"}], "name": "candlestickSetsRemoved", "returnType": "void"}, {"access": "public", "name": "countChanged", "returnType": "void"}, {"access": "public", "name": "maximumColumnWidthChanged", "returnType": "void"}, {"access": "public", "name": "minimumColumnWidthChanged", "returnType": "void"}, {"access": "public", "name": "bodyWidthC<PERSON>ed", "returnType": "void"}, {"access": "public", "name": "bodyOutlineVisibilityChanged", "returnType": "void"}, {"access": "public", "name": "capsWidthChanged", "returnType": "void"}, {"access": "public", "name": "capsVisibilityChanged", "returnType": "void"}, {"access": "public", "name": "increasingColorChanged", "returnType": "void"}, {"access": "public", "name": "decreasingColorChanged", "returnType": "void"}, {"access": "public", "name": "brushChanged", "returnType": "void"}, {"access": "public", "name": "penChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeries"}]}], "inputFile": "qcandlestickseries.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickSeriesPrivate", "object": true, "qualifiedClassName": "QCandlestickSeriesPrivate", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "set", "type": "QCandlestickSet*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "set", "type": "QCandlestickSet*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "set", "type": "QCandlestickSet*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "set", "type": "QCandlestickSet*"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "updated", "returnType": "void"}, {"access": "public", "name": "updatedLayout", "returnType": "void"}, {"access": "public", "name": "updatedCandlesticks", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesChange", "returnType": "void"}, {"access": "private", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesRemove", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeriesPrivate"}]}], "inputFile": "qcandlestickseries_p.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickSet", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "timestamp", "notify": "timestampChanged", "read": "timestamp", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTimestamp"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "open", "notify": "openChanged", "read": "open", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "high", "notify": "highChanged", "read": "high", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHigh"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "low", "notify": "lowChanged", "read": "low", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setLow"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "close", "notify": "closeChanged", "read": "close", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setClose"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pen", "notify": "penChanged", "read": "pen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setPen"}], "qualifiedClassName": "QCandlestickSet", "signals": [{"access": "public", "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "name": "pressed", "returnType": "void"}, {"access": "public", "name": "released", "returnType": "void"}, {"access": "public", "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "timestampChanged", "returnType": "void"}, {"access": "public", "name": "openChanged", "returnType": "void"}, {"access": "public", "name": "highChanged", "returnType": "void"}, {"access": "public", "name": "lowChanged", "returnType": "void"}, {"access": "public", "name": "closeChanged", "returnType": "void"}, {"access": "public", "name": "brushChanged", "returnType": "void"}, {"access": "public", "name": "penChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcandlestickset.h", "outputRevision": 68}, {"classes": [{"className": "QCandlestickSetPrivate", "object": true, "qualifiedClassName": "QCandlestickSetPrivate", "signals": [{"access": "public", "name": "updatedLayout", "returnType": "void"}, {"access": "public", "name": "updatedCandlestick", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcandlestickset_p.h", "outputRevision": 68}, {"classes": [{"className": "QCategoryAxis", "enums": [{"isClass": false, "isFlag": false, "name": "AxisLabelsPosition", "values": ["AxisLabelsPositionCenter", "AxisLabelsPositionOnValue"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "startValue", "read": "startValue", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setStartValue"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "count", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "categoriesLabels", "read": "categoriesLabels", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelsPosition", "notify": "labelsPositionChanged", "read": "labelsPosition", "required": false, "scriptable": true, "stored": true, "type": "AxisLabelsPosition", "user": false, "write": "setLabelsPosition"}], "qualifiedClassName": "QCategoryAxis", "signals": [{"access": "public", "name": "categoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QCategoryAxis::AxisLabelsPosition"}], "name": "labelsPositionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QValueAxis"}]}], "inputFile": "qcategoryaxis.h", "outputRevision": 68}, {"classes": [{"className": "QCategoryAxisPrivate", "object": true, "qualifiedClassName": "QCategoryAxisPrivate", "superClasses": [{"access": "public", "name": "QValueAxisPrivate"}]}], "inputFile": "qcategoryaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "Q<PERSON>hart", "enums": [{"isClass": false, "isFlag": false, "name": "ChartType", "values": ["ChartTypeUndefined", "ChartTypeCartesian", "ChartTypePolar"]}, {"isClass": false, "isFlag": false, "name": "ChartTheme", "values": ["ChartThemeLight", "ChartThemeBlueCerulean", "ChartThemeDark", "ChartThemeBrownSand", "ChartThemeBlueNcs", "ChartThemeHighContrast", "ChartThemeBlueIcy", "ChartThemeQt"]}, {"isClass": false, "isFlag": false, "name": "AnimationOption", "values": ["NoAnimation", "GridAxisAnimations", "SeriesAnimations", "AllAnimations"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "theme", "read": "theme", "required": false, "scriptable": true, "stored": true, "type": "QChart::ChartT<PERSON>e", "user": false, "write": "setTheme"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "title", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "backgroundVisible", "read": "isBackgroundVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackgroundVisible"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "dropShadowEnabled", "read": "isDropShadowEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDropShadowEnabled"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "backgroundRoundness", "read": "backgroundRoundness", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBackgroundRoundness"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "animationOptions", "read": "animationOptions", "required": false, "scriptable": true, "stored": true, "type": "QChart::AnimationOptions", "user": false, "write": "setAnimationOptions"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "animationDuration", "read": "animationDuration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAnimationDuration"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "animationEasingCurve", "read": "animationEasingCurve", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setAnimationEasingCurve"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "margins", "read": "margins", "required": false, "scriptable": true, "stored": true, "type": "<PERSON><PERSON><PERSON><PERSON>", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "chartType", "read": "chartType", "required": false, "scriptable": true, "stored": true, "type": "QChart::ChartType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "plotAreaBackgroundVisible", "read": "isPlotAreaBackgroundVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPlotAreaBackgroundVisible"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "localizeNumbers", "read": "localizeNumbers", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLocalizeNumbers"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "locale", "read": "locale", "required": false, "scriptable": true, "stored": true, "type": "QLocale", "user": false, "write": "setLocale"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "<PERSON><PERSON><PERSON>", "notify": "plotAreaChanged", "read": "<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false, "write": "setPlotArea"}], "qualifiedClassName": "Q<PERSON>hart", "signals": [{"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON>", "type": "QRectF"}], "name": "plotAreaChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsWidget"}]}], "inputFile": "qchart.h", "outputRevision": 68}, {"classes": [{"className": "QChartView", "enums": [{"isClass": false, "isFlag": false, "name": "RubberBand", "values": ["NoRubberBand", "VerticalRubberBand", "HorizontalRubberBand", "RectangleRubberBand", "ClickThroughRubberBand"]}], "object": true, "qualifiedClassName": "QChartView", "superClasses": [{"access": "public", "name": "QGraphicsView"}]}], "inputFile": "qchartview.h", "outputRevision": 68}, {"classes": [{"className": "QColorAxis", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "tickCount", "notify": "tickCountChanged", "read": "tickCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTickCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "min", "notify": "minC<PERSON>ed", "read": "min", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "max", "notify": "max<PERSON><PERSON>ed", "read": "max", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMax"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "size", "notify": "sizeChanged", "read": "size", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setSize"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "autoRange", "notify": "autoRangeChanged", "read": "autoRange", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRange"}], "qualifiedClassName": "QColorAxis", "signals": [{"access": "public", "arguments": [{"name": "min", "type": "qreal"}], "name": "minC<PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "max", "type": "qreal"}], "name": "max<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tickCount", "type": "int"}], "name": "tickCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "gradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "qreal"}], "name": "sizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "autoRange", "type": "bool"}], "name": "autoRangeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAxis"}]}], "inputFile": "qcoloraxis.h", "outputRevision": 68}, {"classes": [{"className": "QColorAxisPrivate", "object": true, "qualifiedClassName": "QColorAxisPrivate", "superClasses": [{"access": "public", "name": "QAbstractAxisPrivate"}]}], "inputFile": "qcoloraxis_p.h", "outputRevision": 68}, {"classes": [{"className": "QDateTimeAxis", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "tickCount", "notify": "tickCountChanged", "read": "tickCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTickCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "min", "notify": "minC<PERSON>ed", "read": "min", "required": false, "scriptable": true, "stored": true, "type": "QDateTime", "user": false, "write": "setMin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "max", "notify": "max<PERSON><PERSON>ed", "read": "max", "required": false, "scriptable": true, "stored": true, "type": "QDateTime", "user": false, "write": "setMax"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "format", "notify": "formatChanged", "read": "format", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setFormat"}], "qualifiedClassName": "QDateTimeAxis", "signals": [{"access": "public", "arguments": [{"name": "min", "type": "QDateTime"}], "name": "minC<PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "max", "type": "QDateTime"}], "name": "max<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "QDateTime"}, {"name": "max", "type": "QDateTime"}], "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "formatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tick", "type": "int"}], "name": "tickCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAxis"}]}], "inputFile": "qdatetimeaxis.h", "outputRevision": 68}, {"classes": [{"className": "QDateTimeAxisPrivate", "object": true, "qualifiedClassName": "QDateTimeAxisPrivate", "superClasses": [{"access": "public", "name": "QAbstractAxisPrivate"}]}], "inputFile": "qdatetimeaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "QHBarModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QAbstractBarSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "firstBarSetRow", "notify": "firstBarSetRowChanged", "read": "firstBarSetRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstBarSetRow"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "lastBarSetRow", "notify": "lastBarSetRowChanged", "read": "lastBarSetRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLastBarSetRow"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstColumn", "notify": "firstColumnChanged", "read": "firstColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstColumn"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "columnCount", "notify": "columnCountChanged", "read": "columnCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColumnCount"}], "qualifiedClassName": "QHBarModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "firstBarSetRowChanged", "returnType": "void"}, {"access": "public", "name": "lastBarSetRowChanged", "returnType": "void"}, {"access": "public", "name": "firstColumnChanged", "returnType": "void"}, {"access": "public", "name": "columnCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBarModelMapper"}]}], "inputFile": "qhbarmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QHBoxPlotModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QBoxPlotSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "firstBoxSetRow", "notify": "firstBoxSetRowChanged", "read": "firstBoxSetRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstBoxSetRow"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "lastBoxSetRow", "notify": "lastBoxSetRowChanged", "read": "lastBoxSetRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLastBoxSetRow"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstColumn", "notify": "firstColumnChanged", "read": "firstColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstColumn"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "columnCount", "notify": "columnCountChanged", "read": "columnCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColumnCount"}], "qualifiedClassName": "QHBoxPlotModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "firstBoxSetRowChanged", "returnType": "void"}, {"access": "public", "name": "lastBoxSetRowChanged", "returnType": "void"}, {"access": "public", "name": "firstColumnChanged", "returnType": "void"}, {"access": "public", "name": "columnCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBoxPlotModelMapper"}]}], "inputFile": "qhboxplotmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QHCandlestickModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "timestampColumn", "notify": "timestampColumnChanged", "read": "timestampColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTimestampColumn"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "openColumn", "notify": "openColumnChanged", "read": "openColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setOpenColumn"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "highColumn", "notify": "highColumnChanged", "read": "highColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHighColumn"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "lowColumn", "notify": "lowColumnChanged", "read": "lowColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLowColumn"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "closeColumn", "notify": "closeColumnChanged", "read": "closeColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCloseColumn"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "firstSetRow", "notify": "firstSetRowChanged", "read": "firstSetRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstSetRow"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "lastSetRow", "notify": "lastSetRowChanged", "read": "lastSetRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLastSetRow"}], "qualifiedClassName": "QHCandlestickModelMapper", "signals": [{"access": "public", "name": "timestampColumnChanged", "returnType": "void"}, {"access": "public", "name": "openColumnChanged", "returnType": "void"}, {"access": "public", "name": "highColumnChanged", "returnType": "void"}, {"access": "public", "name": "lowColumnChanged", "returnType": "void"}, {"access": "public", "name": "closeColumnChanged", "returnType": "void"}, {"access": "public", "name": "firstSetRowChanged", "returnType": "void"}, {"access": "public", "name": "lastSetRowChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCandlestickModelMapper"}]}], "inputFile": "qhcandlestickmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QHorizontalBarSeries", "object": true, "qualifiedClassName": "QHorizontalBarSeries", "superClasses": [{"access": "public", "name": "QAbstractBarSeries"}]}], "inputFile": "qhorizontalbarseries.h", "outputRevision": 68}, {"classes": [{"className": "QHorizontalPercentBarSeries", "object": true, "qualifiedClassName": "QHorizontalPercentBarSeries", "superClasses": [{"access": "public", "name": "QAbstractBarSeries"}]}], "inputFile": "qhorizontalpercentbarseries.h", "outputRevision": 68}, {"classes": [{"className": "QHorizontalStackedBarSeries", "object": true, "qualifiedClassName": "QHorizontalStackedBarSeries", "superClasses": [{"access": "public", "name": "QAbstractBarSeries"}]}], "inputFile": "qhorizontalstackedbarseries.h", "outputRevision": 68}, {"classes": [{"className": "QHPieModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QPieSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "valuesRow", "notify": "valuesRowChanged", "read": "valuesRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setValuesRow"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelsRow", "notify": "labelsRowChanged", "read": "labelsRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLabelsRow"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstColumn", "notify": "firstColumnChanged", "read": "firstColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstColumn"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "columnCount", "notify": "columnCountChanged", "read": "columnCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColumnCount"}], "qualifiedClassName": "QHPieModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "valuesRowChanged", "returnType": "void"}, {"access": "public", "name": "labelsRowChanged", "returnType": "void"}, {"access": "public", "name": "firstColumnChanged", "returnType": "void"}, {"access": "public", "name": "columnCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPieModelMapper"}]}], "inputFile": "qhpiemodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QHXYModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QXYSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "xRow", "notify": "xRowChanged", "read": "xRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setXRow"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "yRow", "notify": "yRowChanged", "read": "yRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setYRow"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstColumn", "notify": "firstColumnChanged", "read": "firstColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstColumn"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "columnCount", "notify": "columnCountChanged", "read": "columnCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColumnCount"}], "qualifiedClassName": "QHXYModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "xRowChanged", "returnType": "void"}, {"access": "public", "name": "yRowChanged", "returnType": "void"}, {"access": "public", "name": "firstColumnChanged", "returnType": "void"}, {"access": "public", "name": "columnCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QXYModelMapper"}]}], "inputFile": "qhxymodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QLegend", "enums": [{"isClass": false, "isFlag": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>er<PERSON>hapeRectangle", "MarkerShapeCircle", "MarkerShapeFromSeries", "MarkerShapeRotatedRectangle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkerShapeStar", "MarkerShapePentagon"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "alignment", "read": "alignment", "required": false, "scriptable": true, "stored": true, "type": "Qt::Alignment", "user": false, "write": "setAlignment"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "backgroundVisible", "notify": "backgroundVisibleChanged", "read": "isBackgroundVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackgroundVisible"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "borderColor", "notify": "borderColorChanged", "read": "borderColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBorderColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "labelColor", "notify": "labelColorChanged", "read": "labelColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "reverseMarkers", "notify": "reverseMarkersChanged", "read": "reverseMarkers", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReverseMarkers"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "showToolTips", "notify": "showToolTipsChanged", "read": "showToolTips", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShowToolTips"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "markerShape", "notify": "markerShapeChanged", "read": "markerShape", "required": false, "scriptable": true, "stored": true, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user": false, "write": "setMarkerShape"}], "qualifiedClassName": "QLegend", "signals": [{"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "backgroundVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "borderColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "labelColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reverseMarkers", "type": "bool"}], "name": "reverseMarkersChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "showToolTips", "type": "bool"}], "name": "showToolTipsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shape", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "name": "markerShapeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "attachedToChart", "type": "bool"}], "name": "attachedToChartChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "interactive", "type": "bool"}], "name": "interactiveChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsWidget"}]}], "inputFile": "qlegend.h", "outputRevision": 68}, {"classes": [{"className": "QLegendPrivate", "object": true, "qualifiedClassName": "QLegendPrivate", "slots": [{"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "name": "handleSeriesRemoved", "returnType": "void"}, {"access": "public", "name": "handleSeriesVisibleChanged", "returnType": "void"}, {"access": "public", "name": "handleCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlegend_p.h", "outputRevision": 68}, {"classes": [{"className": "QLegendMarker", "enums": [{"isClass": false, "isFlag": false, "name": "LegendMarkerType", "values": ["LegendMarkerTypeArea", "LegendMarkerTypeBar", "LegendMarkerTypePie", "LegendMarkerTypeXY", "LegendMarkerTypeBoxPlot", "LegendMarkerTypeCandlestick"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "label", "notify": "labelChanged", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "labelBrush", "notify": "labelBrushChanged", "read": "labelBrush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setLabelBrush"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pen", "notify": "penChanged", "read": "pen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setPen"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "shape", "notify": "shapeChanged", "read": "shape", "required": false, "scriptable": true, "stored": true, "type": "QLegend::<PERSON><PERSON><PERSON><PERSON><PERSON>", "user": false, "write": "setShape"}], "qualifiedClassName": "QLegendMarker", "signals": [{"access": "public", "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "name": "labelChanged", "returnType": "void"}, {"access": "public", "name": "labelBrushChanged", "returnType": "void"}, {"access": "public", "name": "fontChanged", "returnType": "void"}, {"access": "public", "name": "penChanged", "returnType": "void"}, {"access": "public", "name": "brushChanged", "returnType": "void"}, {"access": "public", "name": "visibleChanged", "returnType": "void"}, {"access": "public", "name": "shapeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlegendmarker.h", "outputRevision": 68}, {"classes": [{"className": "QLegendMarkerPrivate", "object": true, "qualifiedClassName": "QLegendMarkerPrivate", "slots": [{"access": "public", "name": "updated", "returnType": "void"}, {"access": "public", "name": "handleShapeChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlegendmarker_p.h", "outputRevision": 68}, {"classes": [{"className": "QLineSeries", "object": true, "qualifiedClassName": "QLineSeries", "superClasses": [{"access": "public", "name": "QXYSeries"}]}], "inputFile": "qlineseries.h", "outputRevision": 68}, {"classes": [{"className": "QLogValueAxis", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "min", "notify": "minC<PERSON>ed", "read": "min", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "max", "notify": "max<PERSON><PERSON>ed", "read": "max", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMax"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "labelFormat", "notify": "labelFormatChanged", "read": "labelFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setLabelFormat"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "base", "notify": "baseChanged", "read": "base", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBase"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "tickCount", "notify": "tickCountChanged", "read": "tickCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "minorTickCount", "notify": "minorTickCount<PERSON><PERSON>ed", "read": "minorTickCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMinorTickCount"}], "qualifiedClassName": "QLogValueAxis", "signals": [{"access": "public", "arguments": [{"name": "min", "type": "qreal"}], "name": "minC<PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "max", "type": "qreal"}], "name": "max<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "labelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "base", "type": "qreal"}], "name": "baseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tickCount", "type": "int"}], "name": "tickCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorTickCount", "type": "int"}], "name": "minorTickCount<PERSON><PERSON>ed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAxis"}]}], "inputFile": "qlogvalueaxis.h", "outputRevision": 68}, {"classes": [{"className": "QLogValueAxisPrivate", "object": true, "qualifiedClassName": "QLogValueAxisPrivate", "superClasses": [{"access": "public", "name": "QAbstractAxisPrivate"}]}], "inputFile": "qlogvalueaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "QPercentBarSeries", "object": true, "qualifiedClassName": "QPercentBarSeries", "superClasses": [{"access": "public", "name": "QAbstractBarSeries"}]}], "inputFile": "qpercentbarseries.h", "outputRevision": 68}, {"classes": [{"className": "QPieLegendMarker", "object": true, "qualifiedClassName": "QPieLegendMarker", "superClasses": [{"access": "public", "name": "QLegendMarker"}]}], "inputFile": "qpielegendmarker.h", "outputRevision": 68}, {"classes": [{"className": "QPieLegendMarkerPrivate", "object": true, "qualifiedClassName": "QPieLegendMarkerPrivate", "slots": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLegendMarkerPrivate"}]}], "inputFile": "qpielegendmarker_p.h", "outputRevision": 68}, {"classes": [{"className": "QPieModelMapper", "object": true, "qualifiedClassName": "QPieModelMapper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpiemodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QPieModelMapperPrivate", "object": true, "qualifiedClassName": "QPieModelMapperPrivate", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "name": "modelUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsRemoved", "returnType": "void"}, {"access": "public", "name": "handleModelDestroyed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "name": "slicesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "name": "slicesRemoved", "returnType": "void"}, {"access": "public", "name": "sliceLabelChanged", "returnType": "void"}, {"access": "public", "name": "sliceValueChanged", "returnType": "void"}, {"access": "public", "name": "handleSeriesDestroyed", "returnType": "void"}, {"access": "public", "name": "initializePieFromModel", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpiemodelmapper_p.h", "outputRevision": 68}, {"classes": [{"className": "QPieSeries", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "horizontalPosition", "read": "horizontalPosition", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHorizontalPosition"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "verticalPosition", "read": "verticalPosition", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setVerticalPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "size", "read": "pieSize", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPieSize"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "startAngle", "read": "pieStartAngle", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPieStartAngle"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "endAngle", "read": "pieEndAngle", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "set<PERSON>ieEndAngle"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "sum", "notify": "sumChanged", "read": "sum", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "holeSize", "read": "holeSize", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHoleSize"}], "qualifiedClassName": "QPieSeries", "signals": [{"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "name": "added", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "name": "removed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}, {"name": "state", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "countChanged", "returnType": "void"}, {"access": "public", "name": "sumChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeries"}]}], "inputFile": "qpieseries.h", "outputRevision": 68}, {"classes": [{"className": "QPieSeriesPrivate", "object": true, "qualifiedClassName": "QPieSeriesPrivate", "signals": [{"access": "public", "name": "calculatedDataChanged", "returnType": "void"}, {"access": "public", "name": "pieSizeChanged", "returnType": "void"}, {"access": "public", "name": "pieStartAngleChanged", "returnType": "void"}, {"access": "public", "name": "pieEndAngleChanged", "returnType": "void"}, {"access": "public", "name": "horizontalPositionChanged", "returnType": "void"}, {"access": "public", "name": "verticalPositionChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "sliceValueChanged", "returnType": "void"}, {"access": "public", "name": "sliceClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "bool"}], "name": "sliceHovered", "returnType": "void"}, {"access": "public", "name": "slicePressed", "returnType": "void"}, {"access": "public", "name": "sliceReleased", "returnType": "void"}, {"access": "public", "name": "sliceDoubleClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeriesPrivate"}]}], "inputFile": "qpieseries_p.h", "outputRevision": 68}, {"classes": [{"className": "QPieSlice", "enums": [{"isClass": false, "isFlag": false, "name": "LabelPosition", "values": ["LabelOutside", "LabelInsideHorizontal", "LabelInsideTangential", "LabelInsideNormal"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "label", "notify": "labelChanged", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "value", "notify": "valueChanged", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setValue"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "labelVisible", "notify": "labelVisibleChanged", "read": "isLabelVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLabelVisible"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelPosition", "read": "labelPosition", "required": false, "scriptable": true, "stored": true, "type": "LabelPosition", "user": false, "write": "setLabelPosition"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "exploded", "read": "isExploded", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setExploded"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pen", "notify": "penChanged", "read": "pen", "required": false, "scriptable": true, "stored": true, "type": "QPen", "user": false, "write": "setPen"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "borderColor", "notify": "borderColorChanged", "read": "borderColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBorderColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "borderWidth", "notify": "borderWidthChanged", "read": "borderWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorder<PERSON>idth"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "labelBrush", "notify": "labelBrushChanged", "read": "labelBrush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setLabelBrush"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "labelColor", "notify": "labelColorChanged", "read": "labelColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelColor"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "labelFont", "notify": "labelFontChanged", "read": "labelFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setLabelFont"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "labelArmLengthFactor", "read": "labelArmLengthFactor", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setLabelArmLengthFactor"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "explodeDistanceFactor", "read": "explodeDistanceFactor", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setExplodeDistanceFactor"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "percentage", "notify": "percentageChanged", "read": "percentage", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "startAngle", "notify": "startAngleChanged", "read": "startAngle", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "angleSpan", "notify": "angleSpanChanged", "read": "angleSpan", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QPieSlice", "signals": [{"access": "public", "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "name": "pressed", "returnType": "void"}, {"access": "public", "name": "released", "returnType": "void"}, {"access": "public", "name": "doubleClicked", "returnType": "void"}, {"access": "public", "name": "labelChanged", "returnType": "void"}, {"access": "public", "name": "valueChanged", "returnType": "void"}, {"access": "public", "name": "labelVisibleChanged", "returnType": "void"}, {"access": "public", "name": "penChanged", "returnType": "void"}, {"access": "public", "name": "brushChanged", "returnType": "void"}, {"access": "public", "name": "labelBrushChanged", "returnType": "void"}, {"access": "public", "name": "labelFontChanged", "returnType": "void"}, {"access": "public", "name": "percentageChanged", "returnType": "void"}, {"access": "public", "name": "startAngleChanged", "returnType": "void"}, {"access": "public", "name": "angleSpanChanged", "returnType": "void"}, {"access": "public", "name": "colorChanged", "returnType": "void"}, {"access": "public", "name": "borderColorChanged", "returnType": "void"}, {"access": "public", "name": "borderWidthChanged", "returnType": "void"}, {"access": "public", "name": "labelColorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpieslice.h", "outputRevision": 68}, {"classes": [{"className": "QPieSlicePrivate", "object": true, "qualifiedClassName": "QPieSlicePrivate", "signals": [{"access": "public", "name": "labelPositionChanged", "returnType": "void"}, {"access": "public", "name": "explodedChanged", "returnType": "void"}, {"access": "public", "name": "labelArmLengthFactorChanged", "returnType": "void"}, {"access": "public", "name": "explodeDistanceFactorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpieslice_p.h", "outputRevision": 68}, {"classes": [{"className": "QPolarChart", "enums": [{"isClass": false, "isFlag": false, "name": "PolarOrientation", "values": ["PolarOrientationRadial", "PolarOrientationAngular"]}, {"alias": "PolarOrientation", "isClass": false, "isFlag": true, "name": "PolarOrientations", "values": ["PolarOrientationRadial", "PolarOrientationAngular"]}], "object": true, "qualifiedClassName": "QPolarChart", "superClasses": [{"access": "public", "name": "Q<PERSON>hart"}]}], "inputFile": "qpolarchart.h", "outputRevision": 68}, {"classes": [{"className": "QScatterSeries", "enums": [{"isClass": false, "isFlag": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["MarkerShapeCircle", "<PERSON>er<PERSON>hapeRectangle", "MarkerShapeRotatedRectangle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkerShapeStar", "MarkerShapePentagon"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "borderColor", "notify": "borderColorChanged", "read": "borderColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBorderColor"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "markerShape", "notify": "markerShapeChanged", "read": "markerShape", "required": false, "scriptable": true, "stored": true, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user": false, "write": "setMarkerShape"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "markerSize", "notify": "markerSizeChanged", "read": "markerSize", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMarkerSize"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "brush", "read": "brush", "required": false, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}], "qualifiedClassName": "QScatterSeries", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "borderColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shape", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "name": "markerShapeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "qreal"}], "name": "markerSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QXYSeries"}]}], "inputFile": "qscatterseries.h", "outputRevision": 68}, {"classes": [{"className": "QSplineSeries", "object": true, "qualifiedClassName": "QSplineSeries", "superClasses": [{"access": "public", "name": "QLineSeries"}]}], "inputFile": "qsplineseries.h", "outputRevision": 68}, {"classes": [{"className": "QSplineSeriesPrivate", "object": true, "qualifiedClassName": "QSplineSeriesPrivate", "superClasses": [{"access": "public", "name": "QLineSeriesPrivate"}]}], "inputFile": "qsplineseries_p.h", "outputRevision": 68}, {"classes": [{"className": "QStackedBarSeries", "object": true, "qualifiedClassName": "QStackedBarSeries", "superClasses": [{"access": "public", "name": "QAbstractBarSeries"}]}], "inputFile": "qstackedbarseries.h", "outputRevision": 68}, {"classes": [{"className": "QValueAxis", "enums": [{"isClass": false, "isFlag": false, "name": "TickType", "values": ["TicksDynamic", "TicksFixed"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "tickCount", "notify": "tickCountChanged", "read": "tickCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTickCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "min", "notify": "minC<PERSON>ed", "read": "min", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "max", "notify": "max<PERSON><PERSON>ed", "read": "max", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMax"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelFormat", "notify": "labelFormatChanged", "read": "labelFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setLabelFormat"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "minorTickCount", "notify": "minorTickCount<PERSON><PERSON>ed", "read": "minorTickCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMinorTickCount"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "tickAnchor", "notify": "tickAnchor<PERSON>hanged", "read": "tickAnchor", "required": false, "revision": 515, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTickAnchor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "tickInterval", "notify": "tickIntervalChanged", "read": "tickInterval", "required": false, "revision": 515, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTickInterval"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "tickType", "notify": "tickTypeChanged", "read": "tickType", "required": false, "revision": 515, "scriptable": true, "stored": true, "type": "TickType", "user": false, "write": "setTickType"}], "qualifiedClassName": "QValueAxis", "signals": [{"access": "public", "arguments": [{"name": "min", "type": "qreal"}], "name": "minC<PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "max", "type": "qreal"}], "name": "max<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "qreal"}, {"name": "max", "type": "qreal"}], "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tickCount", "type": "int"}], "name": "tickCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tickCount", "type": "int"}], "name": "minorTickCount<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "labelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interval", "type": "qreal"}], "name": "tickIntervalChanged", "returnType": "void", "revision": 515}, {"access": "public", "arguments": [{"name": "anchor", "type": "qreal"}], "name": "tickAnchor<PERSON>hanged", "returnType": "void", "revision": 515}, {"access": "public", "arguments": [{"name": "type", "type": "QValueAxis::TickType"}], "name": "tickTypeChanged", "returnType": "void", "revision": 515}], "slots": [{"access": "public", "name": "applyNiceNumbers", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAxis"}]}], "inputFile": "qvalueaxis.h", "outputRevision": 68}, {"classes": [{"className": "QValueAxisPrivate", "object": true, "qualifiedClassName": "QValueAxisPrivate", "superClasses": [{"access": "public", "name": "QAbstractAxisPrivate"}]}], "inputFile": "qvalueaxis_p.h", "outputRevision": 68}, {"classes": [{"className": "QVBarModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QAbstractBarSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "firstBarSetColumn", "notify": "firstBarSetColumnChanged", "read": "firstBarSetColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstBarSetColumn"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "lastBarSetColumn", "notify": "lastBarSetColumnChanged", "read": "lastBarSetColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLastBarSetColumn"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstRow", "notify": "firstRowChanged", "read": "firstRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstRow"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRowCount"}], "qualifiedClassName": "QVBarModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "firstBarSetColumnChanged", "returnType": "void"}, {"access": "public", "name": "lastBarSetColumnChanged", "returnType": "void"}, {"access": "public", "name": "firstRowChanged", "returnType": "void"}, {"access": "public", "name": "rowCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBarModelMapper"}]}], "inputFile": "qvbarmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QVBoxPlotModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QBoxPlotSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "firstBoxSetColumn", "notify": "firstBoxSetColumnChanged", "read": "firstBoxSetColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstBoxSetColumn"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "lastBoxSetColumn", "notify": "lastBoxSetColumnChanged", "read": "lastBoxSetColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLastBoxSetColumn"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstRow", "notify": "firstRowChanged", "read": "firstRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstRow"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRowCount"}], "qualifiedClassName": "QVBoxPlotModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "firstBoxSetColumnChanged", "returnType": "void"}, {"access": "public", "name": "lastBoxSetColumnChanged", "returnType": "void"}, {"access": "public", "name": "firstRowChanged", "returnType": "void"}, {"access": "public", "name": "rowCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBoxPlotModelMapper"}]}], "inputFile": "qvboxplotmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QVCandlestickModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "timestampRow", "notify": "timestampRowChanged", "read": "timestampRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTimestampRow"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "openRow", "notify": "openRowChanged", "read": "openRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setOpenRow"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "highRow", "notify": "highRowChanged", "read": "highRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHighRow"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "lowRow", "notify": "lowRowChanged", "read": "lowRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLowRow"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "closeRow", "notify": "closeRowChanged", "read": "closeRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCloseRow"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "firstSetColumn", "notify": "firstSetColumnChanged", "read": "firstSetColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstSetColumn"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "lastSetColumn", "notify": "lastSetColumnChanged", "read": "lastSetColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLastSetColumn"}], "qualifiedClassName": "QVCandlestickModelMapper", "signals": [{"access": "public", "name": "timestampRowChanged", "returnType": "void"}, {"access": "public", "name": "openRowChanged", "returnType": "void"}, {"access": "public", "name": "highRowChanged", "returnType": "void"}, {"access": "public", "name": "lowRowChanged", "returnType": "void"}, {"access": "public", "name": "closeRowChanged", "returnType": "void"}, {"access": "public", "name": "firstSetColumnChanged", "returnType": "void"}, {"access": "public", "name": "lastSetColumnChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCandlestickModelMapper"}]}], "inputFile": "qvcandlestickmodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QVPieModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QPieSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "valuesColumn", "notify": "valuesColumnChanged", "read": "valuesColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setValuesColumn"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelsColumn", "notify": "labelsColumnChanged", "read": "labelsColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLabelsColumn"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstRow", "notify": "firstRowChanged", "read": "firstRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstRow"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRowCount"}], "qualifiedClassName": "QVPieModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "valuesColumnChanged", "returnType": "void"}, {"access": "public", "name": "labelsColumnChanged", "returnType": "void"}, {"access": "public", "name": "firstRowChanged", "returnType": "void"}, {"access": "public", "name": "rowCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPieModelMapper"}]}], "inputFile": "qvpiemodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QVXYModelMapper", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "series", "notify": "seriesReplaced", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QXYSeries*", "user": false, "write": "setSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "model", "notify": "modelReplaced", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "xColumn", "notify": "xColumnChanged", "read": "xColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setXColumn"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "yColumn", "notify": "yColumnChanged", "read": "yColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setYColumn"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "firstRow", "notify": "firstRowChanged", "read": "firstRow", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFirstRow"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRowCount"}], "qualifiedClassName": "QVXYModelMapper", "signals": [{"access": "public", "name": "seriesReplaced", "returnType": "void"}, {"access": "public", "name": "modelReplaced", "returnType": "void"}, {"access": "public", "name": "xColumnChanged", "returnType": "void"}, {"access": "public", "name": "yColumnChanged", "returnType": "void"}, {"access": "public", "name": "firstRowChanged", "returnType": "void"}, {"access": "public", "name": "rowCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QXYModelMapper"}]}], "inputFile": "qvxymodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QXYLegendMarker", "object": true, "qualifiedClassName": "QXYLegendMarker", "superClasses": [{"access": "public", "name": "QLegendMarker"}]}], "inputFile": "qxylegendmarker.h", "outputRevision": 68}, {"classes": [{"className": "QXYLegendMarkerPrivate", "object": true, "qualifiedClassName": "QXYLegendMarkerPrivate", "slots": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLegendMarkerPrivate"}]}], "inputFile": "qxylegendmarker_p.h", "outputRevision": 68}, {"classes": [{"className": "QXYModelMapper", "object": true, "qualifiedClassName": "QXYModelMapper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qxymodelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QXYModelMapperPrivate", "object": true, "qualifiedClassName": "QXYModelMapperPrivate", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "name": "modelUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "modelColumnsRemoved", "returnType": "void"}, {"access": "public", "name": "handleModelDestroyed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pointPos", "type": "int"}], "name": "handlePointAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pointPos", "type": "int"}], "name": "handlePointRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pointPos", "type": "int"}, {"name": "count", "type": "int"}], "name": "handlePointsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pointPos", "type": "int"}], "name": "handlePointReplaced", "returnType": "void"}, {"access": "public", "name": "handleSeriesDestroyed", "returnType": "void"}, {"access": "public", "name": "initializeXYFromModel", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qxymodelmapper_p.h", "outputRevision": 68}, {"classes": [{"className": "QXYSeries", "enums": [{"isClass": true, "isFlag": false, "name": "PointConfiguration", "values": ["Color", "Size", "Visibility", "LabelVisibility", "LabelFormat"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pointsVisible", "read": "pointsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPointsVisible"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "selectedColor", "notify": "selectedColorChanged", "read": "color", "required": false, "revision": 1538, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSelectedColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pointLabelsFormat", "notify": "pointLabelsFormatChanged", "read": "pointLabelsFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPointLabelsFormat"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pointLabelsVisible", "notify": "pointLabelsVisibilityChanged", "read": "pointLabelsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPointLabelsVisible"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pointLabelsFont", "notify": "pointLabelsFontChanged", "read": "pointLabelsFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setPointLabelsFont"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pointLabelsColor", "notify": "pointLabelsColorChanged", "read": "pointLabelsColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setPointLabelsColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "point<PERSON><PERSON><PERSON><PERSON><PERSON>ping", "notify": "pointLabelsClippingChanged", "read": "point<PERSON><PERSON><PERSON><PERSON><PERSON>ping", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPointLabelsClipping"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "bestFitLineVisible", "notify": "bestFitLineVisibilityChanged", "read": "bestFitLineVisible", "required": false, "revision": 1538, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBestFitLineVisible"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "bestFitLineColor", "notify": "bestFitLineColorChanged", "read": "bestFitLineColor", "required": false, "revision": 1538, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBestFitLineColor"}], "qualifiedClassName": "QXYSeries", "signals": [{"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}, {"name": "state", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "doubleClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "pointReplaced", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "pointRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "pointAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "selectedColorChanged", "returnType": "void", "revision": 1538}, {"access": "public", "name": "pointsReplaced", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "pointLabelsFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "pointLabelsVisibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "pointLabelsFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "pointLabelsColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clipping", "type": "bool"}], "name": "pointLabelsClippingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "pointsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "penChanged", "returnType": "void"}, {"access": "public", "name": "selectedPointsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "lightMarker", "type": "QImage"}], "name": "lightMarkerChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "selected<PERSON><PERSON>M<PERSON><PERSON>", "type": "QImage"}], "name": "selectedLightMarkerChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "bestFitLineVisibilityChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "pen", "type": "QPen"}], "name": "bestFitLinePenChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "bestFitLineColorChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "configuration", "type": "QHash<int,QHash<PointConfiguration,QVariant>>"}], "name": "pointsConfigurationChanged", "returnType": "void", "revision": 1538}, {"access": "public", "arguments": [{"name": "size", "type": "qreal"}], "name": "markerSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeries"}]}], "inputFile": "qxyseries.h", "outputRevision": 68}, {"classes": [{"className": "QXYSeriesPrivate", "object": true, "qualifiedClassName": "QXYSeriesPrivate", "signals": [{"access": "public", "name": "seriesUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSeriesPrivate"}]}], "inputFile": "qxyseries_p.h", "outputRevision": 68}, {"classes": [{"className": "ScatterChartItem", "interfaces": [[{"className": "QGraphicsItem", "id": "\"org.qt-project.Qt.QGraphicsItem\""}]], "object": true, "qualifiedClassName": "ScatterChartItem", "slots": [{"access": "public", "name": "handleSeriesUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "XYChart"}]}], "inputFile": "scatterchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "ScrollTicker", "object": true, "qualifiedClassName": "ScrollTicker", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "scroller_p.h", "outputRevision": 68}, {"classes": [{"className": "SplineChartItem", "interfaces": [[{"className": "QGraphicsItem", "id": "\"org.qt-project.Qt.QGraphicsItem\""}]], "object": true, "qualifiedClassName": "SplineChartItem", "slots": [{"access": "public", "name": "handleSeriesUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "XYChart"}]}], "inputFile": "splinechartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "StackedBarChartItem", "object": true, "qualifiedClassName": "StackedBarChartItem", "slots": [{"access": "private", "name": "handleLabelsPositionChanged", "returnType": "void"}, {"access": "private", "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractBarChartItem"}]}], "inputFile": "stackedbarchartitem_p.h", "outputRevision": 68}, {"classes": [{"className": "ValueAxisLabel", "object": true, "qualifiedClassName": "ValueAxisLabel", "signals": [{"access": "public", "arguments": [{"name": "oldValue", "type": "qreal"}, {"name": "newValue", "type": "qreal"}], "name": "valueChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "EditableAxisLabel"}]}], "inputFile": "valueaxislabel_p.h", "outputRevision": 68}, {"classes": [{"className": "XLogYDomain", "object": true, "qualifiedClassName": "XLogYDomain", "slots": [{"access": "public", "arguments": [{"name": "baseY", "type": "qreal"}], "name": "handleVerticalAxisBaseChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractDomain"}]}], "inputFile": "xlogydomain_p.h", "outputRevision": 68}, {"classes": [{"className": "XLogYPolarDomain", "object": true, "qualifiedClassName": "XLogYPolarDomain", "slots": [{"access": "public", "arguments": [{"name": "baseY", "type": "qreal"}], "name": "handleVerticalAxisBaseChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "PolarDomain"}]}], "inputFile": "xlogypolardomain_p.h", "outputRevision": 68}, {"classes": [{"className": "XYChart", "object": true, "qualifiedClassName": "XYChart", "signals": [{"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}, {"name": "state", "type": "bool"}], "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "name": "doubleClicked", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "handlePointAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "handlePointRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "name": "handlePointsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "handlePointReplaced", "returnType": "void"}, {"access": "public", "name": "handlePointsReplaced", "returnType": "void"}, {"access": "public", "name": "handleDomainUpdated", "returnType": "void"}, {"access": "public", "name": "handleSeriesUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "ChartItem"}]}], "inputFile": "xychart_p.h", "outputRevision": 68}, {"classes": [{"className": "XYDomain", "object": true, "qualifiedClassName": "XYDomain", "superClasses": [{"access": "public", "name": "AbstractDomain"}]}], "inputFile": "xydomain_p.h", "outputRevision": 68}, {"classes": [{"className": "XYPolarDomain", "object": true, "qualifiedClassName": "XYPolarDomain", "superClasses": [{"access": "public", "name": "PolarDomain"}]}], "inputFile": "xypolardomain_p.h", "outputRevision": 68}]