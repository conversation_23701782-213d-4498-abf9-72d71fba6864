<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtRemoteObjects">
    <load-typesystem name="core_common.xml" generate="no"/>
    <load-typesystem name="typesystem_core.xml" generate="no"/>

    <rejection class="QRemoteObjectStringLiterals"/>
    <rejection class="*" function-name="getTypeNameAndMetaobjectFromClassInfo"/>
<!-- Exclude namespace due to Q_NAMESPACE link errors on Windows (QTBUG-68014)
    <rejection class="QtRemoteObjects" field-name="staticMetaObject"/>
    <namespace-type name="QtRemoteObjects">
        <enum-type name="InitialAction"/>
        <enum-type name="QRemoteObjectPacketTypeEnum"/>
    </namespace-type>
-->
    <object-type name="QAbstractItemModelReplica"/>
    <object-type name="QRemoteObjectAbstractPersistedStore"/>
    <object-type name="QRemoteObjectDynamicReplica"/>
    <object-type name="QRemoteObjectHost"/>
    <object-type name="QRemoteObjectHostBase">
        <enum-type name="AllowedSchemas"/>
    </object-type>
    <object-type name="QRemoteObjectNode">
        <enum-type name="ErrorCode"/>
    </object-type>
    <object-type name="QRemoteObjectPendingCall">
        <enum-type name="Error"/>
    </object-type>
    <object-type name="QRemoteObjectPendingCallWatcher"/>
    <object-type name="QRemoteObjectRegistry"/>
    <object-type name="QRemoteObjectRegistryHost"/>
    <object-type name="QRemoteObjectReplica">
        <enum-type name="State"/>
        <!-- protected: <enum-type name="ConstructorType"/>  -->
    </object-type>
    <object-type name="QRemoteObjectSettingsStore"/>
    <value-type name="QRemoteObjectSourceLocationInfo"/>

    <suppress-warning text="^.*Typedef used on signal QRemoteObject.*$"/>
    <suppress-warning text="^QRemoteObjectPendingCallWatcher inherits from a non polymorphic type.*$"/>
    <suppress-warning text="^Enum 'QRemoteObjectReplica::ConstructorType'.*does not have a type entry.*$"/>
    <suppress-warning text="Stripping argument #1 of void QRemoteObjectReplica::QRemoteObjectReplica(QRemoteObjectReplica::ConstructorType) due to unmatched type &quot;QRemoteObjectReplica::ConstructorType&quot; with default expression &quot;DefaultConstructor&quot;."/>
    <suppress-warning text="skipping field 'QRemoteObjectReplica::d_impl' with unmatched type 'QSharedPointer'"/>
    <!-- QtNetwork is pulled in via QtRemoteObjectsDepends. -->
    <suppress-warning text="^Scoped enum 'Q(Ocsp)|(Dtls).*' does not have a type entry.*$"/>

</typesystem>
