[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "SatelliteSource"}, {"name": "QML.AddedInVersion", "value": "1541"}, {"name": "DefaultProperty", "value": "parameters"}], "className": "QDeclarativeSatelliteSource", "enums": [{"isClass": false, "isFlag": false, "name": "SourceError", "values": ["AccessError", "ClosedError", "NoError", "UnknownSourceError", "UpdateTimeoutError"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setBackendProperty", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "backendProperty", "returnType": "Q<PERSON><PERSON><PERSON>"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "valid", "notify": "validityChanged", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "updateInterval", "notify": "updateIntervalChanged", "read": "updateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUpdateInterval"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "sourceError", "notify": "sourceErrorChanged", "read": "sourceError", "required": false, "scriptable": true, "stored": true, "type": "SourceError", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "parameters", "read": "parameters", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativePluginParameter>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "satellitesInUse", "notify": "satellitesInUseChanged", "read": "satellitesInUse", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoSatelliteInfo>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "satellitesInView", "notify": "satellitesInViewChanged", "read": "satellitesInView", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoSatelliteInfo>", "user": false}], "qualifiedClassName": "QDeclarativeSatelliteSource", "signals": [{"access": "public", "name": "activeChanged", "returnType": "void"}, {"access": "public", "name": "validityChanged", "returnType": "void"}, {"access": "public", "name": "updateIntervalChanged", "returnType": "void"}, {"access": "public", "name": "sourceErrorChanged", "returnType": "void"}, {"access": "public", "name": "nameChanged", "returnType": "void"}, {"access": "public", "name": "satellitesInUseChanged", "returnType": "void"}, {"access": "public", "name": "satellitesInViewChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "name": "update", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "update", "returnType": "void"}, {"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "private", "arguments": [{"name": "error", "type": "QGeoSatelliteInfoSource::Error"}], "name": "sourceErrorReceived", "returnType": "void"}, {"access": "private", "name": "onParameterInitialized", "returnType": "void"}, {"access": "private", "arguments": [{"name": "satellites", "type": "QList<QGeoSatelliteInfo>"}], "name": "satellitesInViewUpdateReceived", "returnType": "void"}, {"access": "private", "arguments": [{"name": "satellites", "type": "QList<QGeoSatelliteInfo>"}], "name": "satellitesInUseUpdateReceived", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativesatellitesource_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "QtPositioning"}, {"name": "QML.Singleton", "value": "true"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "LocationSingleton", "methods": [{"access": "public", "name": "coordinate", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "latitude", "type": "double"}, {"name": "longitude", "type": "double"}, {"name": "altitude", "type": "double"}], "name": "coordinate", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "latitude", "type": "double"}, {"name": "longitude", "type": "double"}], "isCloned": true, "name": "coordinate", "returnType": "QGeoCoordinate"}, {"access": "public", "name": "shape", "returnType": "QGeoShape"}, {"access": "public", "name": "rectangle", "returnType": "QGeoRectangle"}, {"access": "public", "arguments": [{"name": "center", "type": "QGeoCoordinate"}, {"name": "width", "type": "double"}, {"name": "height", "type": "double"}], "name": "rectangle", "returnType": "QGeoRectangle"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QGeoCoordinate"}, {"name": "bottomRight", "type": "QGeoCoordinate"}], "name": "rectangle", "returnType": "QGeoRectangle"}, {"access": "public", "arguments": [{"name": "coordinates", "type": "QVariantList"}], "name": "rectangle", "returnType": "QGeoRectangle"}, {"access": "public", "name": "circle", "returnType": "QGeoCircle"}, {"access": "public", "arguments": [{"name": "center", "type": "QGeoCoordinate"}, {"name": "radius", "type": "qreal"}], "name": "circle", "returnType": "QGeoCircle"}, {"access": "public", "arguments": [{"name": "center", "type": "QGeoCoordinate"}], "isCloned": true, "name": "circle", "returnType": "QGeoCircle"}, {"access": "public", "name": "path", "returnType": "QGeoPath"}, {"access": "public", "arguments": [{"name": "value", "type": "QJSValue"}, {"name": "width", "type": "qreal"}], "name": "path", "returnType": "QGeoPath"}, {"access": "public", "arguments": [{"name": "value", "type": "QJSValue"}], "isCloned": true, "name": "path", "returnType": "QGeoPath"}, {"access": "public", "name": "polygon", "returnType": "QGeoPolygon"}, {"access": "public", "arguments": [{"name": "value", "type": "QVariantList"}], "name": "polygon", "returnType": "QGeoPolygon"}, {"access": "public", "arguments": [{"name": "perimeter", "type": "QVariantList"}, {"name": "holes", "type": "QVariantList"}], "name": "polygon", "returnType": "QGeoPolygon"}, {"access": "public", "arguments": [{"name": "shape", "type": "QGeoShape"}], "name": "shapeToCircle", "returnType": "QGeoCircle"}, {"access": "public", "arguments": [{"name": "shape", "type": "QGeoShape"}], "name": "shapeToRectangle", "returnType": "QGeoRectangle"}, {"access": "public", "arguments": [{"name": "shape", "type": "QGeoShape"}], "name": "shapeToPath", "returnType": "QGeoPath"}, {"access": "public", "arguments": [{"name": "shape", "type": "QGeoShape"}], "name": "shapeToPolygon", "returnType": "QGeoPolygon"}, {"access": "public", "arguments": [{"name": "mercator", "type": "QPointF"}], "name": "mercatorToCoord", "returnType": "QGeoCoordinate", "revision": 1292}, {"access": "public", "arguments": [{"name": "coord", "type": "QGeoCoordinate"}], "name": "coordToMercator", "returnType": "QPointF", "revision": 1292}], "object": true, "qualifiedClassName": "LocationSingleton", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "locationsingleton_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Address"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoAddress", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "address", "read": "address", "required": false, "scriptable": true, "stored": true, "type": "QGeoAddress", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "country", "notify": "countryChanged", "read": "country", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCountry"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "countryCode", "notify": "countryCodeChanged", "read": "countryCode", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCountryCode"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "state", "notify": "stateChanged", "read": "state", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setState"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "county", "notify": "countyChanged", "read": "county", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "set<PERSON>ounty"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "city", "notify": "cityChanged", "read": "city", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCity"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "district", "notify": "districtChanged", "read": "district", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDistrict"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "street", "notify": "streetChanged", "read": "street", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setStreet"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "streetNumber", "notify": "streetNumberChanged", "read": "streetNumber", "required": false, "revision": 1538, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setStreetNumber"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "postalCode", "notify": "postalCodeChanged", "read": "postalCode", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPostalCode"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "isTextGenerated", "notify": "isTextGeneratedChanged", "read": "isTextGenerated", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QDeclarativeGeoAddress", "signals": [{"access": "public", "name": "textChanged", "returnType": "void"}, {"access": "public", "name": "countryChanged", "returnType": "void"}, {"access": "public", "name": "countryCodeChanged", "returnType": "void"}, {"access": "public", "name": "stateChanged", "returnType": "void"}, {"access": "public", "name": "countyChanged", "returnType": "void"}, {"access": "public", "name": "cityChanged", "returnType": "void"}, {"access": "public", "name": "districtChanged", "returnType": "void"}, {"access": "public", "name": "streetChanged", "returnType": "void"}, {"access": "public", "name": "streetNumberChanged", "returnType": "void"}, {"access": "public", "name": "postalCodeChanged", "returnType": "void"}, {"access": "public", "name": "isTextGeneratedChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdeclarativegeoaddress_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Location"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoLocation", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "location", "read": "location", "required": false, "scriptable": true, "stored": true, "type": "QGeoLocation", "user": false, "write": "setLocation"}, {"bindable": "bindableAddress", "constant": false, "designable": true, "final": false, "index": 1, "name": "address", "read": "address", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoAddress*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"bindable": "bindableCoordinate", "constant": false, "designable": true, "final": false, "index": 2, "name": "coordinate", "read": "coordinate", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCoordinate"}, {"bindable": "bindableBoundingShape", "constant": false, "designable": true, "final": false, "index": 3, "name": "boundingShape", "read": "boundingShape", "required": false, "revision": 1538, "scriptable": true, "stored": true, "type": "QGeoShape", "user": false, "write": "setBoundingShape"}, {"bindable": "bindableExtendedAttributes", "constant": false, "designable": true, "final": false, "index": 4, "name": "extendedAttributes", "read": "extendedAttributes", "required": false, "revision": 1293, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setExtendedAttributes"}], "qualifiedClassName": "QDeclarativeGeoLocation", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdeclarativegeolocation_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PluginParameter"}, {"name": "QML.AddedInVersion", "value": "1294"}], "className": "QDeclarativePluginParameter", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "value", "notify": "valueChanged", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setValue"}], "qualifiedClassName": "QDeclarativePluginParameter", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "valueChanged", "returnType": "void"}, {"access": "public", "name": "initialized", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdeclarativepluginparameter_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Position"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePosition", "object": true, "properties": [{"bindable": "bindableLatitudeValid", "constant": false, "designable": true, "final": false, "index": 0, "name": "latitudeValid", "read": "isLatitudeValid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableLongitudeValid", "constant": false, "designable": true, "final": false, "index": 1, "name": "longitudeValid", "read": "isLongitudeValid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableAltitudeValid", "constant": false, "designable": true, "final": false, "index": 2, "name": "altitudeValid", "read": "isAltitudeValid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableCoordinate", "constant": false, "designable": true, "final": false, "index": 3, "name": "coordinate", "read": "coordinate", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false}, {"bindable": "bindableTimestamp", "constant": false, "designable": true, "final": false, "index": 4, "name": "timestamp", "read": "timestamp", "required": false, "scriptable": true, "stored": true, "type": "QDateTime", "user": false}, {"bindable": "bindableSpeed", "constant": false, "designable": true, "final": false, "index": 5, "name": "speed", "read": "speed", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false}, {"bindable": "bindableSpeedValid", "constant": false, "designable": true, "final": false, "index": 6, "name": "speedValid", "read": "isSpeedValid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableHorizontalAccuracy", "constant": false, "designable": true, "final": false, "index": 7, "name": "horizontalAccuracy", "read": "horizontalAccuracy", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "binableVerticalAccuracy", "constant": false, "designable": true, "final": false, "index": 8, "name": "verticalAccuracy", "read": "verticalAccuracy", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableHorizontalAccuracyValid", "constant": false, "designable": true, "final": false, "index": 9, "name": "horizontalAccuracyValid", "read": "isHorizontalAccuracyValid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableVerticalAccuracyValid", "constant": false, "designable": true, "final": false, "index": 10, "name": "verticalAccuracyValid", "read": "isVerticalAccuracyValid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableDirectionValid", "constant": false, "designable": true, "final": false, "index": 11, "name": "directionValid", "read": "isDirectionValid", "required": false, "revision": 1281, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableDirection", "constant": false, "designable": true, "final": false, "index": 12, "name": "direction", "read": "direction", "required": false, "revision": 1281, "scriptable": true, "stored": true, "type": "double", "user": false}, {"bindable": "bindableVerticalSpeedValid", "constant": false, "designable": true, "final": false, "index": 13, "name": "verticalSpeedValid", "read": "isVerticalSpeedValid", "required": false, "revision": 1281, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableVerticalSpeed", "constant": false, "designable": true, "final": false, "index": 14, "name": "verticalSpeed", "read": "verticalSpeed", "required": false, "revision": 1281, "scriptable": true, "stored": true, "type": "double", "user": false}, {"bindable": "bindableMagneticVariation", "constant": false, "designable": true, "final": false, "index": 15, "name": "magneticVariation", "read": "magneticVariation", "required": false, "revision": 1282, "scriptable": true, "stored": true, "type": "double", "user": false}, {"bindable": "bindableMagneticVariationValid", "constant": false, "designable": true, "final": false, "index": 16, "name": "magneticVariationValid", "read": "isMagneticVariationValid", "required": false, "revision": 1282, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableDirectionAccuracy", "constant": false, "designable": true, "final": false, "index": 17, "name": "directionAccuracy", "read": "directionAccuracy", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "double", "user": false}, {"bindable": "bindableDirectionAccuracyValid", "constant": false, "designable": true, "final": false, "index": 18, "name": "directionAccuracyValid", "read": "isDirectionAccuracyValid", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QDeclarativePosition", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdeclarativeposition_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PositionSource"}, {"name": "QML.AddedInVersion", "value": "1280"}, {"name": "DefaultProperty", "value": "parameters"}], "className": "QDeclarativePositionSource", "enums": [{"isClass": false, "isFlag": false, "name": "PositioningMethod", "values": ["NoPositioningMethods", "SatellitePositioningMethods", "NonSatellitePositioningMethods", "AllPositioningMethods"]}, {"alias": "PositioningMethod", "isClass": false, "isFlag": true, "name": "PositioningMethods", "values": ["NoPositioningMethods", "SatellitePositioningMethods", "NonSatellitePositioningMethods", "AllPositioningMethods"]}, {"isClass": false, "isFlag": false, "name": "SourceError", "values": ["AccessError", "ClosedError", "UnknownSourceError", "NoError", "UpdateTimeoutError"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setBackendProperty", "returnType": "bool", "revision": 1294}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "backendProperty", "returnType": "Q<PERSON><PERSON><PERSON>", "revision": 1294}], "object": true, "properties": [{"bindable": "bindablePosition", "constant": false, "designable": true, "final": false, "index": 0, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePosition*", "user": false}, {"bindable": "bindableActive", "constant": false, "designable": true, "final": false, "index": 1, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"bindable": "bindableIsValid", "constant": false, "designable": true, "final": false, "index": 2, "name": "valid", "notify": "validityChanged", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "updateInterval", "notify": "updateIntervalChanged", "read": "updateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUpdateInterval"}, {"bindable": "bindableSupportedPositioningMethods", "constant": false, "designable": true, "final": false, "index": 4, "name": "supportedPositioningMethods", "notify": "supportedPositioningMethodsChanged", "read": "supportedPositioningMethods", "required": false, "scriptable": true, "stored": true, "type": "PositioningMethods", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "preferredPositioningMethods", "notify": "preferredPositioningMethodsChanged", "read": "preferredPositioningMethods", "required": false, "scriptable": true, "stored": true, "type": "PositioningMethods", "user": false, "write": "setPreferredPositioningMethods"}, {"bindable": "bindableSourceError", "constant": false, "designable": true, "final": false, "index": 6, "name": "sourceError", "notify": "sourceErrorChanged", "read": "sourceError", "required": false, "scriptable": true, "stored": true, "type": "SourceError", "user": false}, {"bindable": "bindableName", "constant": false, "designable": true, "final": false, "index": 7, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "parameters", "read": "parameters", "required": false, "revision": 1294, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativePluginParameter>", "user": false}], "qualifiedClassName": "QDeclarativePositionSource", "signals": [{"access": "public", "name": "positionChanged", "returnType": "void"}, {"access": "public", "name": "activeChanged", "returnType": "void"}, {"access": "public", "name": "updateIntervalChanged", "returnType": "void"}, {"access": "public", "name": "supportedPositioningMethodsChanged", "returnType": "void"}, {"access": "public", "name": "preferredPositioningMethodsChanged", "returnType": "void"}, {"access": "public", "name": "sourceErrorChanged", "returnType": "void"}, {"access": "public", "name": "nameChanged", "returnType": "void"}, {"access": "public", "name": "validityChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "name": "update", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "update", "returnType": "void"}, {"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "private", "arguments": [{"name": "update", "type": "QGeoPositionInfo"}], "name": "positionUpdateReceived", "returnType": "void"}, {"access": "private", "arguments": [{"name": "error", "type": "QGeoPositionInfoSource::Error"}], "name": "sourceErrorReceived", "returnType": "void"}, {"access": "private", "name": "onParameterInitialized", "returnType": "void"}, {"access": "private", "name": "notifySupportedPositioningMethodsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativepositionsource_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Foreign", "value": "QGeoCoordinate"}, {"name": "QML.Element", "value": "geoCoordinate"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoCoordinateForeign", "gadget": true, "qualifiedClassName": "QGeoCoordinateForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoAddress"}, {"name": "QML.Element", "value": "geo<PERSON><PERSON><PERSON>"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoAddressForeign", "gadget": true, "qualifiedClassName": "QGeoAddressForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoRectangle"}, {"name": "QML.Element", "value": "geoRectangle"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoRectangleForeign", "gadget": true, "qualifiedClassName": "QGeoRectangleForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoCircle"}, {"name": "QML.Element", "value": "geoCircle"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoCircleForeign", "gadget": true, "qualifiedClassName": "QGeoCircleForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoPath"}, {"name": "QML.Element", "value": "geoPath"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoPathForeign", "gadget": true, "qualifiedClassName": "QGeoPathForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoPolygon"}, {"name": "QML.Element", "value": "geoPolygon"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoPolygonForeign", "gadget": true, "qualifiedClassName": "QGeoPolygonForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoLocation"}, {"name": "QML.Element", "value": "geoLocation"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoLocationForeign", "gadget": true, "qualifiedClassName": "QGeoLocationForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoShape"}, {"name": "QML.Element", "value": "geoShape"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "construct"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoShapeForeign", "gadget": true, "qualifiedClassName": "QGeoShapeForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoCoordinateObject"}, {"name": "QML.Element", "value": "anonymous"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoCoordinateObjectForeign", "gadget": true, "qualifiedClassName": "QGeoCoordinateObjectForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoPositionInfo"}, {"name": "QML.Element", "value": "geoPositionInfo"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QGeoPositionInfoForeign", "gadget": true, "qualifiedClassName": "QGeoPositionInfoForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoSatelliteInfo"}, {"name": "QML.Element", "value": "geoSatelliteInfo"}, {"name": "QML.AddedInVersion", "value": "1541"}], "className": "QGeoSatelliteInfoForeign", "gadget": true, "qualifiedClassName": "QGeoSatelliteInfoForeign"}, {"className": "QGeoSatelliteInfoDerived", "gadget": true, "qualifiedClassName": "QGeoSatelliteInfoDerived", "superClasses": [{"access": "public", "name": "QGeoSatelliteInfo"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoSatelliteInfoDerived"}, {"name": "QML.Element", "value": "GeoSatelliteInfo"}, {"name": "QML.AddedInVersion", "value": "1541"}], "className": "QGeoSatelliteInfoForeignNamespace", "namespace": true, "qualifiedClassName": "QGeoSatelliteInfoForeignNamespace"}], "inputFile": "qpositioningquickmodule_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "CoordinateAnimation"}, {"name": "QML.AddedInVersion", "value": "1283"}], "className": "QQuickGeoCoordinateAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "Direction", "values": ["Shortest", "West", "East"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "from", "read": "from", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setFrom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "to", "read": "to", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setTo"}, {"bindable": "bindableDirection", "constant": false, "designable": true, "final": false, "index": 2, "name": "direction", "notify": "directionChanged", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "Direction", "user": false, "write": "setDirection"}], "qualifiedClassName": "QQuickGeoCoordinateAnimation", "signals": [{"access": "public", "name": "directionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickPropertyAnimation"}]}], "inputFile": "qquickgeocoordinateanimation_p.h", "outputRevision": 68}]