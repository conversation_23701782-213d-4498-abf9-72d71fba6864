[{"classes": [{"className": "Quick3DEffect", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "techniques", "read": "techniqueList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QTechnique>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DEffect", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3deffect_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3D<PERSON>ayer<PERSON>ilter", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "layers", "read": "qmlLayers", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QLayer>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3D<PERSON><PERSON><PERSON><PERSON><PERSON>er", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dlayerfilter_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "parameters", "read": "qmlParameters", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DMaterial", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dmaterial_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "waitFor", "read": "waitFor", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setWaitFor"}], "qualifiedClassName": "Qt3DRender::Render::Quick::<PERSON>3<PERSON><PERSON><PERSON>B<PERSON><PERSON>", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dmemorybarrier_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DParameter", "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DParameter", "superClasses": [{"access": "public", "name": "QParameter"}]}], "inputFile": "quick3dparameter_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DRayCaster", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "layers", "read": "qmlLayers", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QLayer>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRayCaster", "superClasses": [{"access": "public", "name": "QRayCaster"}]}], "inputFile": "quick3draycaster_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DRenderPass", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "filterKeys", "read": "filterKeyList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "renderStates", "read": "renderStateList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderState>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRenderPass", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3drenderpass_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DRenderPass<PERSON>ilter", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "matchAny", "read": "includeList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRenderPassFilter", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3drenderpassfilter_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DRenderTargetOutput", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "attachments", "read": "qmlAttachments", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderTargetOutput>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DRenderTargetOutput", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3drendertargetoutput_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DScene", "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DScene", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dscene_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DScreenRayCaster", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "layers", "read": "qmlLayers", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QLayer>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DScreenRayCaster", "superClasses": [{"access": "public", "name": "QScreenRayCaster"}]}], "inputFile": "quick3dscreenraycaster_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DShaderData", "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DShaderData", "superClasses": [{"access": "public", "name": "QShaderData"}]}], "inputFile": "quick3dshaderdata_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "values"}], "className": "Quick3DShaderDataArray", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "values", "read": "valuesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QShaderData>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DShaderDataArray", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "quick3dshaderdataarray_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DStateSet", "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "renderStates", "read": "renderStateList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderState>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DStateSet", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dstateset_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DTechnique", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "filterKeys", "read": "filterKeyList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "renderPasses", "read": "renderPassList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QRenderPass>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DTechnique", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dtechnique_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DTechniqueFilter", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "matchAll", "read": "matchList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::Q<PERSON><PERSON><PERSON><PERSON><PERSON>>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "parameters", "read": "parameterList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QParameter>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DTechniqueFilter", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dtechniquefilter_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "textureImages"}], "className": "Quick3DTextureExtension", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "textureImages", "read": "textureImages", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DRender::QAbstractTextureImage>", "user": false}], "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DTextureExtension", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dtexture_p.h", "outputRevision": 68}, {"classes": [{"className": "Quick3DViewport", "object": true, "qualifiedClassName": "Qt3DRender::Render::Quick::Quick3DViewport", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dviewport_p.h", "outputRevision": 68}]