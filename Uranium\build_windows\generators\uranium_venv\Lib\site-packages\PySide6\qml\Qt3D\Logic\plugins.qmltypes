import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json Qt3D.Logic 2.15'

Module {
    dependencies: ["Qt3D.Core 2.0"]
    Component {
        name: "Qt3DLogic::QFrameAction"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Logic/FrameAction 2.0"]
        exportMetaObjectRevisions: [0]
        Signal {
            name: "triggered"
            Parameter { name: "dt"; type: "float" }
        }
    }
}
