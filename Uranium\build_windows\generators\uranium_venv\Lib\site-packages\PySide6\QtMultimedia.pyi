# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtMultimedia, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtMultimedia`

import PySide6.QtMultimedia
import PySide6.QtCore
import PySide6.QtGui

import enum
from typing import Any, ClassVar, List, Optional, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QAudio(Shiboken.Object):

    class Error(enum.Enum):

        NoError                  : QAudio.Error = ... # 0x0
        OpenError                : QAudio.Error = ... # 0x1
        IOError                  : QAudio.Error = ... # 0x2
        UnderrunError            : QAudio.Error = ... # 0x3
        FatalError               : QAudio.Error = ... # 0x4

    class State(enum.Enum):

        ActiveState              : QAudio.State = ... # 0x0
        SuspendedState           : QAudio.State = ... # 0x1
        StoppedState             : QAudio.State = ... # 0x2
        IdleState                : QAudio.State = ... # 0x3

    class VolumeScale(enum.Enum):

        LinearVolumeScale        : QAudio.VolumeScale = ... # 0x0
        CubicVolumeScale         : QAudio.VolumeScale = ... # 0x1
        LogarithmicVolumeScale   : QAudio.VolumeScale = ... # 0x2
        DecibelVolumeScale       : QAudio.VolumeScale = ... # 0x3


    @staticmethod
    def convertVolume(volume: float, from_: PySide6.QtMultimedia.QAudio.VolumeScale, to: PySide6.QtMultimedia.QAudio.VolumeScale) -> float: ...


class QAudioBuffer(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, data: Union[PySide6.QtCore.QByteArray, bytes], format: PySide6.QtMultimedia.QAudioFormat, startTime: int = ...) -> None: ...
    @overload
    def __init__(self, numFrames: int, format: PySide6.QtMultimedia.QAudioFormat, startTime: int = ...) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtMultimedia.QAudioBuffer) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def byteCount(self) -> int: ...
    def constData(self) -> bytes: ...
    def data(self) -> bytes: ...
    def duration(self) -> int: ...
    def format(self) -> PySide6.QtMultimedia.QAudioFormat: ...
    def frameCount(self) -> int: ...
    def isValid(self) -> bool: ...
    def sampleCount(self) -> int: ...
    def startTime(self) -> int: ...
    def swap(self, other: PySide6.QtMultimedia.QAudioBuffer) -> None: ...


class QAudioDecoder(PySide6.QtCore.QObject):

    bufferAvailableChanged   : ClassVar[Signal] = ... # bufferAvailableChanged(bool)
    bufferReady              : ClassVar[Signal] = ... # bufferReady()
    durationChanged          : ClassVar[Signal] = ... # durationChanged(qlonglong)
    error                    : ClassVar[Signal] = ... # error(QAudioDecoder::Error)
    finished                 : ClassVar[Signal] = ... # finished()
    formatChanged            : ClassVar[Signal] = ... # formatChanged(QAudioFormat)
    isDecodingChanged        : ClassVar[Signal] = ... # isDecodingChanged(bool)
    positionChanged          : ClassVar[Signal] = ... # positionChanged(qlonglong)
    sourceChanged            : ClassVar[Signal] = ... # sourceChanged()

    class Error(enum.Enum):

        NoError                  : QAudioDecoder.Error = ... # 0x0
        ResourceError            : QAudioDecoder.Error = ... # 0x1
        FormatError              : QAudioDecoder.Error = ... # 0x2
        AccessDeniedError        : QAudioDecoder.Error = ... # 0x3
        NotSupportedError        : QAudioDecoder.Error = ... # 0x4


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def audioFormat(self) -> PySide6.QtMultimedia.QAudioFormat: ...
    def bufferAvailable(self) -> bool: ...
    def duration(self) -> int: ...
    def errorString(self) -> str: ...
    def isDecoding(self) -> bool: ...
    def isSupported(self) -> bool: ...
    def position(self) -> int: ...
    def read(self) -> PySide6.QtMultimedia.QAudioBuffer: ...
    def setAudioFormat(self, format: PySide6.QtMultimedia.QAudioFormat) -> None: ...
    def setSource(self, fileName: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def setSourceDevice(self, device: PySide6.QtCore.QIODevice) -> None: ...
    def source(self) -> PySide6.QtCore.QUrl: ...
    def sourceDevice(self) -> PySide6.QtCore.QIODevice: ...
    def start(self) -> None: ...
    def stop(self) -> None: ...


class QAudioDevice(Shiboken.Object):

    class Mode(enum.Enum):

        Null                     : QAudioDevice.Mode = ... # 0x0
        Input                    : QAudioDevice.Mode = ... # 0x1
        Output                   : QAudioDevice.Mode = ... # 0x2


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtMultimedia.QAudioDevice) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def channelConfiguration(self) -> PySide6.QtMultimedia.QAudioFormat.ChannelConfig: ...
    def description(self) -> str: ...
    def id(self) -> PySide6.QtCore.QByteArray: ...
    def isDefault(self) -> bool: ...
    def isFormatSupported(self, format: PySide6.QtMultimedia.QAudioFormat) -> bool: ...
    def isNull(self) -> bool: ...
    def maximumChannelCount(self) -> int: ...
    def maximumSampleRate(self) -> int: ...
    def minimumChannelCount(self) -> int: ...
    def minimumSampleRate(self) -> int: ...
    def mode(self) -> PySide6.QtMultimedia.QAudioDevice.Mode: ...
    def preferredFormat(self) -> PySide6.QtMultimedia.QAudioFormat: ...
    def supportedSampleFormats(self) -> List[PySide6.QtMultimedia.QAudioFormat.SampleFormat]: ...
    def swap(self, other: PySide6.QtMultimedia.QAudioDevice) -> None: ...


class QAudioFormat(Shiboken.Object):

    class AudioChannelPosition(enum.Enum):

        UnknownPosition          : QAudioFormat.AudioChannelPosition = ... # 0x0
        FrontLeft                : QAudioFormat.AudioChannelPosition = ... # 0x1
        FrontRight               : QAudioFormat.AudioChannelPosition = ... # 0x2
        FrontCenter              : QAudioFormat.AudioChannelPosition = ... # 0x3
        LFE                      : QAudioFormat.AudioChannelPosition = ... # 0x4
        BackLeft                 : QAudioFormat.AudioChannelPosition = ... # 0x5
        BackRight                : QAudioFormat.AudioChannelPosition = ... # 0x6
        FrontLeftOfCenter        : QAudioFormat.AudioChannelPosition = ... # 0x7
        FrontRightOfCenter       : QAudioFormat.AudioChannelPosition = ... # 0x8
        BackCenter               : QAudioFormat.AudioChannelPosition = ... # 0x9
        SideLeft                 : QAudioFormat.AudioChannelPosition = ... # 0xa
        SideRight                : QAudioFormat.AudioChannelPosition = ... # 0xb
        TopCenter                : QAudioFormat.AudioChannelPosition = ... # 0xc
        TopFrontLeft             : QAudioFormat.AudioChannelPosition = ... # 0xd
        TopFrontCenter           : QAudioFormat.AudioChannelPosition = ... # 0xe
        TopFrontRight            : QAudioFormat.AudioChannelPosition = ... # 0xf
        TopBackLeft              : QAudioFormat.AudioChannelPosition = ... # 0x10
        TopBackCenter            : QAudioFormat.AudioChannelPosition = ... # 0x11
        TopBackRight             : QAudioFormat.AudioChannelPosition = ... # 0x12
        LFE2                     : QAudioFormat.AudioChannelPosition = ... # 0x13
        TopSideLeft              : QAudioFormat.AudioChannelPosition = ... # 0x14
        TopSideRight             : QAudioFormat.AudioChannelPosition = ... # 0x15
        BottomFrontCenter        : QAudioFormat.AudioChannelPosition = ... # 0x16
        BottomFrontLeft          : QAudioFormat.AudioChannelPosition = ... # 0x17
        BottomFrontRight         : QAudioFormat.AudioChannelPosition = ... # 0x18

    class ChannelConfig(enum.Enum):

        ChannelConfigUnknown     : QAudioFormat.ChannelConfig = ... # 0x0
        ChannelConfigStereo      : QAudioFormat.ChannelConfig = ... # 0x6
        ChannelConfigMono        : QAudioFormat.ChannelConfig = ... # 0x8
        ChannelConfig3Dot0       : QAudioFormat.ChannelConfig = ... # 0xe
        ChannelConfig2Dot1       : QAudioFormat.ChannelConfig = ... # 0x16
        ChannelConfig3Dot1       : QAudioFormat.ChannelConfig = ... # 0x1e
        ChannelConfigSurround5Dot0: QAudioFormat.ChannelConfig = ... # 0x6e
        ChannelConfigSurround5Dot1: QAudioFormat.ChannelConfig = ... # 0x7e
        ChannelConfigSurround7Dot0: QAudioFormat.ChannelConfig = ... # 0xc6e
        ChannelConfigSurround7Dot1: QAudioFormat.ChannelConfig = ... # 0xc7e

    class SampleFormat(enum.Enum):

        Unknown                  : QAudioFormat.SampleFormat = ... # 0x0
        UInt8                    : QAudioFormat.SampleFormat = ... # 0x1
        Int16                    : QAudioFormat.SampleFormat = ... # 0x2
        Int32                    : QAudioFormat.SampleFormat = ... # 0x3
        Float                    : QAudioFormat.SampleFormat = ... # 0x4
        NSampleFormats           : QAudioFormat.SampleFormat = ... # 0x5


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, QAudioFormat: PySide6.QtMultimedia.QAudioFormat) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def bytesForDuration(self, microseconds: int) -> int: ...
    def bytesForFrames(self, frameCount: int) -> int: ...
    def bytesPerFrame(self) -> int: ...
    def bytesPerSample(self) -> int: ...
    def channelConfig(self) -> PySide6.QtMultimedia.QAudioFormat.ChannelConfig: ...
    def channelCount(self) -> int: ...
    def channelOffset(self, channel: PySide6.QtMultimedia.QAudioFormat.AudioChannelPosition) -> int: ...
    @staticmethod
    def defaultChannelConfigForChannelCount(channelCount: int) -> PySide6.QtMultimedia.QAudioFormat.ChannelConfig: ...
    def durationForBytes(self, byteCount: int) -> int: ...
    def durationForFrames(self, frameCount: int) -> int: ...
    def framesForBytes(self, byteCount: int) -> int: ...
    def framesForDuration(self, microseconds: int) -> int: ...
    def isValid(self) -> bool: ...
    def normalizedSampleValue(self, sample: bytes) -> float: ...
    def sampleFormat(self) -> PySide6.QtMultimedia.QAudioFormat.SampleFormat: ...
    def sampleRate(self) -> int: ...
    def setChannelConfig(self, config: PySide6.QtMultimedia.QAudioFormat.ChannelConfig) -> None: ...
    def setChannelCount(self, channelCount: int) -> None: ...
    def setSampleFormat(self, f: PySide6.QtMultimedia.QAudioFormat.SampleFormat) -> None: ...
    def setSampleRate(self, sampleRate: int) -> None: ...


class QAudioInput(PySide6.QtCore.QObject):

    deviceChanged            : ClassVar[Signal] = ... # deviceChanged()
    mutedChanged             : ClassVar[Signal] = ... # mutedChanged(bool)
    volumeChanged            : ClassVar[Signal] = ... # volumeChanged(float)

    @overload
    def __init__(self, deviceInfo: PySide6.QtMultimedia.QAudioDevice, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def device(self) -> PySide6.QtMultimedia.QAudioDevice: ...
    def isMuted(self) -> bool: ...
    def setDevice(self, device: PySide6.QtMultimedia.QAudioDevice) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    def volume(self) -> float: ...


class QAudioOutput(PySide6.QtCore.QObject):

    deviceChanged            : ClassVar[Signal] = ... # deviceChanged()
    mutedChanged             : ClassVar[Signal] = ... # mutedChanged(bool)
    volumeChanged            : ClassVar[Signal] = ... # volumeChanged(float)

    @overload
    def __init__(self, device: PySide6.QtMultimedia.QAudioDevice, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def device(self) -> PySide6.QtMultimedia.QAudioDevice: ...
    def isMuted(self) -> bool: ...
    def setDevice(self, device: PySide6.QtMultimedia.QAudioDevice) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    def volume(self) -> float: ...


class QAudioSink(PySide6.QtCore.QObject):

    stateChanged             : ClassVar[Signal] = ... # stateChanged(QAudio::State)

    @overload
    def __init__(self, audioDeviceInfo: PySide6.QtMultimedia.QAudioDevice, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def bufferSize(self) -> int: ...
    def bytesFree(self) -> int: ...
    def elapsedUSecs(self) -> int: ...
    def error(self) -> PySide6.QtMultimedia.QAudio.Error: ...
    def format(self) -> PySide6.QtMultimedia.QAudioFormat: ...
    def isNull(self) -> bool: ...
    def processedUSecs(self) -> int: ...
    def reset(self) -> None: ...
    def resume(self) -> None: ...
    def setBufferSize(self, bytes: int) -> None: ...
    def setVolume(self, arg__1: float) -> None: ...
    @overload
    def start(self) -> PySide6.QtCore.QIODevice: ...
    @overload
    def start(self, device: PySide6.QtCore.QIODevice) -> None: ...
    def state(self) -> PySide6.QtMultimedia.QAudio.State: ...
    def stop(self) -> None: ...
    def suspend(self) -> None: ...
    def volume(self) -> float: ...


class QAudioSource(PySide6.QtCore.QObject):

    stateChanged             : ClassVar[Signal] = ... # stateChanged(QAudio::State)

    @overload
    def __init__(self, audioDeviceInfo: PySide6.QtMultimedia.QAudioDevice, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def bufferSize(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def elapsedUSecs(self) -> int: ...
    def error(self) -> PySide6.QtMultimedia.QAudio.Error: ...
    def format(self) -> PySide6.QtMultimedia.QAudioFormat: ...
    def isNull(self) -> bool: ...
    def processedUSecs(self) -> int: ...
    def reset(self) -> None: ...
    def resume(self) -> None: ...
    def setBufferSize(self, bytes: int) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    @overload
    def start(self) -> PySide6.QtCore.QIODevice: ...
    @overload
    def start(self, device: PySide6.QtCore.QIODevice) -> None: ...
    def state(self) -> PySide6.QtMultimedia.QAudio.State: ...
    def stop(self) -> None: ...
    def suspend(self) -> None: ...
    def volume(self) -> float: ...


class QCamera(PySide6.QtCore.QObject):

    activeChanged            : ClassVar[Signal] = ... # activeChanged(bool)
    brightnessChanged        : ClassVar[Signal] = ... # brightnessChanged()
    cameraDeviceChanged      : ClassVar[Signal] = ... # cameraDeviceChanged()
    cameraFormatChanged      : ClassVar[Signal] = ... # cameraFormatChanged()
    colorTemperatureChanged  : ClassVar[Signal] = ... # colorTemperatureChanged()
    contrastChanged          : ClassVar[Signal] = ... # contrastChanged()
    customFocusPointChanged  : ClassVar[Signal] = ... # customFocusPointChanged()
    errorChanged             : ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QCamera::Error,QString)
    exposureCompensationChanged: ClassVar[Signal] = ... # exposureCompensationChanged(float)
    exposureModeChanged      : ClassVar[Signal] = ... # exposureModeChanged()
    exposureTimeChanged      : ClassVar[Signal] = ... # exposureTimeChanged(float)
    flashModeChanged         : ClassVar[Signal] = ... # flashModeChanged()
    flashReady               : ClassVar[Signal] = ... # flashReady(bool)
    focusDistanceChanged     : ClassVar[Signal] = ... # focusDistanceChanged(float)
    focusModeChanged         : ClassVar[Signal] = ... # focusModeChanged()
    focusPointChanged        : ClassVar[Signal] = ... # focusPointChanged()
    hueChanged               : ClassVar[Signal] = ... # hueChanged()
    isoSensitivityChanged    : ClassVar[Signal] = ... # isoSensitivityChanged(int)
    manualExposureTimeChanged: ClassVar[Signal] = ... # manualExposureTimeChanged(float)
    manualIsoSensitivityChanged: ClassVar[Signal] = ... # manualIsoSensitivityChanged(int)
    maximumZoomFactorChanged : ClassVar[Signal] = ... # maximumZoomFactorChanged(float)
    minimumZoomFactorChanged : ClassVar[Signal] = ... # minimumZoomFactorChanged(float)
    saturationChanged        : ClassVar[Signal] = ... # saturationChanged()
    supportedFeaturesChanged : ClassVar[Signal] = ... # supportedFeaturesChanged()
    torchModeChanged         : ClassVar[Signal] = ... # torchModeChanged()
    whiteBalanceModeChanged  : ClassVar[Signal] = ... # whiteBalanceModeChanged()
    zoomFactorChanged        : ClassVar[Signal] = ... # zoomFactorChanged(float)

    class Error(enum.Enum):

        NoError                  : QCamera.Error = ... # 0x0
        CameraError              : QCamera.Error = ... # 0x1

    class ExposureMode(enum.Enum):

        ExposureAuto             : QCamera.ExposureMode = ... # 0x0
        ExposureManual           : QCamera.ExposureMode = ... # 0x1
        ExposurePortrait         : QCamera.ExposureMode = ... # 0x2
        ExposureNight            : QCamera.ExposureMode = ... # 0x3
        ExposureSports           : QCamera.ExposureMode = ... # 0x4
        ExposureSnow             : QCamera.ExposureMode = ... # 0x5
        ExposureBeach            : QCamera.ExposureMode = ... # 0x6
        ExposureAction           : QCamera.ExposureMode = ... # 0x7
        ExposureLandscape        : QCamera.ExposureMode = ... # 0x8
        ExposureNightPortrait    : QCamera.ExposureMode = ... # 0x9
        ExposureTheatre          : QCamera.ExposureMode = ... # 0xa
        ExposureSunset           : QCamera.ExposureMode = ... # 0xb
        ExposureSteadyPhoto      : QCamera.ExposureMode = ... # 0xc
        ExposureFireworks        : QCamera.ExposureMode = ... # 0xd
        ExposureParty            : QCamera.ExposureMode = ... # 0xe
        ExposureCandlelight      : QCamera.ExposureMode = ... # 0xf
        ExposureBarcode          : QCamera.ExposureMode = ... # 0x10

    class Feature(enum.Flag):

        ColorTemperature         : QCamera.Feature = ... # 0x1
        ExposureCompensation     : QCamera.Feature = ... # 0x2
        IsoSensitivity           : QCamera.Feature = ... # 0x4
        ManualExposureTime       : QCamera.Feature = ... # 0x8
        CustomFocusPoint         : QCamera.Feature = ... # 0x10
        FocusDistance            : QCamera.Feature = ... # 0x20

    class FlashMode(enum.Enum):

        FlashOff                 : QCamera.FlashMode = ... # 0x0
        FlashOn                  : QCamera.FlashMode = ... # 0x1
        FlashAuto                : QCamera.FlashMode = ... # 0x2

    class FocusMode(enum.Enum):

        FocusModeAuto            : QCamera.FocusMode = ... # 0x0
        FocusModeAutoNear        : QCamera.FocusMode = ... # 0x1
        FocusModeAutoFar         : QCamera.FocusMode = ... # 0x2
        FocusModeHyperfocal      : QCamera.FocusMode = ... # 0x3
        FocusModeInfinity        : QCamera.FocusMode = ... # 0x4
        FocusModeManual          : QCamera.FocusMode = ... # 0x5

    class TorchMode(enum.Enum):

        TorchOff                 : QCamera.TorchMode = ... # 0x0
        TorchOn                  : QCamera.TorchMode = ... # 0x1
        TorchAuto                : QCamera.TorchMode = ... # 0x2

    class WhiteBalanceMode(enum.Enum):

        WhiteBalanceAuto         : QCamera.WhiteBalanceMode = ... # 0x0
        WhiteBalanceManual       : QCamera.WhiteBalanceMode = ... # 0x1
        WhiteBalanceSunlight     : QCamera.WhiteBalanceMode = ... # 0x2
        WhiteBalanceCloudy       : QCamera.WhiteBalanceMode = ... # 0x3
        WhiteBalanceShade        : QCamera.WhiteBalanceMode = ... # 0x4
        WhiteBalanceTungsten     : QCamera.WhiteBalanceMode = ... # 0x5
        WhiteBalanceFluorescent  : QCamera.WhiteBalanceMode = ... # 0x6
        WhiteBalanceFlash        : QCamera.WhiteBalanceMode = ... # 0x7
        WhiteBalanceSunset       : QCamera.WhiteBalanceMode = ... # 0x8


    @overload
    def __init__(self, cameraDevice: PySide6.QtMultimedia.QCameraDevice, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, position: PySide6.QtMultimedia.QCameraDevice.Position, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def cameraDevice(self) -> PySide6.QtMultimedia.QCameraDevice: ...
    def cameraFormat(self) -> PySide6.QtMultimedia.QCameraFormat: ...
    def captureSession(self) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def colorTemperature(self) -> int: ...
    def customFocusPoint(self) -> PySide6.QtCore.QPointF: ...
    def error(self) -> PySide6.QtMultimedia.QCamera.Error: ...
    def errorString(self) -> str: ...
    def exposureCompensation(self) -> float: ...
    def exposureMode(self) -> PySide6.QtMultimedia.QCamera.ExposureMode: ...
    def exposureTime(self) -> float: ...
    def flashMode(self) -> PySide6.QtMultimedia.QCamera.FlashMode: ...
    def focusDistance(self) -> float: ...
    def focusMode(self) -> PySide6.QtMultimedia.QCamera.FocusMode: ...
    def focusPoint(self) -> PySide6.QtCore.QPointF: ...
    def isActive(self) -> bool: ...
    def isAvailable(self) -> bool: ...
    def isExposureModeSupported(self, mode: PySide6.QtMultimedia.QCamera.ExposureMode) -> bool: ...
    def isFlashModeSupported(self, mode: PySide6.QtMultimedia.QCamera.FlashMode) -> bool: ...
    def isFlashReady(self) -> bool: ...
    def isFocusModeSupported(self, mode: PySide6.QtMultimedia.QCamera.FocusMode) -> bool: ...
    def isTorchModeSupported(self, mode: PySide6.QtMultimedia.QCamera.TorchMode) -> bool: ...
    def isWhiteBalanceModeSupported(self, mode: PySide6.QtMultimedia.QCamera.WhiteBalanceMode) -> bool: ...
    def isoSensitivity(self) -> int: ...
    def manualExposureTime(self) -> float: ...
    def manualIsoSensitivity(self) -> int: ...
    def maximumExposureTime(self) -> float: ...
    def maximumIsoSensitivity(self) -> int: ...
    def maximumZoomFactor(self) -> float: ...
    def minimumExposureTime(self) -> float: ...
    def minimumIsoSensitivity(self) -> int: ...
    def minimumZoomFactor(self) -> float: ...
    def setActive(self, active: bool) -> None: ...
    def setAutoExposureTime(self) -> None: ...
    def setAutoIsoSensitivity(self) -> None: ...
    def setCameraDevice(self, cameraDevice: PySide6.QtMultimedia.QCameraDevice) -> None: ...
    def setCameraFormat(self, format: PySide6.QtMultimedia.QCameraFormat) -> None: ...
    def setColorTemperature(self, colorTemperature: int) -> None: ...
    def setCustomFocusPoint(self, point: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element]) -> None: ...
    def setExposureCompensation(self, ev: float) -> None: ...
    def setExposureMode(self, mode: PySide6.QtMultimedia.QCamera.ExposureMode) -> None: ...
    def setFlashMode(self, mode: PySide6.QtMultimedia.QCamera.FlashMode) -> None: ...
    def setFocusDistance(self, d: float) -> None: ...
    def setFocusMode(self, mode: PySide6.QtMultimedia.QCamera.FocusMode) -> None: ...
    def setManualExposureTime(self, seconds: float) -> None: ...
    def setManualIsoSensitivity(self, iso: int) -> None: ...
    def setTorchMode(self, mode: PySide6.QtMultimedia.QCamera.TorchMode) -> None: ...
    def setWhiteBalanceMode(self, mode: PySide6.QtMultimedia.QCamera.WhiteBalanceMode) -> None: ...
    def setZoomFactor(self, factor: float) -> None: ...
    def start(self) -> None: ...
    def stop(self) -> None: ...
    def supportedFeatures(self) -> PySide6.QtMultimedia.QCamera.Feature: ...
    def torchMode(self) -> PySide6.QtMultimedia.QCamera.TorchMode: ...
    def whiteBalanceMode(self) -> PySide6.QtMultimedia.QCamera.WhiteBalanceMode: ...
    def zoomFactor(self) -> float: ...
    def zoomTo(self, zoom: float, rate: float) -> None: ...


class QCameraDevice(Shiboken.Object):

    class Position(enum.Enum):

        UnspecifiedPosition      : QCameraDevice.Position = ... # 0x0
        BackFace                 : QCameraDevice.Position = ... # 0x1
        FrontFace                : QCameraDevice.Position = ... # 0x2


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtMultimedia.QCameraDevice) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def description(self) -> str: ...
    def id(self) -> PySide6.QtCore.QByteArray: ...
    def isDefault(self) -> bool: ...
    def isNull(self) -> bool: ...
    def photoResolutions(self) -> List[PySide6.QtCore.QSize]: ...
    def position(self) -> PySide6.QtMultimedia.QCameraDevice.Position: ...
    def videoFormats(self) -> List[PySide6.QtMultimedia.QCameraFormat]: ...


class QCameraFormat(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtMultimedia.QCameraFormat) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def isNull(self) -> bool: ...
    def maxFrameRate(self) -> float: ...
    def minFrameRate(self) -> float: ...
    def pixelFormat(self) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    def resolution(self) -> PySide6.QtCore.QSize: ...


class QCapturableWindow(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtMultimedia.QCapturableWindow) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def description(self) -> str: ...
    def isValid(self) -> bool: ...
    def swap(self, other: PySide6.QtMultimedia.QCapturableWindow) -> None: ...


class QImageCapture(PySide6.QtCore.QObject):

    errorChanged             : ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(int,QImageCapture::Error,QString)
    fileFormatChanged        : ClassVar[Signal] = ... # fileFormatChanged()
    imageAvailable           : ClassVar[Signal] = ... # imageAvailable(int,QVideoFrame)
    imageCaptured            : ClassVar[Signal] = ... # imageCaptured(int,QImage)
    imageExposed             : ClassVar[Signal] = ... # imageExposed(int)
    imageMetadataAvailable   : ClassVar[Signal] = ... # imageMetadataAvailable(int,QMediaMetaData)
    imageSaved               : ClassVar[Signal] = ... # imageSaved(int,QString)
    metaDataChanged          : ClassVar[Signal] = ... # metaDataChanged()
    qualityChanged           : ClassVar[Signal] = ... # qualityChanged()
    readyForCaptureChanged   : ClassVar[Signal] = ... # readyForCaptureChanged(bool)
    resolutionChanged        : ClassVar[Signal] = ... # resolutionChanged()

    class Error(enum.Enum):

        NoError                  : QImageCapture.Error = ... # 0x0
        NotReadyError            : QImageCapture.Error = ... # 0x1
        ResourceError            : QImageCapture.Error = ... # 0x2
        OutOfSpaceError          : QImageCapture.Error = ... # 0x3
        NotSupportedFeatureError : QImageCapture.Error = ... # 0x4
        FormatError              : QImageCapture.Error = ... # 0x5

    class FileFormat(enum.Enum):

        UnspecifiedFormat        : QImageCapture.FileFormat = ... # 0x0
        JPEG                     : QImageCapture.FileFormat = ... # 0x1
        PNG                      : QImageCapture.FileFormat = ... # 0x2
        WebP                     : QImageCapture.FileFormat = ... # 0x3
        LastFileFormat           : QImageCapture.FileFormat = ... # 0x4
        Tiff                     : QImageCapture.FileFormat = ... # 0x4

    class Quality(enum.Enum):

        VeryLowQuality           : QImageCapture.Quality = ... # 0x0
        LowQuality               : QImageCapture.Quality = ... # 0x1
        NormalQuality            : QImageCapture.Quality = ... # 0x2
        HighQuality              : QImageCapture.Quality = ... # 0x3
        VeryHighQuality          : QImageCapture.Quality = ... # 0x4


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def addMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData) -> None: ...
    def capture(self) -> int: ...
    def captureSession(self) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def captureToFile(self, location: str = ...) -> int: ...
    def error(self) -> PySide6.QtMultimedia.QImageCapture.Error: ...
    def errorString(self) -> str: ...
    def fileFormat(self) -> PySide6.QtMultimedia.QImageCapture.FileFormat: ...
    @staticmethod
    def fileFormatDescription(c: PySide6.QtMultimedia.QImageCapture.FileFormat) -> str: ...
    @staticmethod
    def fileFormatName(c: PySide6.QtMultimedia.QImageCapture.FileFormat) -> str: ...
    def isAvailable(self) -> bool: ...
    def isReadyForCapture(self) -> bool: ...
    def metaData(self) -> PySide6.QtMultimedia.QMediaMetaData: ...
    def quality(self) -> PySide6.QtMultimedia.QImageCapture.Quality: ...
    def resolution(self) -> PySide6.QtCore.QSize: ...
    def setFileFormat(self, format: PySide6.QtMultimedia.QImageCapture.FileFormat) -> None: ...
    def setMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData) -> None: ...
    def setQuality(self, quality: PySide6.QtMultimedia.QImageCapture.Quality) -> None: ...
    @overload
    def setResolution(self, arg__1: PySide6.QtCore.QSize) -> None: ...
    @overload
    def setResolution(self, width: int, height: int) -> None: ...
    @staticmethod
    def supportedFormats() -> List[PySide6.QtMultimedia.QImageCapture.FileFormat]: ...


class QIntList(object): ...


class QMediaCaptureSession(PySide6.QtCore.QObject):

    audioInputChanged        : ClassVar[Signal] = ... # audioInputChanged()
    audioOutputChanged       : ClassVar[Signal] = ... # audioOutputChanged()
    cameraChanged            : ClassVar[Signal] = ... # cameraChanged()
    imageCaptureChanged      : ClassVar[Signal] = ... # imageCaptureChanged()
    recorderChanged          : ClassVar[Signal] = ... # recorderChanged()
    screenCaptureChanged     : ClassVar[Signal] = ... # screenCaptureChanged()
    videoOutputChanged       : ClassVar[Signal] = ... # videoOutputChanged()
    windowCaptureChanged     : ClassVar[Signal] = ... # windowCaptureChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def audioInput(self) -> PySide6.QtMultimedia.QAudioInput: ...
    def audioOutput(self) -> PySide6.QtMultimedia.QAudioOutput: ...
    def camera(self) -> PySide6.QtMultimedia.QCamera: ...
    def imageCapture(self) -> PySide6.QtMultimedia.QImageCapture: ...
    def recorder(self) -> PySide6.QtMultimedia.QMediaRecorder: ...
    def screenCapture(self) -> PySide6.QtMultimedia.QScreenCapture: ...
    def setAudioInput(self, input: PySide6.QtMultimedia.QAudioInput) -> None: ...
    def setAudioOutput(self, output: PySide6.QtMultimedia.QAudioOutput) -> None: ...
    def setCamera(self, camera: PySide6.QtMultimedia.QCamera) -> None: ...
    def setImageCapture(self, imageCapture: PySide6.QtMultimedia.QImageCapture) -> None: ...
    def setRecorder(self, recorder: PySide6.QtMultimedia.QMediaRecorder) -> None: ...
    def setScreenCapture(self, screenCapture: PySide6.QtMultimedia.QScreenCapture) -> None: ...
    def setVideoOutput(self, output: PySide6.QtCore.QObject) -> None: ...
    def setVideoSink(self, sink: PySide6.QtMultimedia.QVideoSink) -> None: ...
    def setWindowCapture(self, windowCapture: PySide6.QtMultimedia.QWindowCapture) -> None: ...
    def videoOutput(self) -> PySide6.QtCore.QObject: ...
    def videoSink(self) -> PySide6.QtMultimedia.QVideoSink: ...
    def windowCapture(self) -> PySide6.QtMultimedia.QWindowCapture: ...


class QMediaDevices(PySide6.QtCore.QObject):

    audioInputsChanged       : ClassVar[Signal] = ... # audioInputsChanged()
    audioOutputsChanged      : ClassVar[Signal] = ... # audioOutputsChanged()
    videoInputsChanged       : ClassVar[Signal] = ... # videoInputsChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def audioInputs() -> List[PySide6.QtMultimedia.QAudioDevice]: ...
    @staticmethod
    def audioOutputs() -> List[PySide6.QtMultimedia.QAudioDevice]: ...
    def connectNotify(self, signal: PySide6.QtCore.QMetaMethod) -> None: ...
    @staticmethod
    def defaultAudioInput() -> PySide6.QtMultimedia.QAudioDevice: ...
    @staticmethod
    def defaultAudioOutput() -> PySide6.QtMultimedia.QAudioDevice: ...
    @staticmethod
    def defaultVideoInput() -> PySide6.QtMultimedia.QCameraDevice: ...
    @staticmethod
    def videoInputs() -> List[PySide6.QtMultimedia.QCameraDevice]: ...


class QMediaFormat(Shiboken.Object):

    class AudioCodec(enum.Enum):

        Unspecified              : QMediaFormat.AudioCodec = ... # -0x1
        MP3                      : QMediaFormat.AudioCodec = ... # 0x0
        AAC                      : QMediaFormat.AudioCodec = ... # 0x1
        AC3                      : QMediaFormat.AudioCodec = ... # 0x2
        EAC3                     : QMediaFormat.AudioCodec = ... # 0x3
        FLAC                     : QMediaFormat.AudioCodec = ... # 0x4
        DolbyTrueHD              : QMediaFormat.AudioCodec = ... # 0x5
        Opus                     : QMediaFormat.AudioCodec = ... # 0x6
        Vorbis                   : QMediaFormat.AudioCodec = ... # 0x7
        Wave                     : QMediaFormat.AudioCodec = ... # 0x8
        WMA                      : QMediaFormat.AudioCodec = ... # 0x9
        ALAC                     : QMediaFormat.AudioCodec = ... # 0xa
        LastAudioCodec           : QMediaFormat.AudioCodec = ... # 0xa

    class ConversionMode(enum.Enum):

        Encode                   : QMediaFormat.ConversionMode = ... # 0x0
        Decode                   : QMediaFormat.ConversionMode = ... # 0x1

    class FileFormat(enum.Enum):

        UnspecifiedFormat        : QMediaFormat.FileFormat = ... # -0x1
        WMV                      : QMediaFormat.FileFormat = ... # 0x0
        AVI                      : QMediaFormat.FileFormat = ... # 0x1
        Matroska                 : QMediaFormat.FileFormat = ... # 0x2
        MPEG4                    : QMediaFormat.FileFormat = ... # 0x3
        Ogg                      : QMediaFormat.FileFormat = ... # 0x4
        QuickTime                : QMediaFormat.FileFormat = ... # 0x5
        WebM                     : QMediaFormat.FileFormat = ... # 0x6
        Mpeg4Audio               : QMediaFormat.FileFormat = ... # 0x7
        AAC                      : QMediaFormat.FileFormat = ... # 0x8
        WMA                      : QMediaFormat.FileFormat = ... # 0x9
        MP3                      : QMediaFormat.FileFormat = ... # 0xa
        FLAC                     : QMediaFormat.FileFormat = ... # 0xb
        LastFileFormat           : QMediaFormat.FileFormat = ... # 0xc
        Wave                     : QMediaFormat.FileFormat = ... # 0xc

    class ResolveFlags(enum.Enum):

        NoFlags                  : QMediaFormat.ResolveFlags = ... # 0x0
        RequiresVideo            : QMediaFormat.ResolveFlags = ... # 0x1

    class VideoCodec(enum.Enum):

        Unspecified              : QMediaFormat.VideoCodec = ... # -0x1
        MPEG1                    : QMediaFormat.VideoCodec = ... # 0x0
        MPEG2                    : QMediaFormat.VideoCodec = ... # 0x1
        MPEG4                    : QMediaFormat.VideoCodec = ... # 0x2
        H264                     : QMediaFormat.VideoCodec = ... # 0x3
        H265                     : QMediaFormat.VideoCodec = ... # 0x4
        VP8                      : QMediaFormat.VideoCodec = ... # 0x5
        VP9                      : QMediaFormat.VideoCodec = ... # 0x6
        AV1                      : QMediaFormat.VideoCodec = ... # 0x7
        Theora                   : QMediaFormat.VideoCodec = ... # 0x8
        WMV                      : QMediaFormat.VideoCodec = ... # 0x9
        LastVideoCodec           : QMediaFormat.VideoCodec = ... # 0xa
        MotionJPEG               : QMediaFormat.VideoCodec = ... # 0xa


    @overload
    def __init__(self, format: PySide6.QtMultimedia.QMediaFormat.FileFormat = ...) -> None: ...
    @overload
    def __init__(self, other: Union[PySide6.QtMultimedia.QMediaFormat, PySide6.QtMultimedia.QMediaFormat.FileFormat]) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def audioCodec(self) -> PySide6.QtMultimedia.QMediaFormat.AudioCodec: ...
    @staticmethod
    def audioCodecDescription(codec: PySide6.QtMultimedia.QMediaFormat.AudioCodec) -> str: ...
    @staticmethod
    def audioCodecName(codec: PySide6.QtMultimedia.QMediaFormat.AudioCodec) -> str: ...
    def fileFormat(self) -> PySide6.QtMultimedia.QMediaFormat.FileFormat: ...
    @staticmethod
    def fileFormatDescription(fileFormat: PySide6.QtMultimedia.QMediaFormat.FileFormat) -> str: ...
    @staticmethod
    def fileFormatName(fileFormat: PySide6.QtMultimedia.QMediaFormat.FileFormat) -> str: ...
    def isSupported(self, mode: PySide6.QtMultimedia.QMediaFormat.ConversionMode) -> bool: ...
    def mimeType(self) -> PySide6.QtCore.QMimeType: ...
    def resolveForEncoding(self, flags: PySide6.QtMultimedia.QMediaFormat.ResolveFlags) -> None: ...
    def setAudioCodec(self, codec: PySide6.QtMultimedia.QMediaFormat.AudioCodec) -> None: ...
    def setFileFormat(self, f: PySide6.QtMultimedia.QMediaFormat.FileFormat) -> None: ...
    def setVideoCodec(self, codec: PySide6.QtMultimedia.QMediaFormat.VideoCodec) -> None: ...
    def supportedAudioCodecs(self, m: PySide6.QtMultimedia.QMediaFormat.ConversionMode) -> List[PySide6.QtMultimedia.QMediaFormat.AudioCodec]: ...
    def supportedFileFormats(self, m: PySide6.QtMultimedia.QMediaFormat.ConversionMode) -> List[PySide6.QtMultimedia.QMediaFormat.FileFormat]: ...
    def supportedVideoCodecs(self, m: PySide6.QtMultimedia.QMediaFormat.ConversionMode) -> List[PySide6.QtMultimedia.QMediaFormat.VideoCodec]: ...
    def swap(self, other: Union[PySide6.QtMultimedia.QMediaFormat, PySide6.QtMultimedia.QMediaFormat.FileFormat]) -> None: ...
    def videoCodec(self) -> PySide6.QtMultimedia.QMediaFormat.VideoCodec: ...
    @staticmethod
    def videoCodecDescription(codec: PySide6.QtMultimedia.QMediaFormat.VideoCodec) -> str: ...
    @staticmethod
    def videoCodecName(codec: PySide6.QtMultimedia.QMediaFormat.VideoCodec) -> str: ...


class QMediaMetaData(Shiboken.Object):

    class Key(enum.Enum):

        Title                    : QMediaMetaData.Key = ... # 0x0
        Author                   : QMediaMetaData.Key = ... # 0x1
        Comment                  : QMediaMetaData.Key = ... # 0x2
        Description              : QMediaMetaData.Key = ... # 0x3
        Genre                    : QMediaMetaData.Key = ... # 0x4
        Date                     : QMediaMetaData.Key = ... # 0x5
        Language                 : QMediaMetaData.Key = ... # 0x6
        Publisher                : QMediaMetaData.Key = ... # 0x7
        Copyright                : QMediaMetaData.Key = ... # 0x8
        Url                      : QMediaMetaData.Key = ... # 0x9
        Duration                 : QMediaMetaData.Key = ... # 0xa
        MediaType                : QMediaMetaData.Key = ... # 0xb
        FileFormat               : QMediaMetaData.Key = ... # 0xc
        AudioBitRate             : QMediaMetaData.Key = ... # 0xd
        AudioCodec               : QMediaMetaData.Key = ... # 0xe
        VideoBitRate             : QMediaMetaData.Key = ... # 0xf
        VideoCodec               : QMediaMetaData.Key = ... # 0x10
        VideoFrameRate           : QMediaMetaData.Key = ... # 0x11
        AlbumTitle               : QMediaMetaData.Key = ... # 0x12
        AlbumArtist              : QMediaMetaData.Key = ... # 0x13
        ContributingArtist       : QMediaMetaData.Key = ... # 0x14
        TrackNumber              : QMediaMetaData.Key = ... # 0x15
        Composer                 : QMediaMetaData.Key = ... # 0x16
        LeadPerformer            : QMediaMetaData.Key = ... # 0x17
        ThumbnailImage           : QMediaMetaData.Key = ... # 0x18
        CoverArtImage            : QMediaMetaData.Key = ... # 0x19
        Orientation              : QMediaMetaData.Key = ... # 0x1a
        Resolution               : QMediaMetaData.Key = ... # 0x1b


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, QMediaMetaData: PySide6.QtMultimedia.QMediaMetaData) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def clear(self) -> None: ...
    def insert(self, k: PySide6.QtMultimedia.QMediaMetaData.Key, value: Any) -> None: ...
    def isEmpty(self) -> bool: ...
    @staticmethod
    def keyType(key: PySide6.QtMultimedia.QMediaMetaData.Key) -> PySide6.QtCore.QMetaType: ...
    def keys(self) -> List[PySide6.QtMultimedia.QMediaMetaData.Key]: ...
    @staticmethod
    def metaDataKeyToString(k: PySide6.QtMultimedia.QMediaMetaData.Key) -> str: ...
    def remove(self, k: PySide6.QtMultimedia.QMediaMetaData.Key) -> None: ...
    def stringValue(self, k: PySide6.QtMultimedia.QMediaMetaData.Key) -> str: ...
    def value(self, k: PySide6.QtMultimedia.QMediaMetaData.Key) -> Any: ...


class QMediaPlayer(PySide6.QtCore.QObject):

    activeTracksChanged      : ClassVar[Signal] = ... # activeTracksChanged()
    audioOutputChanged       : ClassVar[Signal] = ... # audioOutputChanged()
    bufferProgressChanged    : ClassVar[Signal] = ... # bufferProgressChanged(float)
    durationChanged          : ClassVar[Signal] = ... # durationChanged(qlonglong)
    errorChanged             : ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QMediaPlayer::Error,QString)
    hasAudioChanged          : ClassVar[Signal] = ... # hasAudioChanged(bool)
    hasVideoChanged          : ClassVar[Signal] = ... # hasVideoChanged(bool)
    loopsChanged             : ClassVar[Signal] = ... # loopsChanged()
    mediaStatusChanged       : ClassVar[Signal] = ... # mediaStatusChanged(QMediaPlayer::MediaStatus)
    metaDataChanged          : ClassVar[Signal] = ... # metaDataChanged()
    playbackRateChanged      : ClassVar[Signal] = ... # playbackRateChanged(double)
    playbackStateChanged     : ClassVar[Signal] = ... # playbackStateChanged(QMediaPlayer::PlaybackState)
    playingChanged           : ClassVar[Signal] = ... # playingChanged(bool)
    positionChanged          : ClassVar[Signal] = ... # positionChanged(qlonglong)
    seekableChanged          : ClassVar[Signal] = ... # seekableChanged(bool)
    sourceChanged            : ClassVar[Signal] = ... # sourceChanged(QUrl)
    tracksChanged            : ClassVar[Signal] = ... # tracksChanged()
    videoOutputChanged       : ClassVar[Signal] = ... # videoOutputChanged()

    class Error(enum.Enum):

        NoError                  : QMediaPlayer.Error = ... # 0x0
        ResourceError            : QMediaPlayer.Error = ... # 0x1
        FormatError              : QMediaPlayer.Error = ... # 0x2
        NetworkError             : QMediaPlayer.Error = ... # 0x3
        AccessDeniedError        : QMediaPlayer.Error = ... # 0x4

    class Loops(enum.IntEnum):

        Infinite                 : QMediaPlayer.Loops = ... # -0x1
        Once                     : QMediaPlayer.Loops = ... # 0x1

    class MediaStatus(enum.Enum):

        NoMedia                  : QMediaPlayer.MediaStatus = ... # 0x0
        LoadingMedia             : QMediaPlayer.MediaStatus = ... # 0x1
        LoadedMedia              : QMediaPlayer.MediaStatus = ... # 0x2
        StalledMedia             : QMediaPlayer.MediaStatus = ... # 0x3
        BufferingMedia           : QMediaPlayer.MediaStatus = ... # 0x4
        BufferedMedia            : QMediaPlayer.MediaStatus = ... # 0x5
        EndOfMedia               : QMediaPlayer.MediaStatus = ... # 0x6
        InvalidMedia             : QMediaPlayer.MediaStatus = ... # 0x7

    class PlaybackState(enum.Enum):

        StoppedState             : QMediaPlayer.PlaybackState = ... # 0x0
        PlayingState             : QMediaPlayer.PlaybackState = ... # 0x1
        PausedState              : QMediaPlayer.PlaybackState = ... # 0x2


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def activeAudioTrack(self) -> int: ...
    def activeSubtitleTrack(self) -> int: ...
    def activeVideoTrack(self) -> int: ...
    def audioOutput(self) -> PySide6.QtMultimedia.QAudioOutput: ...
    def audioTracks(self) -> List[PySide6.QtMultimedia.QMediaMetaData]: ...
    def bufferProgress(self) -> float: ...
    def bufferedTimeRange(self) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    def duration(self) -> int: ...
    def error(self) -> PySide6.QtMultimedia.QMediaPlayer.Error: ...
    def errorString(self) -> str: ...
    def hasAudio(self) -> bool: ...
    def hasVideo(self) -> bool: ...
    def isAvailable(self) -> bool: ...
    def isPlaying(self) -> bool: ...
    def isSeekable(self) -> bool: ...
    def loops(self) -> int: ...
    def mediaStatus(self) -> PySide6.QtMultimedia.QMediaPlayer.MediaStatus: ...
    def metaData(self) -> PySide6.QtMultimedia.QMediaMetaData: ...
    def pause(self) -> None: ...
    def play(self) -> None: ...
    def playbackRate(self) -> float: ...
    def playbackState(self) -> PySide6.QtMultimedia.QMediaPlayer.PlaybackState: ...
    def position(self) -> int: ...
    def setActiveAudioTrack(self, index: int) -> None: ...
    def setActiveSubtitleTrack(self, index: int) -> None: ...
    def setActiveVideoTrack(self, index: int) -> None: ...
    def setAudioOutput(self, output: PySide6.QtMultimedia.QAudioOutput) -> None: ...
    def setLoops(self, loops: int) -> None: ...
    def setPlaybackRate(self, rate: float) -> None: ...
    def setPosition(self, position: int) -> None: ...
    def setSource(self, source: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def setSourceDevice(self, device: PySide6.QtCore.QIODevice, sourceUrl: Union[PySide6.QtCore.QUrl, str] = ...) -> None: ...
    def setVideoOutput(self, arg__1: PySide6.QtCore.QObject) -> None: ...
    def setVideoSink(self, sink: PySide6.QtMultimedia.QVideoSink) -> None: ...
    def source(self) -> PySide6.QtCore.QUrl: ...
    def sourceDevice(self) -> PySide6.QtCore.QIODevice: ...
    def stop(self) -> None: ...
    def subtitleTracks(self) -> List[PySide6.QtMultimedia.QMediaMetaData]: ...
    def videoOutput(self) -> PySide6.QtCore.QObject: ...
    def videoSink(self) -> PySide6.QtMultimedia.QVideoSink: ...
    def videoTracks(self) -> List[PySide6.QtMultimedia.QMediaMetaData]: ...


class QMediaRecorder(PySide6.QtCore.QObject):

    actualLocationChanged    : ClassVar[Signal] = ... # actualLocationChanged(QUrl)
    audioBitRateChanged      : ClassVar[Signal] = ... # audioBitRateChanged()
    audioChannelCountChanged : ClassVar[Signal] = ... # audioChannelCountChanged()
    audioSampleRateChanged   : ClassVar[Signal] = ... # audioSampleRateChanged()
    durationChanged          : ClassVar[Signal] = ... # durationChanged(qlonglong)
    encoderSettingsChanged   : ClassVar[Signal] = ... # encoderSettingsChanged()
    encodingModeChanged      : ClassVar[Signal] = ... # encodingModeChanged()
    errorChanged             : ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(Error,QString)
    mediaFormatChanged       : ClassVar[Signal] = ... # mediaFormatChanged()
    metaDataChanged          : ClassVar[Signal] = ... # metaDataChanged()
    qualityChanged           : ClassVar[Signal] = ... # qualityChanged()
    recorderStateChanged     : ClassVar[Signal] = ... # recorderStateChanged(RecorderState)
    videoBitRateChanged      : ClassVar[Signal] = ... # videoBitRateChanged()
    videoFrameRateChanged    : ClassVar[Signal] = ... # videoFrameRateChanged()
    videoResolutionChanged   : ClassVar[Signal] = ... # videoResolutionChanged()

    class EncodingMode(enum.Enum):

        ConstantQualityEncoding  : QMediaRecorder.EncodingMode = ... # 0x0
        ConstantBitRateEncoding  : QMediaRecorder.EncodingMode = ... # 0x1
        AverageBitRateEncoding   : QMediaRecorder.EncodingMode = ... # 0x2
        TwoPassEncoding          : QMediaRecorder.EncodingMode = ... # 0x3

    class Error(enum.Enum):

        NoError                  : QMediaRecorder.Error = ... # 0x0
        ResourceError            : QMediaRecorder.Error = ... # 0x1
        FormatError              : QMediaRecorder.Error = ... # 0x2
        OutOfSpaceError          : QMediaRecorder.Error = ... # 0x3
        LocationNotWritable      : QMediaRecorder.Error = ... # 0x4

    class Quality(enum.Enum):

        VeryLowQuality           : QMediaRecorder.Quality = ... # 0x0
        LowQuality               : QMediaRecorder.Quality = ... # 0x1
        NormalQuality            : QMediaRecorder.Quality = ... # 0x2
        HighQuality              : QMediaRecorder.Quality = ... # 0x3
        VeryHighQuality          : QMediaRecorder.Quality = ... # 0x4

    class RecorderState(enum.Enum):

        StoppedState             : QMediaRecorder.RecorderState = ... # 0x0
        RecordingState           : QMediaRecorder.RecorderState = ... # 0x1
        PausedState              : QMediaRecorder.RecorderState = ... # 0x2


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def actualLocation(self) -> PySide6.QtCore.QUrl: ...
    def addMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData) -> None: ...
    def audioBitRate(self) -> int: ...
    def audioChannelCount(self) -> int: ...
    def audioSampleRate(self) -> int: ...
    def captureSession(self) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def duration(self) -> int: ...
    def encodingMode(self) -> PySide6.QtMultimedia.QMediaRecorder.EncodingMode: ...
    def error(self) -> PySide6.QtMultimedia.QMediaRecorder.Error: ...
    def errorString(self) -> str: ...
    def isAvailable(self) -> bool: ...
    def mediaFormat(self) -> PySide6.QtMultimedia.QMediaFormat: ...
    def metaData(self) -> PySide6.QtMultimedia.QMediaMetaData: ...
    def outputLocation(self) -> PySide6.QtCore.QUrl: ...
    def pause(self) -> None: ...
    def quality(self) -> PySide6.QtMultimedia.QMediaRecorder.Quality: ...
    def record(self) -> None: ...
    def recorderState(self) -> PySide6.QtMultimedia.QMediaRecorder.RecorderState: ...
    def setAudioBitRate(self, bitRate: int) -> None: ...
    def setAudioChannelCount(self, channels: int) -> None: ...
    def setAudioSampleRate(self, sampleRate: int) -> None: ...
    def setEncodingMode(self, arg__1: PySide6.QtMultimedia.QMediaRecorder.EncodingMode) -> None: ...
    def setMediaFormat(self, format: Union[PySide6.QtMultimedia.QMediaFormat, PySide6.QtMultimedia.QMediaFormat.FileFormat]) -> None: ...
    def setMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData) -> None: ...
    def setOutputLocation(self, location: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def setQuality(self, quality: PySide6.QtMultimedia.QMediaRecorder.Quality) -> None: ...
    def setVideoBitRate(self, bitRate: int) -> None: ...
    def setVideoFrameRate(self, frameRate: float) -> None: ...
    @overload
    def setVideoResolution(self, arg__1: PySide6.QtCore.QSize) -> None: ...
    @overload
    def setVideoResolution(self, width: int, height: int) -> None: ...
    def stop(self) -> None: ...
    def videoBitRate(self) -> int: ...
    def videoFrameRate(self) -> float: ...
    def videoResolution(self) -> PySide6.QtCore.QSize: ...


class QMediaTimeRange(Shiboken.Object):

    class Interval(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, Interval: PySide6.QtMultimedia.QMediaTimeRange.Interval) -> None: ...
        @overload
        def __init__(self, start: int, end: int) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def contains(self, time: int) -> bool: ...
        def end(self) -> int: ...
        def isNormal(self) -> bool: ...
        def normalized(self) -> PySide6.QtMultimedia.QMediaTimeRange.Interval: ...
        def start(self) -> int: ...
        def translated(self, offset: int) -> PySide6.QtMultimedia.QMediaTimeRange.Interval: ...


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange.Interval) -> None: ...
    @overload
    def __init__(self, range: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> None: ...
    @overload
    def __init__(self, start: int, end: int) -> None: ...

    def __add__(self, r2: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @staticmethod
    def __copy__() -> None: ...
    @overload
    def __iadd__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange.Interval) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @overload
    def __iadd__(self, arg__1: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @overload
    def __isub__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange.Interval) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @overload
    def __isub__(self, arg__1: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    def __sub__(self, r2: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @overload
    def addInterval(self, interval: PySide6.QtMultimedia.QMediaTimeRange.Interval) -> None: ...
    @overload
    def addInterval(self, start: int, end: int) -> None: ...
    def addTimeRange(self, arg__1: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> None: ...
    def clear(self) -> None: ...
    def contains(self, time: int) -> bool: ...
    def earliestTime(self) -> int: ...
    def intervals(self) -> List[PySide6.QtMultimedia.QMediaTimeRange.Interval]: ...
    def isContinuous(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def latestTime(self) -> int: ...
    @overload
    def removeInterval(self, interval: PySide6.QtMultimedia.QMediaTimeRange.Interval) -> None: ...
    @overload
    def removeInterval(self, start: int, end: int) -> None: ...
    def removeTimeRange(self, arg__1: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> None: ...
    def swap(self, other: Union[PySide6.QtMultimedia.QMediaTimeRange, PySide6.QtMultimedia.QMediaTimeRange.Interval]) -> None: ...


class QScreenCapture(PySide6.QtCore.QObject):

    activeChanged            : ClassVar[Signal] = ... # activeChanged(bool)
    errorChanged             : ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QScreenCapture::Error,QString)
    screenChanged            : ClassVar[Signal] = ... # screenChanged(QScreen*)

    class Error(enum.Enum):

        NoError                  : QScreenCapture.Error = ... # 0x0
        InternalError            : QScreenCapture.Error = ... # 0x1
        CapturingNotSupported    : QScreenCapture.Error = ... # 0x2
        CaptureFailed            : QScreenCapture.Error = ... # 0x4
        NotFound                 : QScreenCapture.Error = ... # 0x5


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def captureSession(self) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def error(self) -> PySide6.QtMultimedia.QScreenCapture.Error: ...
    def errorString(self) -> str: ...
    def isActive(self) -> bool: ...
    def screen(self) -> PySide6.QtGui.QScreen: ...
    def setActive(self, active: bool) -> None: ...
    def setScreen(self, screen: PySide6.QtGui.QScreen) -> None: ...
    def start(self) -> None: ...
    def stop(self) -> None: ...


class QSoundEffect(PySide6.QtCore.QObject):

    audioDeviceChanged       : ClassVar[Signal] = ... # audioDeviceChanged()
    loadedChanged            : ClassVar[Signal] = ... # loadedChanged()
    loopCountChanged         : ClassVar[Signal] = ... # loopCountChanged()
    loopsRemainingChanged    : ClassVar[Signal] = ... # loopsRemainingChanged()
    mutedChanged             : ClassVar[Signal] = ... # mutedChanged()
    playingChanged           : ClassVar[Signal] = ... # playingChanged()
    sourceChanged            : ClassVar[Signal] = ... # sourceChanged()
    statusChanged            : ClassVar[Signal] = ... # statusChanged()
    volumeChanged            : ClassVar[Signal] = ... # volumeChanged()

    class Loop(enum.Enum):

        Infinite                 : QSoundEffect.Loop = ... # -0x2

    class Status(enum.Enum):

        Null                     : QSoundEffect.Status = ... # 0x0
        Loading                  : QSoundEffect.Status = ... # 0x1
        Ready                    : QSoundEffect.Status = ... # 0x2
        Error                    : QSoundEffect.Status = ... # 0x3


    @overload
    def __init__(self, audioDevice: PySide6.QtMultimedia.QAudioDevice, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def audioDevice(self) -> PySide6.QtMultimedia.QAudioDevice: ...
    def isLoaded(self) -> bool: ...
    def isMuted(self) -> bool: ...
    def isPlaying(self) -> bool: ...
    def loopCount(self) -> int: ...
    def loopsRemaining(self) -> int: ...
    def play(self) -> None: ...
    def setAudioDevice(self, device: PySide6.QtMultimedia.QAudioDevice) -> None: ...
    def setLoopCount(self, loopCount: int) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def setSource(self, url: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    def source(self) -> PySide6.QtCore.QUrl: ...
    def status(self) -> PySide6.QtMultimedia.QSoundEffect.Status: ...
    def stop(self) -> None: ...
    @staticmethod
    def supportedMimeTypes() -> List[str]: ...
    def volume(self) -> float: ...


class QVideoFrame(Shiboken.Object):

    class HandleType(enum.Enum):

        NoHandle                 : QVideoFrame.HandleType = ... # 0x0
        RhiTextureHandle         : QVideoFrame.HandleType = ... # 0x1

    class MapMode(enum.Enum):

        NotMapped                : QVideoFrame.MapMode = ... # 0x0
        ReadOnly                 : QVideoFrame.MapMode = ... # 0x1
        WriteOnly                : QVideoFrame.MapMode = ... # 0x2
        ReadWrite                : QVideoFrame.MapMode = ... # 0x3

    class RotationAngle(enum.Enum):

        Rotation0                : QVideoFrame.RotationAngle = ... # 0x0
        Rotation90               : QVideoFrame.RotationAngle = ... # 0x5a
        Rotation180              : QVideoFrame.RotationAngle = ... # 0xb4
        Rotation270              : QVideoFrame.RotationAngle = ... # 0x10e


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, format: PySide6.QtMultimedia.QVideoFrameFormat) -> None: ...
    @overload
    def __init__(self, other: Union[PySide6.QtMultimedia.QVideoFrame, PySide6.QtMultimedia.QVideoFrameFormat]) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def bits(self, plane: int) -> bytes: ...
    def bytesPerLine(self, plane: int) -> int: ...
    def endTime(self) -> int: ...
    def handleType(self) -> PySide6.QtMultimedia.QVideoFrame.HandleType: ...
    def height(self) -> int: ...
    def isMapped(self) -> bool: ...
    def isReadable(self) -> bool: ...
    def isValid(self) -> bool: ...
    def isWritable(self) -> bool: ...
    def map(self, mode: PySide6.QtMultimedia.QVideoFrame.MapMode) -> bool: ...
    def mapMode(self) -> PySide6.QtMultimedia.QVideoFrame.MapMode: ...
    def mappedBytes(self, plane: int) -> int: ...
    def mirrored(self) -> bool: ...
    def pixelFormat(self) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    def planeCount(self) -> int: ...
    def rotationAngle(self) -> PySide6.QtMultimedia.QVideoFrame.RotationAngle: ...
    def setEndTime(self, time: int) -> None: ...
    def setMirrored(self, arg__1: bool) -> None: ...
    def setRotationAngle(self, arg__1: PySide6.QtMultimedia.QVideoFrame.RotationAngle) -> None: ...
    def setStartTime(self, time: int) -> None: ...
    def setSubtitleText(self, text: str) -> None: ...
    def size(self) -> PySide6.QtCore.QSize: ...
    def startTime(self) -> int: ...
    def subtitleText(self) -> str: ...
    def surfaceFormat(self) -> PySide6.QtMultimedia.QVideoFrameFormat: ...
    def swap(self, other: Union[PySide6.QtMultimedia.QVideoFrame, PySide6.QtMultimedia.QVideoFrameFormat]) -> None: ...
    def toImage(self) -> PySide6.QtGui.QImage: ...
    def unmap(self) -> None: ...
    def width(self) -> int: ...


class QVideoFrameFormat(Shiboken.Object):

    class ColorRange(enum.Enum):

        ColorRange_Unknown       : QVideoFrameFormat.ColorRange = ... # 0x0
        ColorRange_Video         : QVideoFrameFormat.ColorRange = ... # 0x1
        ColorRange_Full          : QVideoFrameFormat.ColorRange = ... # 0x2

    class ColorSpace(enum.Enum):

        ColorSpace_Undefined     : QVideoFrameFormat.ColorSpace = ... # 0x0
        ColorSpace_BT601         : QVideoFrameFormat.ColorSpace = ... # 0x1
        ColorSpace_BT709         : QVideoFrameFormat.ColorSpace = ... # 0x2
        ColorSpace_AdobeRgb      : QVideoFrameFormat.ColorSpace = ... # 0x5
        ColorSpace_BT2020        : QVideoFrameFormat.ColorSpace = ... # 0x6

    class ColorTransfer(enum.Enum):

        ColorTransfer_Unknown    : QVideoFrameFormat.ColorTransfer = ... # 0x0
        ColorTransfer_BT709      : QVideoFrameFormat.ColorTransfer = ... # 0x1
        ColorTransfer_BT601      : QVideoFrameFormat.ColorTransfer = ... # 0x2
        ColorTransfer_Linear     : QVideoFrameFormat.ColorTransfer = ... # 0x3
        ColorTransfer_Gamma22    : QVideoFrameFormat.ColorTransfer = ... # 0x4
        ColorTransfer_Gamma28    : QVideoFrameFormat.ColorTransfer = ... # 0x5
        ColorTransfer_ST2084     : QVideoFrameFormat.ColorTransfer = ... # 0x6
        ColorTransfer_STD_B67    : QVideoFrameFormat.ColorTransfer = ... # 0x7

    class Direction(enum.Enum):

        TopToBottom              : QVideoFrameFormat.Direction = ... # 0x0
        BottomToTop              : QVideoFrameFormat.Direction = ... # 0x1

    class PixelFormat(enum.Enum):

        Format_Invalid           : QVideoFrameFormat.PixelFormat = ... # 0x0
        Format_ARGB8888          : QVideoFrameFormat.PixelFormat = ... # 0x1
        Format_ARGB8888_Premultiplied: QVideoFrameFormat.PixelFormat = ... # 0x2
        Format_XRGB8888          : QVideoFrameFormat.PixelFormat = ... # 0x3
        Format_BGRA8888          : QVideoFrameFormat.PixelFormat = ... # 0x4
        Format_BGRA8888_Premultiplied: QVideoFrameFormat.PixelFormat = ... # 0x5
        Format_BGRX8888          : QVideoFrameFormat.PixelFormat = ... # 0x6
        Format_ABGR8888          : QVideoFrameFormat.PixelFormat = ... # 0x7
        Format_XBGR8888          : QVideoFrameFormat.PixelFormat = ... # 0x8
        Format_RGBA8888          : QVideoFrameFormat.PixelFormat = ... # 0x9
        Format_RGBX8888          : QVideoFrameFormat.PixelFormat = ... # 0xa
        Format_AYUV              : QVideoFrameFormat.PixelFormat = ... # 0xb
        Format_AYUV_Premultiplied: QVideoFrameFormat.PixelFormat = ... # 0xc
        Format_YUV420P           : QVideoFrameFormat.PixelFormat = ... # 0xd
        Format_YUV422P           : QVideoFrameFormat.PixelFormat = ... # 0xe
        Format_YV12              : QVideoFrameFormat.PixelFormat = ... # 0xf
        Format_UYVY              : QVideoFrameFormat.PixelFormat = ... # 0x10
        Format_YUYV              : QVideoFrameFormat.PixelFormat = ... # 0x11
        Format_NV12              : QVideoFrameFormat.PixelFormat = ... # 0x12
        Format_NV21              : QVideoFrameFormat.PixelFormat = ... # 0x13
        Format_IMC1              : QVideoFrameFormat.PixelFormat = ... # 0x14
        Format_IMC2              : QVideoFrameFormat.PixelFormat = ... # 0x15
        Format_IMC3              : QVideoFrameFormat.PixelFormat = ... # 0x16
        Format_IMC4              : QVideoFrameFormat.PixelFormat = ... # 0x17
        Format_Y8                : QVideoFrameFormat.PixelFormat = ... # 0x18
        Format_Y16               : QVideoFrameFormat.PixelFormat = ... # 0x19
        Format_P010              : QVideoFrameFormat.PixelFormat = ... # 0x1a
        Format_P016              : QVideoFrameFormat.PixelFormat = ... # 0x1b
        Format_SamplerExternalOES: QVideoFrameFormat.PixelFormat = ... # 0x1c
        Format_Jpeg              : QVideoFrameFormat.PixelFormat = ... # 0x1d
        Format_SamplerRect       : QVideoFrameFormat.PixelFormat = ... # 0x1e
        Format_YUV420P10         : QVideoFrameFormat.PixelFormat = ... # 0x1f

    class YCbCrColorSpace(enum.Enum):

        YCbCr_Undefined          : QVideoFrameFormat.YCbCrColorSpace = ... # 0x0
        YCbCr_BT601              : QVideoFrameFormat.YCbCrColorSpace = ... # 0x1
        YCbCr_BT709              : QVideoFrameFormat.YCbCrColorSpace = ... # 0x2
        YCbCr_xvYCC601           : QVideoFrameFormat.YCbCrColorSpace = ... # 0x3
        YCbCr_xvYCC709           : QVideoFrameFormat.YCbCrColorSpace = ... # 0x4
        YCbCr_JPEG               : QVideoFrameFormat.YCbCrColorSpace = ... # 0x5
        YCbCr_BT2020             : QVideoFrameFormat.YCbCrColorSpace = ... # 0x6


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, format: PySide6.QtMultimedia.QVideoFrameFormat) -> None: ...
    @overload
    def __init__(self, size: PySide6.QtCore.QSize, pixelFormat: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def colorRange(self) -> PySide6.QtMultimedia.QVideoFrameFormat.ColorRange: ...
    def colorSpace(self) -> PySide6.QtMultimedia.QVideoFrameFormat.ColorSpace: ...
    def colorTransfer(self) -> PySide6.QtMultimedia.QVideoFrameFormat.ColorTransfer: ...
    def fragmentShaderFileName(self) -> str: ...
    def frameHeight(self) -> int: ...
    def frameRate(self) -> float: ...
    def frameSize(self) -> PySide6.QtCore.QSize: ...
    def frameWidth(self) -> int: ...
    @staticmethod
    def imageFormatFromPixelFormat(format: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat) -> PySide6.QtGui.QImage.Format: ...
    def isMirrored(self) -> bool: ...
    def isValid(self) -> bool: ...
    def maxLuminance(self) -> float: ...
    def pixelFormat(self) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    @staticmethod
    def pixelFormatFromImageFormat(format: PySide6.QtGui.QImage.Format) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    @staticmethod
    def pixelFormatToString(pixelFormat: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat) -> str: ...
    def planeCount(self) -> int: ...
    def scanLineDirection(self) -> PySide6.QtMultimedia.QVideoFrameFormat.Direction: ...
    def setColorRange(self, range: PySide6.QtMultimedia.QVideoFrameFormat.ColorRange) -> None: ...
    def setColorSpace(self, colorSpace: PySide6.QtMultimedia.QVideoFrameFormat.ColorSpace) -> None: ...
    def setColorTransfer(self, colorTransfer: PySide6.QtMultimedia.QVideoFrameFormat.ColorTransfer) -> None: ...
    def setFrameRate(self, rate: float) -> None: ...
    @overload
    def setFrameSize(self, size: PySide6.QtCore.QSize) -> None: ...
    @overload
    def setFrameSize(self, width: int, height: int) -> None: ...
    def setMaxLuminance(self, lum: float) -> None: ...
    def setMirrored(self, mirrored: bool) -> None: ...
    def setScanLineDirection(self, direction: PySide6.QtMultimedia.QVideoFrameFormat.Direction) -> None: ...
    def setViewport(self, viewport: PySide6.QtCore.QRect) -> None: ...
    def setYCbCrColorSpace(self, colorSpace: PySide6.QtMultimedia.QVideoFrameFormat.YCbCrColorSpace) -> None: ...
    def swap(self, other: PySide6.QtMultimedia.QVideoFrameFormat) -> None: ...
    def updateUniformData(self, dst: Union[PySide6.QtCore.QByteArray, bytes], frame: Union[PySide6.QtMultimedia.QVideoFrame, PySide6.QtMultimedia.QVideoFrameFormat], transform: Union[PySide6.QtGui.QMatrix4x4, PySide6.QtGui.QTransform], opacity: float) -> None: ...
    def vertexShaderFileName(self) -> str: ...
    def viewport(self) -> PySide6.QtCore.QRect: ...
    def yCbCrColorSpace(self) -> PySide6.QtMultimedia.QVideoFrameFormat.YCbCrColorSpace: ...


class QVideoSink(PySide6.QtCore.QObject):

    subtitleTextChanged      : ClassVar[Signal] = ... # subtitleTextChanged(QString)
    videoFrameChanged        : ClassVar[Signal] = ... # videoFrameChanged(QVideoFrame)
    videoSizeChanged         : ClassVar[Signal] = ... # videoSizeChanged()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def setSubtitleText(self, subtitle: str) -> None: ...
    def setVideoFrame(self, frame: Union[PySide6.QtMultimedia.QVideoFrame, PySide6.QtMultimedia.QVideoFrameFormat]) -> None: ...
    def subtitleText(self) -> str: ...
    def videoFrame(self) -> PySide6.QtMultimedia.QVideoFrame: ...
    def videoSize(self) -> PySide6.QtCore.QSize: ...


class QWindowCapture(PySide6.QtCore.QObject):

    activeChanged            : ClassVar[Signal] = ... # activeChanged(bool)
    errorChanged             : ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QWindowCapture::Error,QString)
    windowChanged            : ClassVar[Signal] = ... # windowChanged(QCapturableWindow)

    class Error(enum.Enum):

        NoError                  : QWindowCapture.Error = ... # 0x0
        InternalError            : QWindowCapture.Error = ... # 0x1
        CapturingNotSupported    : QWindowCapture.Error = ... # 0x2
        CaptureFailed            : QWindowCapture.Error = ... # 0x4
        NotFound                 : QWindowCapture.Error = ... # 0x5


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def capturableWindows() -> List[PySide6.QtMultimedia.QCapturableWindow]: ...
    def captureSession(self) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def error(self) -> PySide6.QtMultimedia.QWindowCapture.Error: ...
    def errorString(self) -> str: ...
    def isActive(self) -> bool: ...
    def setActive(self, active: bool) -> None: ...
    def setWindow(self, window: PySide6.QtMultimedia.QCapturableWindow) -> None: ...
    def start(self) -> None: ...
    def stop(self) -> None: ...
    def window(self) -> PySide6.QtMultimedia.QCapturableWindow: ...


# eof
