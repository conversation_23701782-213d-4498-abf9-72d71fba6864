# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtTextToSpeech, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtTextToSpeech`

import PySide6.QtTextToSpeech
import PySide6.QtCore

import enum
from typing import Any, ClassVar, Dict, List, Optional, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QIntList(object): ...


class QTextToSpeech(PySide6.QtCore.QObject):

    aboutToSynthesize        : ClassVar[Signal] = ... # aboutToSynthesize(qsizetype)
    engineChanged            : ClassVar[Signal] = ... # engineChanged(QString)
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QTextToSpeech::ErrorReason,QString)
    localeChanged            : ClassVar[Signal] = ... # localeChanged(QLocale)
    pitchChanged             : ClassVar[Signal] = ... # pitchChanged(double)
    rateChanged              : ClassVar[Signal] = ... # rateChanged(double)
    sayingWord               : ClassVar[Signal] = ... # sayingWord(QString,qsizetype,qsizetype,qsizetype)
    stateChanged             : ClassVar[Signal] = ... # stateChanged(QTextToSpeech::State)
    voiceChanged             : ClassVar[Signal] = ... # voiceChanged(QVoice)
    volumeChanged            : ClassVar[Signal] = ... # volumeChanged(double)

    class BoundaryHint(enum.Enum):

        Default                  : QTextToSpeech.BoundaryHint = ... # 0x0
        Immediate                : QTextToSpeech.BoundaryHint = ... # 0x1
        Word                     : QTextToSpeech.BoundaryHint = ... # 0x2
        Sentence                 : QTextToSpeech.BoundaryHint = ... # 0x3
        Utterance                : QTextToSpeech.BoundaryHint = ... # 0x4

    class Capability(enum.Flag):

        None_                    : QTextToSpeech.Capability = ... # 0x0
        Speak                    : QTextToSpeech.Capability = ... # 0x1
        PauseResume              : QTextToSpeech.Capability = ... # 0x2
        WordByWordProgress       : QTextToSpeech.Capability = ... # 0x4
        Synthesize               : QTextToSpeech.Capability = ... # 0x8

    class ErrorReason(enum.Enum):

        NoError                  : QTextToSpeech.ErrorReason = ... # 0x0
        Initialization           : QTextToSpeech.ErrorReason = ... # 0x1
        Configuration            : QTextToSpeech.ErrorReason = ... # 0x2
        Input                    : QTextToSpeech.ErrorReason = ... # 0x3
        Playback                 : QTextToSpeech.ErrorReason = ... # 0x4

    class State(enum.Enum):

        Ready                    : QTextToSpeech.State = ... # 0x0
        Speaking                 : QTextToSpeech.State = ... # 0x1
        Paused                   : QTextToSpeech.State = ... # 0x2
        Error                    : QTextToSpeech.State = ... # 0x3
        Synthesizing             : QTextToSpeech.State = ... # 0x4


    @overload
    def __init__(self, engine: str, params: Dict[str, Any], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, engine: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def allVoices(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> List[PySide6.QtTextToSpeech.QVoice]: ...
    @staticmethod
    def availableEngines() -> List[str]: ...
    def availableLocales(self) -> List[PySide6.QtCore.QLocale]: ...
    def availableVoices(self) -> List[PySide6.QtTextToSpeech.QVoice]: ...
    def engine(self) -> str: ...
    def engineCapabilities(self) -> PySide6.QtTextToSpeech.QTextToSpeech.Capability: ...
    def enqueue(self, text: str) -> int: ...
    def errorReason(self) -> PySide6.QtTextToSpeech.QTextToSpeech.ErrorReason: ...
    def errorString(self) -> str: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def pause(self, boundaryHint: PySide6.QtTextToSpeech.QTextToSpeech.BoundaryHint = ...) -> None: ...
    def pitch(self) -> float: ...
    def rate(self) -> float: ...
    def resume(self) -> None: ...
    def say(self, text: str) -> None: ...
    def setEngine(self, engine: str, params: Dict[str, Any] = ...) -> bool: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def setPitch(self, pitch: float) -> None: ...
    def setRate(self, rate: float) -> None: ...
    def setVoice(self, voice: PySide6.QtTextToSpeech.QVoice) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    def state(self) -> PySide6.QtTextToSpeech.QTextToSpeech.State: ...
    def stop(self, boundaryHint: PySide6.QtTextToSpeech.QTextToSpeech.BoundaryHint = ...) -> None: ...
    def voice(self) -> PySide6.QtTextToSpeech.QVoice: ...
    def volume(self) -> float: ...


class QTextToSpeechEngine(PySide6.QtCore.QObject):

    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QTextToSpeech::ErrorReason,QString)
    sayingWord               : ClassVar[Signal] = ... # sayingWord(QString,qsizetype,qsizetype)
    stateChanged             : ClassVar[Signal] = ... # stateChanged(QTextToSpeech::State)
    synthesized              : ClassVar[Signal] = ... # synthesized(QAudioFormat,QByteArray)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def availableLocales(self) -> List[PySide6.QtCore.QLocale]: ...
    def availableVoices(self) -> List[PySide6.QtTextToSpeech.QVoice]: ...
    def capabilities(self) -> PySide6.QtTextToSpeech.QTextToSpeech.Capability: ...
    @staticmethod
    def createVoice(name: str, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language], gender: PySide6.QtTextToSpeech.QVoice.Gender, age: PySide6.QtTextToSpeech.QVoice.Age, data: Any) -> PySide6.QtTextToSpeech.QVoice: ...
    def errorReason(self) -> PySide6.QtTextToSpeech.QTextToSpeech.ErrorReason: ...
    def errorString(self) -> str: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def pause(self, boundaryHint: PySide6.QtTextToSpeech.QTextToSpeech.BoundaryHint) -> None: ...
    def pitch(self) -> float: ...
    def rate(self) -> float: ...
    def resume(self) -> None: ...
    def say(self, text: str) -> None: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> bool: ...
    def setPitch(self, pitch: float) -> bool: ...
    def setRate(self, rate: float) -> bool: ...
    def setVoice(self, voice: PySide6.QtTextToSpeech.QVoice) -> bool: ...
    def setVolume(self, volume: float) -> bool: ...
    def state(self) -> PySide6.QtTextToSpeech.QTextToSpeech.State: ...
    def stop(self, boundaryHint: PySide6.QtTextToSpeech.QTextToSpeech.BoundaryHint) -> None: ...
    def synthesize(self, text: str) -> None: ...
    def voice(self) -> PySide6.QtTextToSpeech.QVoice: ...
    @staticmethod
    def voiceData(voice: PySide6.QtTextToSpeech.QVoice) -> Any: ...
    def volume(self) -> float: ...


class QVoice(Shiboken.Object):

    class Age(enum.Enum):

        Child                    : QVoice.Age = ... # 0x0
        Teenager                 : QVoice.Age = ... # 0x1
        Adult                    : QVoice.Age = ... # 0x2
        Senior                   : QVoice.Age = ... # 0x3
        Other                    : QVoice.Age = ... # 0x4

    class Gender(enum.Enum):

        Male                     : QVoice.Gender = ... # 0x0
        Female                   : QVoice.Gender = ... # 0x1
        Unknown                  : QVoice.Gender = ... # 0x2


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtTextToSpeech.QVoice) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def __lshift__(self, str: PySide6.QtCore.QDataStream) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, str: PySide6.QtCore.QDataStream) -> PySide6.QtCore.QDataStream: ...
    def age(self) -> PySide6.QtTextToSpeech.QVoice.Age: ...
    @staticmethod
    def ageName(age: PySide6.QtTextToSpeech.QVoice.Age) -> str: ...
    def gender(self) -> PySide6.QtTextToSpeech.QVoice.Gender: ...
    @staticmethod
    def genderName(gender: PySide6.QtTextToSpeech.QVoice.Gender) -> str: ...
    def language(self) -> PySide6.QtCore.QLocale.Language: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def name(self) -> str: ...
    def swap(self, other: PySide6.QtTextToSpeech.QVoice) -> None: ...


# eof
