[{"classes": [{"className": "QAmbientSound", "enums": [{"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite", "Once"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "loops", "notify": "loopsChanged", "read": "loops", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoops"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "autoPlay", "notify": "autoPlayChanged", "read": "autoPlay", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoPlay"}], "qualifiedClassName": "QAmbientSound", "signals": [{"access": "public", "name": "sourceChanged", "returnType": "void"}, {"access": "public", "name": "loopsChanged", "returnType": "void"}, {"access": "public", "name": "autoPlayChanged", "returnType": "void"}, {"access": "public", "name": "volumeChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "play", "returnType": "void"}, {"access": "public", "name": "pause", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qambientsound.h", "outputRevision": 68}, {"classes": [{"className": "QAudioEngine", "enums": [{"isClass": false, "isFlag": false, "name": "OutputMode", "values": ["Surround", "Stereo", "Headphone"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "outputMode", "notify": "outputModeChanged", "read": "outputMode", "required": false, "scriptable": true, "stored": true, "type": "OutputMode", "user": false, "write": "setOutputMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "outputDevice", "notify": "outputDeviceChanged", "read": "outputDevice", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setOutputDevice"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "masterVolume", "notify": "masterVolumeChanged", "read": "masterVolume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMasterVolume"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "paused", "notify": "paused<PERSON><PERSON>ed", "read": "paused", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPaused"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "distanceScale", "notify": "distanceScaleChanged", "read": "distanceScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDistanceScale"}], "qualifiedClassName": "QAudioEngine", "signals": [{"access": "public", "name": "outputModeChanged", "returnType": "void"}, {"access": "public", "name": "outputDeviceChanged", "returnType": "void"}, {"access": "public", "name": "masterVolumeChanged", "returnType": "void"}, {"access": "public", "name": "paused<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "name": "distanceScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "public", "name": "pause", "returnType": "void"}, {"access": "public", "name": "resume", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudioengine.h", "outputRevision": 68}, {"classes": [{"className": "QAudioRoom", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "dimensions", "notify": "dimensionsChanged", "read": "dimensions", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setDimensions"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "reflectionGain", "notify": "reflection<PERSON>ain<PERSON><PERSON>ed", "read": "reflectionGain", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReflectionGain"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "reverbGain", "notify": "reverbGain<PERSON><PERSON>ed", "read": "reverbGain", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReverbGain"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "reverbTime", "notify": "reverbTimeChanged", "read": "reverbTime", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReverbTime"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "reverbBrightness", "notify": "reverbBrightnessChanged", "read": "reverbBrightness", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReverbBrightness"}], "qualifiedClassName": "QAudioRoom", "signals": [{"access": "public", "name": "positionChanged", "returnType": "void"}, {"access": "public", "name": "dimensionsChanged", "returnType": "void"}, {"access": "public", "name": "rotationChanged", "returnType": "void"}, {"access": "public", "name": "wallsChanged", "returnType": "void"}, {"access": "public", "name": "reflection<PERSON>ain<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "name": "reverbGain<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "name": "reverbTimeChanged", "returnType": "void"}, {"access": "public", "name": "reverbBrightnessChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudioroom.h", "outputRevision": 68}, {"classes": [{"className": "QSpatialSound", "enums": [{"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite", "Once"]}, {"isClass": true, "isFlag": false, "name": "DistanceModel", "values": ["Logarithmic", "Linear", "ManualAttenuation"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "distanceModel", "notify": "distanceModelChanged", "read": "distanceModel", "required": false, "scriptable": true, "stored": true, "type": "DistanceModel", "user": false, "write": "setDistanceModel"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "size", "notify": "sizeChanged", "read": "size", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSize"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "distanceCutoff<PERSON>hanged", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "manualAttenuation", "notify": "manualAttenuationChanged", "read": "manualAttenuation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setManualAttenuation"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "occlusionIntensity", "notify": "occlusionIntensityChanged", "read": "occlusionIntensity", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setOcclusionIntensity"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "directivity", "notify": "directivityChanged", "read": "directivity", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDirectivity"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "directivityOrder", "notify": "directivityOrderChanged", "read": "directivityOrder", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDirectivityOrder"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "nearFieldGain", "notify": "nearFieldGainChanged", "read": "nearFieldGain", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setNearFieldGain"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "loops", "notify": "loopsChanged", "read": "loops", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoops"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "autoPlay", "notify": "autoPlayChanged", "read": "autoPlay", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoPlay"}], "qualifiedClassName": "QSpatialSound", "signals": [{"access": "public", "name": "sourceChanged", "returnType": "void"}, {"access": "public", "name": "loopsChanged", "returnType": "void"}, {"access": "public", "name": "autoPlayChanged", "returnType": "void"}, {"access": "public", "name": "positionChanged", "returnType": "void"}, {"access": "public", "name": "rotationChanged", "returnType": "void"}, {"access": "public", "name": "volumeChanged", "returnType": "void"}, {"access": "public", "name": "distanceModelChanged", "returnType": "void"}, {"access": "public", "name": "sizeChanged", "returnType": "void"}, {"access": "public", "name": "distanceCutoff<PERSON>hanged", "returnType": "void"}, {"access": "public", "name": "manualAttenuationChanged", "returnType": "void"}, {"access": "public", "name": "occlusionIntensityChanged", "returnType": "void"}, {"access": "public", "name": "directivityChanged", "returnType": "void"}, {"access": "public", "name": "directivityOrderChanged", "returnType": "void"}, {"access": "public", "name": "nearFieldGainChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "play", "returnType": "void"}, {"access": "public", "name": "pause", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qspatialsound.h", "outputRevision": 68}, {"classes": [{"className": "QAudioOutputStream", "methods": [{"access": "public", "name": "startOutput", "returnType": "void"}, {"access": "public", "name": "stopOutput", "returnType": "void"}, {"access": "public", "name": "restartOutput", "returnType": "void"}], "object": true, "qualifiedClassName": "QAudioOutputStream", "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qaudioengine.cpp", "outputRevision": 68}]