[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "Accelerometer"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAccelerometer", "enums": [{"isClass": false, "isFlag": false, "name": "AccelerationMode", "values": ["Combined", "Gravity", "User"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "accelerationMode", "notify": "accelerationModeChanged", "read": "accelerationMode", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "AccelerationMode", "user": false, "write": "setAccelerationMode"}], "qualifiedClassName": "QmlAccelerometer", "signals": [{"access": "public", "arguments": [{"name": "accelerationMode", "type": "AccelerationMode"}], "name": "accelerationModeChanged", "returnType": "void", "revision": 65281}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "AccelerometerReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create AccelerometerReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAccelerometerReading", "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlAccelerometerReading", "signals": [{"access": "public", "name": "xChanged", "returnType": "void"}, {"access": "public", "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "z<PERSON>hanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlaccelerometer_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AmbientLightSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAmbientLightSensor", "object": true, "qualifiedClassName": "QmlAmbientLightSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "AmbientLightReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create AmbientLightReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAmbientLightSensorReading", "object": true, "properties": [{"bindable": "bindableLightLevel", "constant": false, "designable": true, "final": false, "index": 0, "name": "lightLevel", "notify": "lightLevelChanged", "read": "lightLevel", "required": false, "scriptable": true, "stored": true, "type": "QAmbientLightReading::LightLevel", "user": false}], "qualifiedClassName": "QmlAmbientLightSensorReading", "signals": [{"access": "public", "name": "lightLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlambientlightsensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AmbientTemperatureSensor"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlAmbientTemperatureSensor", "object": true, "qualifiedClassName": "QmlAmbientTemperatureSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "AmbientTemperatureReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create AmbientTemperatureReading"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlAmbientTemperatureReading", "object": true, "properties": [{"bindable": "bindableTemperature", "constant": false, "designable": true, "final": false, "index": 0, "name": "temperature", "notify": "temperatureChanged", "read": "temperature", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlAmbientTemperatureReading", "signals": [{"access": "public", "name": "temperatureChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlambienttemperaturesensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "<PERSON>mp<PERSON>"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlCompass", "object": true, "qualifiedClassName": "QmlCompass", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "CompassReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create CompassReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlCompassReading", "object": true, "properties": [{"bindable": "bindableAzimuth", "constant": false, "designable": true, "final": false, "index": 0, "name": "azimuth", "notify": "azimuthChanged", "read": "azimuth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableCalibrationLevel", "constant": false, "designable": true, "final": false, "index": 1, "name": "calibrationLevel", "notify": "calibrationLevelChanged", "read": "calibrationLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlCompassReading", "signals": [{"access": "public", "name": "azimuthChanged", "returnType": "void"}, {"access": "public", "name": "calibrationLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlcompass_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Gyroscope"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlGyroscope", "object": true, "qualifiedClassName": "QmlGyroscope", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "GyroscopeReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create GyroscopeReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlGyroscopeReading", "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlGyroscopeReading", "signals": [{"access": "public", "name": "xChanged", "returnType": "void"}, {"access": "public", "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "z<PERSON>hanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlgyroscope_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "HumiditySensor"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlHumiditySensor", "object": true, "qualifiedClassName": "QmlHumiditySensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "HumidityReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create HumidityReading"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlHumidityReading", "object": true, "properties": [{"bindable": "bindableRelativeHumidity", "constant": false, "designable": true, "final": false, "index": 0, "name": "relativeHumidity", "notify": "relativeHumidityChanged", "read": "relativeHumidity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableAbsoluteHumidity", "constant": false, "designable": true, "final": false, "index": 1, "name": "absoluteHumidity", "notify": "absoluteHumidityChanged", "read": "absoluteHumidity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlHumidityReading", "signals": [{"access": "public", "name": "relativeHumidityChanged", "returnType": "void"}, {"access": "public", "name": "absoluteHumidityChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlhumiditysensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "IRProximitySensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlIRProximitySensor", "object": true, "qualifiedClassName": "QmlIRProximitySensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "IRProximityReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create IRProximityReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlIRProximitySensorReading", "object": true, "properties": [{"bindable": "bindableReflectance", "constant": false, "designable": true, "final": false, "index": 0, "name": "reflectance", "notify": "reflectanceChanged", "read": "reflectance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlIRProximitySensorReading", "signals": [{"access": "public", "name": "reflectanceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlirproximitysensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LidSensor"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlLidSensor", "object": true, "qualifiedClassName": "QmlLidSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "LidReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create LidReading"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlLidReading", "object": true, "properties": [{"bindable": "bindableBackLidClosed", "constant": false, "designable": true, "final": false, "index": 0, "name": "backLidClosed", "notify": "backLid<PERSON><PERSON>ed", "read": "backLidClosed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableFrontLidClosed", "constant": false, "designable": true, "final": false, "index": 1, "name": "frontLidClosed", "notify": "frontLidChanged", "read": "frontLidClosed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlLidReading", "signals": [{"access": "public", "arguments": [{"name": "closed", "type": "bool"}], "name": "backLid<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "closed", "type": "bool"}], "name": "frontLidChanged", "returnType": "bool"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmllidsensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LightSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlLightSensor", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fieldOfView", "notify": "fieldOfViewChanged", "read": "fieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlLightSensor", "signals": [{"access": "public", "arguments": [{"name": "fieldOfView", "type": "qreal"}], "name": "fieldOfViewChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "LightReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create LightReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlLightSensorReading", "object": true, "properties": [{"bindable": "bindableIlluminance", "constant": false, "designable": true, "final": false, "index": 0, "name": "illuminance", "notify": "illuminanceChanged", "read": "illuminance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlLightSensorReading", "signals": [{"access": "public", "name": "illuminanceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmllightsensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Magnetometer"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlMagnetometer", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "returnGeoValues", "notify": "returnGeoValuesChanged", "read": "returnGeoValues", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReturnGeoValues"}], "qualifiedClassName": "QmlMagnetometer", "signals": [{"access": "public", "arguments": [{"name": "returnGeoValues", "type": "bool"}], "name": "returnGeoValuesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "MagnetometerReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create MagnetometerReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlMagnetometerReading", "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableCalibrationLevel", "constant": false, "designable": true, "final": false, "index": 3, "name": "calibrationLevel", "notify": "calibrationLevelChanged", "read": "calibrationLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlMagnetometerReading", "signals": [{"access": "public", "name": "xChanged", "returnType": "void"}, {"access": "public", "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "z<PERSON>hanged", "returnType": "void"}, {"access": "public", "name": "calibrationLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlmagnetometer_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "OrientationSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlOrientationSensor", "object": true, "qualifiedClassName": "QmlOrientationSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "OrientationReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create OrientationReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlOrientationSensorReading", "object": true, "properties": [{"bindable": "bindableOrientation", "constant": false, "designable": true, "final": false, "index": 0, "name": "orientation", "notify": "orientationChanged", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "QOrientationReading::Orientation", "user": false}], "qualifiedClassName": "QmlOrientationSensorReading", "signals": [{"access": "public", "name": "orientationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlorientationsensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PressureSensor"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlPressureSensor", "object": true, "qualifiedClassName": "QmlPressureSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "PressureReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create PressureReading"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlPressureReading", "object": true, "properties": [{"bindable": "bindablePressure", "constant": false, "designable": true, "final": false, "index": 0, "name": "pressure", "notify": "pressureChanged", "read": "pressure", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableTemperature", "constant": false, "designable": true, "final": false, "index": 1, "name": "temperature", "notify": "temperatureChanged", "read": "temperature", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlPressureReading", "signals": [{"access": "public", "name": "pressureChanged", "returnType": "void"}, {"access": "public", "name": "temperatureChanged", "returnType": "void", "revision": 65281}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlpressuresensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ProximitySensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlProximitySensor", "object": true, "qualifiedClassName": "QmlProximitySensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "ProximityReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create ProximityReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlProximitySensorReading", "object": true, "properties": [{"bindable": "bindableNear", "constant": false, "designable": true, "final": false, "index": 0, "name": "near", "notify": "nearChanged", "read": "near", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlProximitySensorReading", "signals": [{"access": "public", "name": "nearChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlproximitysensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "RotationSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlRotationSensor", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "hasZ", "notify": "hasZChanged", "read": "hasZ", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlRotationSensor", "signals": [{"access": "public", "arguments": [{"name": "hasZ", "type": "bool"}], "name": "hasZChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "RotationReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create RotationReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlRotationSensorReading", "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlRotationSensorReading", "signals": [{"access": "public", "name": "xChanged", "returnType": "void"}, {"access": "public", "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "z<PERSON>hanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlrotationsensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Sensor"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create Sensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensor", "enums": [{"isClass": false, "isFlag": false, "name": "AxesOrientationMode", "values": ["FixedOrientation", "AutomaticOrientation", "UserOrientation"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "identifier", "notify": "identifierChanged", "read": "identifier", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false, "write": "setIdentifier"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "connectedToBackend", "notify": "connectedToBackendChanged", "read": "isConnectedToBackend", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "availableDataRates", "notify": "availableDataRatesChanged", "read": "availableDataRates", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QmlSensorRange>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "dataRate", "notify": "dataRateChanged", "read": "dataRate", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDataRate"}, {"bindable": "bindableReading", "constant": false, "designable": true, "final": false, "index": 5, "name": "reading", "notify": "readingChanged", "read": "reading", "required": false, "scriptable": true, "stored": true, "type": "QmlSensorReading*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "busy", "notify": "busyChanged", "read": "isBusy", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "outputRanges", "notify": "outputRangesChanged", "read": "outputRanges", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QmlSensorOutputRange>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "outputRange", "notify": "outputRangeChanged", "read": "outputRange", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setOutputRange"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "description", "notify": "descriptionChanged", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "alwaysOn", "notify": "alwaysOnChanged", "read": "isAlwaysOn", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAlwaysOn"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "skipDuplicates", "notify": "skipDuplicatesChanged", "read": "skipDuplicates", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSkipDuplicates"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "axesOrientationMode", "notify": "axesOrientationModeChanged", "read": "axesOrientationMode", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "AxesOrientationMode", "user": false, "write": "setAxesOrientationMode"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "currentOrientation", "notify": "currentOrientationChanged", "read": "currentOrientation", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "userOrientation", "notify": "userOrientationChanged", "read": "userOrientation", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUserOrientation"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "maxBufferSize", "notify": "maxBufferSizeChanged", "read": "maxBufferSize", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "efficientBufferSize", "notify": "efficientBufferSizeChanged", "read": "efficientBufferSize", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "bufferSize", "notify": "bufferSizeChanged", "read": "bufferSize", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBufferSize"}], "qualifiedClassName": "QmlSensor", "signals": [{"access": "public", "name": "identifierChanged", "returnType": "void"}, {"access": "public", "name": "connectedToBackendChanged", "returnType": "void"}, {"access": "public", "name": "availableDataRatesChanged", "returnType": "void"}, {"access": "public", "name": "dataRateChanged", "returnType": "void"}, {"access": "public", "name": "readingChanged", "returnType": "void"}, {"access": "public", "name": "activeChanged", "returnType": "void"}, {"access": "public", "name": "outputRangesChanged", "returnType": "void"}, {"access": "public", "name": "outputRangeChanged", "returnType": "void"}, {"access": "public", "name": "descriptionChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "name": "alwaysOnChanged", "returnType": "void"}, {"access": "public", "name": "busyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skipDuplicates", "type": "bool"}], "name": "skipDuplicatesChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "axesOrientationMode", "type": "AxesOrientationMode"}], "name": "axesOrientationModeChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "currentOrientation", "type": "int"}], "name": "currentOrientationChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "userOrientation", "type": "int"}], "name": "userOrientationChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "maxBufferSize", "type": "int"}], "name": "maxBufferSizeChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "efficientBufferSize", "type": "int"}], "name": "efficientBufferSizeChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "bufferSize", "type": "int"}], "name": "bufferSizeChanged", "returnType": "void", "revision": 65281}], "slots": [{"access": "public", "name": "start", "returnType": "bool"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "private", "name": "updateReading", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "SensorReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create SensorReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorReading", "object": true, "properties": [{"bindable": "bindableTimestamp", "constant": false, "designable": true, "final": false, "index": 0, "name": "timestamp", "notify": "timestampChanged", "read": "timestamp", "required": false, "scriptable": true, "stored": true, "type": "quint64", "user": false}], "qualifiedClassName": "QmlSensorReading", "signals": [{"access": "public", "name": "timestampChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmlsensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "QmlSensors"}, {"name": "QML.Singleton", "value": "true"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorGlobal", "methods": [{"access": "public", "name": "sensorTypes", "returnType": "QStringList"}, {"access": "public", "arguments": [{"name": "type", "type": "QString"}], "name": "sensorsForType", "returnType": "QStringList"}, {"access": "public", "arguments": [{"name": "type", "type": "QString"}], "name": "defaultSensorForType", "returnType": "QString"}], "object": true, "qualifiedClassName": "QmlSensorGlobal", "signals": [{"access": "public", "name": "availableSensorsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmlsensorglobal_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Range"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create Range"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorRange", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "minimum", "read": "minimum", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maximum", "read": "maximum", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QmlSensorRange", "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "OutputRange"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create OutputRange"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorOutputRange", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "minimum", "read": "minimum", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maximum", "read": "maximum", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "accuracy", "read": "accuracy", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlSensorOutputRange", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmlsensorrange_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TapSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTapSensor", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "returnDoubleTapEvents", "notify": "returnDoubleTapEventsChanged", "read": "returnDoubleTapEvents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReturnDoubleTapEvents"}], "qualifiedClassName": "QmlTapSensor", "signals": [{"access": "public", "arguments": [{"name": "returnDoubleTapEvents", "type": "bool"}], "name": "returnDoubleTapEventsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "TapReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create TapReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTapSensorReading", "object": true, "properties": [{"bindable": "bindableTapDirection", "constant": false, "designable": true, "final": false, "index": 0, "name": "tapDirection", "notify": "tapDirectionChanged", "read": "tapDirection", "required": false, "scriptable": true, "stored": true, "type": "QTapReading::TapDirection", "user": false}, {"bindable": "bindableDoubleTap", "constant": false, "designable": true, "final": false, "index": 1, "name": "doubleTap", "notify": "isDoubleTapChanged", "read": "isDoubleTap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlTapSensorReading", "signals": [{"access": "public", "name": "tapDirectionChanged", "returnType": "void"}, {"access": "public", "name": "isDoubleTapChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmltapsensor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TiltSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTiltSensor", "methods": [{"access": "public", "name": "calibrate", "returnType": "void"}], "object": true, "qualifiedClassName": "QmlTiltSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "TiltReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create TiltReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTiltSensorReading", "object": true, "properties": [{"bindable": "bindableYRotation", "constant": false, "designable": true, "final": false, "index": 0, "name": "yRotation", "notify": "yRotationChanged", "read": "yRotation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableXRotation", "constant": false, "designable": true, "final": false, "index": 1, "name": "xRotation", "notify": "xRotationChanged", "read": "xRotation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlTiltSensorReading", "signals": [{"access": "public", "name": "yRotationChanged", "returnType": "void"}, {"access": "public", "name": "xRotationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmltiltsensor_p.h", "outputRevision": 68}]