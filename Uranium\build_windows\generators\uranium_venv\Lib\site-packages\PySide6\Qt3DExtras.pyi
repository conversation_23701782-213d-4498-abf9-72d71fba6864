# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.Qt3DExtras, except for defaults which are replaced by "...".
"""

# Module `PySide6.Qt3DExtras`

import PySide6.Qt3DExtras
import PySide6.QtCore
import PySide6.QtGui
import PySide6.Qt3DCore
import PySide6.Qt3DRender

from typing import Any, ClassVar, List, Optional, Sequence, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QIntList(object): ...


class Qt3DExtras(Shiboken.Object):

    class QAbstractCameraController(PySide6.Qt3DCore.Qt3DCore.QEntity):

        accelerationChanged      : ClassVar[Signal] = ... # accelerationChanged(float)
        cameraChanged            : ClassVar[Signal] = ... # cameraChanged()
        decelerationChanged      : ClassVar[Signal] = ... # decelerationChanged(float)
        linearSpeedChanged       : ClassVar[Signal] = ... # linearSpeedChanged()
        lookSpeedChanged         : ClassVar[Signal] = ... # lookSpeedChanged()

        class InputState(Shiboken.Object):

            @overload
            def __init__(self) -> None: ...
            @overload
            def __init__(self, InputState: PySide6.Qt3DExtras.Qt3DExtras.QAbstractCameraController.InputState) -> None: ...

            @staticmethod
            def __copy__() -> None: ...


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def acceleration(self) -> float: ...
        def camera(self) -> PySide6.Qt3DRender.Qt3DRender.QCamera: ...
        def deceleration(self) -> float: ...
        def linearSpeed(self) -> float: ...
        def lookSpeed(self) -> float: ...
        def setAcceleration(self, acceleration: float) -> None: ...
        def setCamera(self, camera: PySide6.Qt3DRender.Qt3DRender.QCamera) -> None: ...
        def setDeceleration(self, deceleration: float) -> None: ...
        def setLinearSpeed(self, linearSpeed: float) -> None: ...
        def setLookSpeed(self, lookSpeed: float) -> None: ...

    class QAbstractSpriteSheet(PySide6.Qt3DCore.Qt3DCore.QNode):

        currentIndexChanged      : ClassVar[Signal] = ... # currentIndexChanged(int)
        textureChanged           : ClassVar[Signal] = ... # textureChanged(Qt3DRender::QAbstractTexture*)
        textureTransformChanged  : ClassVar[Signal] = ... # textureTransformChanged(QMatrix3x3)
        def currentIndex(self) -> int: ...
        def setCurrentIndex(self, currentIndex: int) -> None: ...
        def setTexture(self, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def texture(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureTransform(self) -> PySide6.QtGui.QMatrix3x3: ...

    class QConeGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        bottomRadiusChanged      : ClassVar[Signal] = ... # bottomRadiusChanged(float)
        hasBottomEndcapChanged   : ClassVar[Signal] = ... # hasBottomEndcapChanged(bool)
        hasTopEndcapChanged      : ClassVar[Signal] = ... # hasTopEndcapChanged(bool)
        lengthChanged            : ClassVar[Signal] = ... # lengthChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)
        topRadiusChanged         : ClassVar[Signal] = ... # topRadiusChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def bottomRadius(self) -> float: ...
        def hasBottomEndcap(self) -> bool: ...
        def hasTopEndcap(self) -> bool: ...
        def indexAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def length(self) -> float: ...
        def normalAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def rings(self) -> int: ...
        def setBottomRadius(self, bottomRadius: float) -> None: ...
        def setHasBottomEndcap(self, hasBottomEndcap: bool) -> None: ...
        def setHasTopEndcap(self, hasTopEndcap: bool) -> None: ...
        def setLength(self, length: float) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setTopRadius(self, topRadius: float) -> None: ...
        def slices(self) -> int: ...
        def texCoordAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def topRadius(self) -> float: ...
        def updateIndices(self) -> None: ...
        def updateVertices(self) -> None: ...

    class QConeGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        bottomRadiusChanged      : ClassVar[Signal] = ... # bottomRadiusChanged(float)
        hasBottomEndcapChanged   : ClassVar[Signal] = ... # hasBottomEndcapChanged(bool)
        hasTopEndcapChanged      : ClassVar[Signal] = ... # hasTopEndcapChanged(bool)
        lengthChanged            : ClassVar[Signal] = ... # lengthChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)
        topRadiusChanged         : ClassVar[Signal] = ... # topRadiusChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def bottomRadius(self) -> float: ...
        def hasBottomEndcap(self) -> bool: ...
        def hasTopEndcap(self) -> bool: ...
        def length(self) -> float: ...
        def rings(self) -> int: ...
        def setBottomRadius(self, bottomRadius: float) -> None: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setHasBottomEndcap(self, hasBottomEndcap: bool) -> None: ...
        def setHasTopEndcap(self, hasTopEndcap: bool) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setLength(self, length: float) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setTopRadius(self, topRadius: float) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...
        def topRadius(self) -> float: ...

    class QConeMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        bottomRadiusChanged      : ClassVar[Signal] = ... # bottomRadiusChanged(float)
        hasBottomEndcapChanged   : ClassVar[Signal] = ... # hasBottomEndcapChanged(bool)
        hasTopEndcapChanged      : ClassVar[Signal] = ... # hasTopEndcapChanged(bool)
        lengthChanged            : ClassVar[Signal] = ... # lengthChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)
        topRadiusChanged         : ClassVar[Signal] = ... # topRadiusChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def bottomRadius(self) -> float: ...
        def hasBottomEndcap(self) -> bool: ...
        def hasTopEndcap(self) -> bool: ...
        def length(self) -> float: ...
        def rings(self) -> int: ...
        def setBottomRadius(self, bottomRadius: float) -> None: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setHasBottomEndcap(self, hasBottomEndcap: bool) -> None: ...
        def setHasTopEndcap(self, hasTopEndcap: bool) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setLength(self, length: float) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setTopRadius(self, topRadius: float) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...
        def topRadius(self) -> float: ...

    class QCuboidGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        xExtentChanged           : ClassVar[Signal] = ... # xExtentChanged(float)
        xyMeshResolutionChanged  : ClassVar[Signal] = ... # xyMeshResolutionChanged(QSize)
        xzMeshResolutionChanged  : ClassVar[Signal] = ... # xzMeshResolutionChanged(QSize)
        yExtentChanged           : ClassVar[Signal] = ... # yExtentChanged(float)
        yzMeshResolutionChanged  : ClassVar[Signal] = ... # yzMeshResolutionChanged(QSize)
        zExtentChanged           : ClassVar[Signal] = ... # zExtentChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def indexAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def normalAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def setXExtent(self, xExtent: float) -> None: ...
        def setXYMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setXZMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setYExtent(self, yExtent: float) -> None: ...
        def setYZMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setZExtent(self, zExtent: float) -> None: ...
        def tangentAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def texCoordAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self) -> None: ...
        def updateVertices(self) -> None: ...
        def xExtent(self) -> float: ...
        def xyMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def xzMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def yExtent(self) -> float: ...
        def yzMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def zExtent(self) -> float: ...

    class QCuboidGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        xExtentChanged           : ClassVar[Signal] = ... # xExtentChanged(float)
        xyMeshResolutionChanged  : ClassVar[Signal] = ... # xyMeshResolutionChanged(QSize)
        xzMeshResolutionChanged  : ClassVar[Signal] = ... # xzMeshResolutionChanged(QSize)
        yExtentChanged           : ClassVar[Signal] = ... # yExtentChanged(float)
        yzMeshResolutionChanged  : ClassVar[Signal] = ... # yzMeshResolutionChanged(QSize)
        zExtentChanged           : ClassVar[Signal] = ... # zExtentChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def setXExtent(self, xExtent: float) -> None: ...
        def setXYMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setXZMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setYExtent(self, yExtent: float) -> None: ...
        def setYZMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setZExtent(self, zExtent: float) -> None: ...
        def xExtent(self) -> float: ...
        def xyMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def xzMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def yExtent(self) -> float: ...
        def yzMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def zExtent(self) -> float: ...

    class QCuboidMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        xExtentChanged           : ClassVar[Signal] = ... # xExtentChanged(float)
        xyMeshResolutionChanged  : ClassVar[Signal] = ... # xyMeshResolutionChanged(QSize)
        xzMeshResolutionChanged  : ClassVar[Signal] = ... # xzMeshResolutionChanged(QSize)
        yExtentChanged           : ClassVar[Signal] = ... # yExtentChanged(float)
        yzMeshResolutionChanged  : ClassVar[Signal] = ... # yzMeshResolutionChanged(QSize)
        zExtentChanged           : ClassVar[Signal] = ... # zExtentChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def setXExtent(self, xExtent: float) -> None: ...
        def setXYMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setXZMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setYExtent(self, yExtent: float) -> None: ...
        def setYZMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setZExtent(self, zExtent: float) -> None: ...
        def xExtent(self) -> float: ...
        def xyMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def xzMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def yExtent(self) -> float: ...
        def yzMeshResolution(self) -> PySide6.QtCore.QSize: ...
        def zExtent(self) -> float: ...

    class QCylinderGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        lengthChanged            : ClassVar[Signal] = ... # lengthChanged(float)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def indexAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def length(self) -> float: ...
        def normalAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setLength(self, length: float) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def slices(self) -> int: ...
        def texCoordAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self) -> None: ...
        def updateVertices(self) -> None: ...

    class QCylinderGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        lengthChanged            : ClassVar[Signal] = ... # lengthChanged(float)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def length(self) -> float: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setLength(self, length: float) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...

    class QCylinderMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        lengthChanged            : ClassVar[Signal] = ... # lengthChanged(float)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def length(self) -> float: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setLength(self, length: float) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...

    class QDiffuseMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(QColor)
        textureScaleChanged      : ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambient(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setTextureScale(self, textureScale: float) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> PySide6.QtGui.QColor: ...
        def textureScale(self) -> float: ...

    class QDiffuseSpecularMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(Qt3DRender::QAbstractTexture*)
        textureScaleChanged      : ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambient(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, ambient: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setTextureScale(self, textureScale: float) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureScale(self) -> float: ...

    class QDiffuseSpecularMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaBlendingEnabledChanged: ClassVar[Signal] = ... # alphaBlendingEnabledChanged(bool)
        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(QVariant)
        normalChanged            : ClassVar[Signal] = ... # normalChanged(QVariant)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(QVariant)
        textureScaleChanged      : ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambient(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> Any: ...
        def isAlphaBlendingEnabled(self) -> bool: ...
        def normal(self) -> Any: ...
        def setAlphaBlendingEnabled(self, enabled: bool) -> None: ...
        def setAmbient(self, ambient: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: Any) -> None: ...
        def setNormal(self, normal: Any) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: Any) -> None: ...
        def setTextureScale(self, textureScale: float) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> Any: ...
        def textureScale(self) -> float: ...

    class QExtrudedTextGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        depthChanged             : ClassVar[Signal] = ... # depthChanged(float)
        fontChanged              : ClassVar[Signal] = ... # fontChanged(QFont)
        textChanged              : ClassVar[Signal] = ... # textChanged(QString)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def extrusionLength(self) -> float: ...
        def font(self) -> PySide6.QtGui.QFont: ...
        def indexAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def normalAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def setDepth(self, extrusionLength: float) -> None: ...
        def setFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
        def setText(self, text: str) -> None: ...
        def text(self) -> str: ...

    class QExtrudedTextMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        depthChanged             : ClassVar[Signal] = ... # depthChanged(float)
        fontChanged              : ClassVar[Signal] = ... # fontChanged(QFont)
        textChanged              : ClassVar[Signal] = ... # textChanged(QString)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def depth(self) -> float: ...
        def font(self) -> PySide6.QtGui.QFont: ...
        def setDepth(self, depth: float) -> None: ...
        def setFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
        def setText(self, text: str) -> None: ...
        def text(self) -> str: ...

    class QFirstPersonCameraController(PySide6.Qt3DExtras.Qt3DExtras.QAbstractCameraController):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QForwardRenderer(PySide6.Qt3DRender.Qt3DRender.QTechniqueFilter):

        buffersToClearChanged    : ClassVar[Signal] = ... # buffersToClearChanged(Qt3DRender::QClearBuffers::BufferType)
        cameraChanged            : ClassVar[Signal] = ... # cameraChanged(Qt3DCore::QEntity*)
        clearColorChanged        : ClassVar[Signal] = ... # clearColorChanged(QColor)
        externalRenderTargetSizeChanged: ClassVar[Signal] = ... # externalRenderTargetSizeChanged(QSize)
        frustumCullingEnabledChanged: ClassVar[Signal] = ... # frustumCullingEnabledChanged(bool)
        gammaChanged             : ClassVar[Signal] = ... # gammaChanged(float)
        showDebugOverlayChanged  : ClassVar[Signal] = ... # showDebugOverlayChanged(bool)
        surfaceChanged           : ClassVar[Signal] = ... # surfaceChanged(QObject*)
        viewportRectChanged      : ClassVar[Signal] = ... # viewportRectChanged(QRectF)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def buffersToClear(self) -> PySide6.Qt3DRender.Qt3DRender.QClearBuffers.BufferType: ...
        def camera(self) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def clearColor(self) -> PySide6.QtGui.QColor: ...
        def externalRenderTargetSize(self) -> PySide6.QtCore.QSize: ...
        def gamma(self) -> float: ...
        def isFrustumCullingEnabled(self) -> bool: ...
        def setBuffersToClear(self, arg__1: PySide6.Qt3DRender.Qt3DRender.QClearBuffers.BufferType) -> None: ...
        def setCamera(self, camera: PySide6.Qt3DCore.Qt3DCore.QEntity) -> None: ...
        def setClearColor(self, clearColor: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setExternalRenderTargetSize(self, size: PySide6.QtCore.QSize) -> None: ...
        def setFrustumCullingEnabled(self, enabled: bool) -> None: ...
        def setGamma(self, gamma: float) -> None: ...
        def setShowDebugOverlay(self, showDebugOverlay: bool) -> None: ...
        def setSurface(self, surface: PySide6.QtCore.QObject) -> None: ...
        def setViewportRect(self, viewportRect: Union[PySide6.QtCore.QRectF, PySide6.QtCore.QRect]) -> None: ...
        def showDebugOverlay(self) -> bool: ...
        def surface(self) -> PySide6.QtCore.QObject: ...
        def viewportRect(self) -> PySide6.QtCore.QRectF: ...

    class QGoochMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaChanged             : ClassVar[Signal] = ... # alphaChanged(float)
        betaChanged              : ClassVar[Signal] = ... # betaChanged(float)
        coolChanged              : ClassVar[Signal] = ... # coolChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(QColor)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(QColor)
        warmChanged              : ClassVar[Signal] = ... # warmChanged(QColor)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def alpha(self) -> float: ...
        def beta(self) -> float: ...
        def cool(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> PySide6.QtGui.QColor: ...
        def setAlpha(self, alpha: float) -> None: ...
        def setBeta(self, beta: float) -> None: ...
        def setCool(self, cool: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setWarm(self, warm: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> PySide6.QtGui.QColor: ...
        def warm(self) -> PySide6.QtGui.QColor: ...

    class QMetalRoughMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientOcclusionChanged  : ClassVar[Signal] = ... # ambientOcclusionChanged(QVariant)
        baseColorChanged         : ClassVar[Signal] = ... # baseColorChanged(QVariant)
        metalnessChanged         : ClassVar[Signal] = ... # metalnessChanged(QVariant)
        normalChanged            : ClassVar[Signal] = ... # normalChanged(QVariant)
        roughnessChanged         : ClassVar[Signal] = ... # roughnessChanged(QVariant)
        textureScaleChanged      : ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambientOcclusion(self) -> Any: ...
        def baseColor(self) -> Any: ...
        def metalness(self) -> Any: ...
        def normal(self) -> Any: ...
        def roughness(self) -> Any: ...
        def setAmbientOcclusion(self, ambientOcclusion: Any) -> None: ...
        def setBaseColor(self, baseColor: Any) -> None: ...
        def setMetalness(self, metalness: Any) -> None: ...
        def setNormal(self, normal: Any) -> None: ...
        def setRoughness(self, roughness: Any) -> None: ...
        def setTextureScale(self, textureScale: float) -> None: ...
        def textureScale(self) -> float: ...

    class QMorphPhongMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(QColor)
        interpolatorChanged      : ClassVar[Signal] = ... # interpolatorChanged(float)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(QColor)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambient(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> PySide6.QtGui.QColor: ...
        def interpolator(self) -> float: ...
        def setAmbient(self, ambient: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setInterpolator(self, interpolator: float) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> PySide6.QtGui.QColor: ...

    class QNormalDiffuseMapAlphaMaterial(PySide6.Qt3DExtras.Qt3DExtras.QNormalDiffuseMapMaterial):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QNormalDiffuseMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        normalChanged            : ClassVar[Signal] = ... # normalChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(QColor)
        textureScaleChanged      : ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambient(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def normal(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, ambient: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setNormal(self, normal: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setTextureScale(self, textureScale: float) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> PySide6.QtGui.QColor: ...
        def textureScale(self) -> float: ...

    class QNormalDiffuseSpecularMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        normalChanged            : ClassVar[Signal] = ... # normalChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(Qt3DRender::QAbstractTexture*)
        textureScaleChanged      : ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambient(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def normal(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, ambient: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setNormal(self, normal: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setTextureScale(self, textureScale: float) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureScale(self) -> float: ...

    class QOrbitCameraController(PySide6.Qt3DExtras.Qt3DExtras.QAbstractCameraController):

        zoomInLimitChanged       : ClassVar[Signal] = ... # zoomInLimitChanged()

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setZoomInLimit(self, zoomInLimit: float) -> None: ...
        def zoomInLimit(self) -> float: ...

    class QPerVertexColorMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QPhongAlphaMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaChanged             : ClassVar[Signal] = ... # alphaChanged(float)
        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        blendFunctionArgChanged  : ClassVar[Signal] = ... # blendFunctionArgChanged(Qt3DRender::QBlendEquation::BlendFunction)
        destinationAlphaArgChanged: ClassVar[Signal] = ... # destinationAlphaArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        destinationRgbArgChanged : ClassVar[Signal] = ... # destinationRgbArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(QColor)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        sourceAlphaArgChanged    : ClassVar[Signal] = ... # sourceAlphaArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        sourceRgbArgChanged      : ClassVar[Signal] = ... # sourceRgbArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(QColor)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def alpha(self) -> float: ...
        def ambient(self) -> PySide6.QtGui.QColor: ...
        def blendFunctionArg(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquation.BlendFunction: ...
        def destinationAlphaArg(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def destinationRgbArg(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def diffuse(self) -> PySide6.QtGui.QColor: ...
        def setAlpha(self, alpha: float) -> None: ...
        def setAmbient(self, ambient: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setBlendFunctionArg(self, blendFunctionArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquation.BlendFunction) -> None: ...
        def setDestinationAlphaArg(self, destinationAlphaArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setDestinationRgbArg(self, destinationRgbArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setDiffuse(self, diffuse: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSourceAlphaArg(self, sourceAlphaArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setSourceRgbArg(self, sourceRgbArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setSpecular(self, specular: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def shininess(self) -> float: ...
        def sourceAlphaArg(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def sourceRgbArg(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def specular(self) -> PySide6.QtGui.QColor: ...

    class QPhongMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : ClassVar[Signal] = ... # diffuseChanged(QColor)
        shininessChanged         : ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(QColor)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def ambient(self) -> PySide6.QtGui.QColor: ...
        def diffuse(self) -> PySide6.QtGui.QColor: ...
        def setAmbient(self, ambient: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setDiffuse(self, diffuse: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setShininess(self, shininess: float) -> None: ...
        def setSpecular(self, specular: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def shininess(self) -> float: ...
        def specular(self) -> PySide6.QtGui.QColor: ...

    class QPlaneGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        heightChanged            : ClassVar[Signal] = ... # heightChanged(float)
        mirroredChanged          : ClassVar[Signal] = ... # mirroredChanged(bool)
        resolutionChanged        : ClassVar[Signal] = ... # resolutionChanged(QSize)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def height(self) -> float: ...
        def indexAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def mirrored(self) -> bool: ...
        def normalAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def resolution(self) -> PySide6.QtCore.QSize: ...
        def setHeight(self, height: float) -> None: ...
        def setMirrored(self, mirrored: bool) -> None: ...
        def setResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setWidth(self, width: float) -> None: ...
        def tangentAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def texCoordAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self) -> None: ...
        def updateVertices(self) -> None: ...
        def width(self) -> float: ...

    class QPlaneGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        heightChanged            : ClassVar[Signal] = ... # heightChanged(float)
        meshResolutionChanged    : ClassVar[Signal] = ... # meshResolutionChanged(QSize)
        mirroredChanged          : ClassVar[Signal] = ... # mirroredChanged(bool)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def height(self) -> float: ...
        def meshResolution(self) -> PySide6.QtCore.QSize: ...
        def mirrored(self) -> bool: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setHeight(self, height: float) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setMirrored(self, mirrored: bool) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def setWidth(self, width: float) -> None: ...
        def width(self) -> float: ...

    class QPlaneMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        heightChanged            : ClassVar[Signal] = ... # heightChanged(float)
        meshResolutionChanged    : ClassVar[Signal] = ... # meshResolutionChanged(QSize)
        mirroredChanged          : ClassVar[Signal] = ... # mirroredChanged(bool)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def height(self) -> float: ...
        def meshResolution(self) -> PySide6.QtCore.QSize: ...
        def mirrored(self) -> bool: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setHeight(self, height: float) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setMeshResolution(self, resolution: PySide6.QtCore.QSize) -> None: ...
        def setMirrored(self, mirrored: bool) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def setWidth(self, width: float) -> None: ...
        def width(self) -> float: ...

    class QSkyboxEntity(PySide6.Qt3DCore.Qt3DCore.QEntity):

        baseNameChanged          : ClassVar[Signal] = ... # baseNameChanged(QString)
        extensionChanged         : ClassVar[Signal] = ... # extensionChanged(QString)
        gammaCorrectEnabledChanged: ClassVar[Signal] = ... # gammaCorrectEnabledChanged(bool)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def baseName(self) -> str: ...
        def extension(self) -> str: ...
        def isGammaCorrectEnabled(self) -> bool: ...
        def setBaseName(self, path: str) -> None: ...
        def setExtension(self, extension: str) -> None: ...
        def setGammaCorrectEnabled(self, enabled: bool) -> None: ...

    class QSphereGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        generateTangentsChanged  : ClassVar[Signal] = ... # generateTangentsChanged(bool)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def generateTangents(self) -> bool: ...
        def indexAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def normalAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setGenerateTangents(self, gen: bool) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def slices(self) -> int: ...
        def tangentAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def texCoordAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self) -> None: ...
        def updateVertices(self) -> None: ...

    class QSphereGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        generateTangentsChanged  : ClassVar[Signal] = ... # generateTangentsChanged(bool)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def generateTangents(self) -> bool: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGenerateTangents(self, gen: bool) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...

    class QSphereMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        generateTangentsChanged  : ClassVar[Signal] = ... # generateTangentsChanged(bool)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def generateTangents(self) -> bool: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGenerateTangents(self, gen: bool) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...

    class QSpriteGrid(PySide6.Qt3DExtras.Qt3DExtras.QAbstractSpriteSheet):

        columnsChanged           : ClassVar[Signal] = ... # columnsChanged(int)
        rowsChanged              : ClassVar[Signal] = ... # rowsChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def columns(self) -> int: ...
        def rows(self) -> int: ...
        def setColumns(self, columns: int) -> None: ...
        def setRows(self, rows: int) -> None: ...

    class QSpriteSheet(PySide6.Qt3DExtras.Qt3DExtras.QAbstractSpriteSheet):

        spritesChanged           : ClassVar[Signal] = ... # spritesChanged(QList<QSpriteSheetItem*>)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        @overload
        def addSprite(self, sprite: PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem) -> None: ...
        @overload
        def addSprite(self, x: int, y: int, width: int, height: int) -> PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem: ...
        def removeSprite(self, sprite: PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem) -> None: ...
        def setSprites(self, sprites: Sequence[PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem]) -> None: ...
        def sprites(self) -> List[PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem]: ...

    class QSpriteSheetItem(PySide6.Qt3DCore.Qt3DCore.QNode):

        heightChanged            : ClassVar[Signal] = ... # heightChanged(int)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(int)
        xChanged                 : ClassVar[Signal] = ... # xChanged(int)
        yChanged                 : ClassVar[Signal] = ... # yChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def height(self) -> int: ...
        def setHeight(self, height: int) -> None: ...
        def setWidth(self, width: int) -> None: ...
        def setX(self, x: int) -> None: ...
        def setY(self, y: int) -> None: ...
        def width(self) -> int: ...
        def x(self) -> int: ...
        def y(self) -> int: ...

    class QText2DEntity(PySide6.Qt3DCore.Qt3DCore.QEntity):

        colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
        fontChanged              : ClassVar[Signal] = ... # fontChanged(QFont)
        heightChanged            : ClassVar[Signal] = ... # heightChanged(float)
        textChanged              : ClassVar[Signal] = ... # textChanged(QString)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def color(self) -> PySide6.QtGui.QColor: ...
        def font(self) -> PySide6.QtGui.QFont: ...
        def height(self) -> float: ...
        def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setFont(self, font: Union[PySide6.QtGui.QFont, str, Sequence[str]]) -> None: ...
        def setHeight(self, height: float) -> None: ...
        def setText(self, text: str) -> None: ...
        def setWidth(self, width: float) -> None: ...
        def text(self) -> str: ...
        def width(self) -> float: ...

    class QTextureMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaBlendingEnabledChanged: ClassVar[Signal] = ... # alphaBlendingEnabledChanged(bool)
        textureChanged           : ClassVar[Signal] = ... # textureChanged(Qt3DRender::QAbstractTexture*)
        textureOffsetChanged     : ClassVar[Signal] = ... # textureOffsetChanged(QVector2D)
        textureTransformChanged  : ClassVar[Signal] = ... # textureTransformChanged(QMatrix3x3)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def isAlphaBlendingEnabled(self) -> bool: ...
        def setAlphaBlendingEnabled(self, enabled: bool) -> None: ...
        def setTexture(self, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setTextureOffset(self, textureOffset: PySide6.QtGui.QVector2D) -> None: ...
        def setTextureTransform(self, matrix: PySide6.QtGui.QMatrix3x3) -> None: ...
        def texture(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureOffset(self) -> PySide6.QtGui.QVector2D: ...
        def textureTransform(self) -> PySide6.QtGui.QMatrix3x3: ...

    class QTorusGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        minorRadiusChanged       : ClassVar[Signal] = ... # minorRadiusChanged(float)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def indexAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def minorRadius(self) -> float: ...
        def normalAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setMinorRadius(self, minorRadius: float) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def slices(self) -> int: ...
        def texCoordAttribute(self) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self) -> None: ...
        def updateVertices(self) -> None: ...

    class QTorusGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        minorRadiusChanged       : ClassVar[Signal] = ... # minorRadiusChanged(float)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def minorRadius(self) -> float: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setMinorRadius(self, minorRadius: float) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...

    class QTorusMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        minorRadiusChanged       : ClassVar[Signal] = ... # minorRadiusChanged(float)
        radiusChanged            : ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def minorRadius(self) -> float: ...
        def radius(self) -> float: ...
        def rings(self) -> int: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setMinorRadius(self, minorRadius: float) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType) -> None: ...
        def setRadius(self, radius: float) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setRings(self, rings: int) -> None: ...
        def setSlices(self, slices: int) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def slices(self) -> int: ...

    class Qt3DWindow(PySide6.QtGui.QWindow):

        def __init__(self, screen: Optional[PySide6.QtGui.QScreen] = ..., arg__2: PySide6.Qt3DRender.Qt3DRender.API = ...) -> None: ...

        def activeFrameGraph(self) -> PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode: ...
        def camera(self) -> PySide6.Qt3DRender.Qt3DRender.QCamera: ...
        def defaultFrameGraph(self) -> PySide6.Qt3DExtras.Qt3DExtras.QForwardRenderer: ...
        def event(self, e: PySide6.QtCore.QEvent) -> bool: ...
        @overload
        def registerAspect(self, aspect: PySide6.Qt3DCore.Qt3DCore.QAbstractAspect) -> None: ...
        @overload
        def registerAspect(self, name: str) -> None: ...
        def renderSettings(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderSettings: ...
        def resizeEvent(self, arg__1: PySide6.QtGui.QResizeEvent) -> None: ...
        def setActiveFrameGraph(self, activeFrameGraph: PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode) -> None: ...
        def setRootEntity(self, root: PySide6.Qt3DCore.Qt3DCore.QEntity) -> None: ...
        def showEvent(self, e: PySide6.QtGui.QShowEvent) -> None: ...


    @staticmethod
    def setupWindowSurface(window: PySide6.QtGui.QWindow, arg__2: PySide6.Qt3DRender.Qt3DRender.API) -> None: ...


# eof
