[settings]
arch=x86_64
build_type=Release
compiler=msvc
compiler.cppstd=17
compiler.runtime=dynamic
compiler.runtime_type=Release
compiler.version=193
os=Windows
curaengine*:compiler.cppstd=20
curaengine_plugin_infill_generate*:compiler.cppstd=20
curaengine_plugin_gradual_flow*:compiler.cppstd=20
curaengine_grpc_definitions*:compiler.cppstd=20
scripta*:compiler.cppstd=20
umspatial*:compiler.cppstd=20
dulcificum*:compiler.cppstd=20
curator/*:compiler.cppstd=20
[options]
asio-grpc/*:local_allocator=recycling_allocator
boost/*:header_only=True
clipper/*:shared=True
cpython/*:shared=True
cpython/*:with_curses=False
cpython/*:with_tkinter=False
dulcificum/*:shared=False
grpc/*:csharp_plugin=False
grpc/*:node_plugin=False
grpc/*:objective_c_plugin=False
grpc/*:php_plugin=False
grpc/*:python_plugin=False
grpc/*:ruby_plugin=False
pyarcus/*:shared=True
pynest2d/*:shared=True
pysavitar/*:shared=True
[conf]
tools.build:skip_test=True
tools.cmake.cmaketoolchain:generator=Ninja
tools.gnu:define_libcxx11_abi=True
