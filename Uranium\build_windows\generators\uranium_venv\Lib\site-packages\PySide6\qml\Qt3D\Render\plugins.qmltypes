import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json Qt3D.Render 2.15'

Module {
    dependencies: ["Qt3D.Core 2.0"]
    Component {
        name: "QWindow"
        prototype: "QObject"
        exports: ["Qt3D.Render/Window 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Visibility"
            values: {
                "Hidden": 0,
                "AutomaticVisibility": 1,
                "Windowed": 2,
                "Minimized": 3,
                "Maximized": 4,
                "FullScreen": 5
            }
        }
        Enum {
            name: "AncestorMode"
            values: {
                "ExcludeTransients": 0,
                "IncludeTransients": 1
            }
        }
        Property { name: "title"; type: "string" }
        Property { name: "modality"; type: "Qt::WindowModality" }
        Property { name: "flags"; type: "Qt::WindowFlags" }
        Property { name: "x"; type: "int" }
        Property { name: "y"; type: "int" }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Property { name: "minimumWidth"; type: "int" }
        Property { name: "minimumHeight"; type: "int" }
        Property { name: "maximumWidth"; type: "int" }
        Property { name: "maximumHeight"; type: "int" }
        Property { name: "visible"; type: "bool" }
        Property { name: "active"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "visibility"; revision: 1; type: "Visibility" }
        Property { name: "contentOrientation"; type: "Qt::ScreenOrientation" }
        Property { name: "opacity"; revision: 1; type: "double" }
        Property { name: "transientParent"; revision: 13; type: "QWindow"; isPointer: true }
        Signal {
            name: "screenChanged"
            Parameter { name: "screen"; type: "QScreen"; isPointer: true }
        }
        Signal {
            name: "modalityChanged"
            Parameter { name: "modality"; type: "Qt::WindowModality" }
        }
        Signal {
            name: "windowStateChanged"
            Parameter { name: "windowState"; type: "Qt::WindowState" }
        }
        Signal {
            name: "windowTitleChanged"
            revision: 2
            Parameter { name: "title"; type: "string" }
        }
        Signal {
            name: "xChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "visibilityChanged"
            revision: 1
            Parameter { name: "visibility"; type: "QWindow::Visibility" }
        }
        Signal { name: "activeChanged"; revision: 1 }
        Signal {
            name: "contentOrientationChanged"
            Parameter { name: "orientation"; type: "Qt::ScreenOrientation" }
        }
        Signal {
            name: "focusObjectChanged"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "opacityChanged"
            revision: 1
            Parameter { name: "opacity"; type: "double" }
        }
        Signal {
            name: "transientParentChanged"
            revision: 13
            Parameter { name: "transientParent"; type: "QWindow"; isPointer: true }
        }
        Method { name: "requestActivate"; revision: 1 }
        Method {
            name: "setVisible"
            Parameter { name: "visible"; type: "bool" }
        }
        Method { name: "show" }
        Method { name: "hide" }
        Method { name: "showMinimized" }
        Method { name: "showMaximized" }
        Method { name: "showFullScreen" }
        Method { name: "showNormal" }
        Method { name: "close"; type: "bool" }
        Method { name: "raise" }
        Method { name: "lower" }
        Method {
            name: "startSystemResize"
            type: "bool"
            Parameter { name: "edges"; type: "Qt::Edges" }
        }
        Method { name: "startSystemMove"; type: "bool" }
        Method {
            name: "setTitle"
            Parameter { type: "string" }
        }
        Method {
            name: "setX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "posx"; type: "int" }
            Parameter { name: "posy"; type: "int" }
            Parameter { name: "w"; type: "int" }
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "rect"; type: "QRect" }
        }
        Method {
            name: "setMinimumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMinimumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setMaximumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMaximumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "alert"
            revision: 1
            Parameter { name: "msec"; type: "int" }
        }
        Method { name: "requestUpdate"; revision: 3 }
    }
    Component {
        name: "Qt3DRender::QAbstractLight"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/Light 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Type"
            values: {
                "PointLight": 0,
                "DirectionalLight": 1,
                "SpotLight": 2
            }
        }
        Property { name: "type"; type: "Type"; isReadonly: true }
        Property { name: "color"; type: "QColor" }
        Property { name: "intensity"; type: "float" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "intensityChanged"
            Parameter { name: "intensity"; type: "float" }
        }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setIntensity"
            Parameter { name: "intensity"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QAbstractRayCaster"
        prototype: "Qt3DCore::QComponent"
        Enum {
            name: "RunMode"
            values: {
                "Continuous": 0,
                "SingleShot": 1
            }
        }
        Enum {
            name: "FilterMode"
            values: {
                "AcceptAnyMatchingLayers": 0,
                "AcceptAllMatchingLayers": 1,
                "DiscardAnyMatchingLayers": 2,
                "DiscardAllMatchingLayers": 3
            }
        }
        Property { name: "runMode"; type: "RunMode" }
        Property { name: "filterMode"; type: "FilterMode" }
        Property { name: "hits"; type: "Hits"; isReadonly: true }
        Signal {
            name: "runModeChanged"
            Parameter { name: "runMode"; type: "Qt3DRender::QAbstractRayCaster::RunMode" }
        }
        Signal {
            name: "hitsChanged"
            Parameter { name: "hits"; type: "Qt3DRender::QAbstractRayCaster::Hits" }
        }
        Signal {
            name: "filterModeChanged"
            Parameter { name: "filterMode"; type: "Qt3DRender::QAbstractRayCaster::FilterMode" }
        }
        Method {
            name: "setRunMode"
            Parameter { name: "runMode"; type: "RunMode" }
        }
        Method {
            name: "setFilterMode"
            Parameter { name: "filterMode"; type: "FilterMode" }
        }
    }
    Component {
        name: "Qt3DRender::QAbstractTexture"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/Texture 2.0", "Qt3D.Render/Texture 2.13"]
        isCreatable: false
        exportMetaObjectRevisions: [0, 13]
        Enum {
            name: "Status"
            values: {
                "None": 0,
                "Loading": 1,
                "Ready": 2,
                "Error": 3
            }
        }
        Enum {
            name: "Target"
            values: {
                "TargetAutomatic": 0,
                "Target1D": 3552,
                "Target1DArray": 35864,
                "Target2D": 3553,
                "Target2DArray": 35866,
                "Target3D": 32879,
                "TargetCubeMap": 34067,
                "TargetCubeMapArray": 36873,
                "Target2DMultisample": 37120,
                "Target2DMultisampleArray": 37122,
                "TargetRectangle": 34037,
                "TargetBuffer": 35882
            }
        }
        Enum {
            name: "TextureFormat"
            values: {
                "NoFormat": 0,
                "Automatic": 1,
                "R8_UNorm": 33321,
                "RG8_UNorm": 33323,
                "RGB8_UNorm": 32849,
                "RGBA8_UNorm": 32856,
                "R16_UNorm": 33322,
                "RG16_UNorm": 33324,
                "RGB16_UNorm": 32852,
                "RGBA16_UNorm": 32859,
                "R8_SNorm": 36756,
                "RG8_SNorm": 36757,
                "RGB8_SNorm": 36758,
                "RGBA8_SNorm": 36759,
                "R16_SNorm": 36760,
                "RG16_SNorm": 36761,
                "RGB16_SNorm": 36762,
                "RGBA16_SNorm": 36763,
                "R8U": 33330,
                "RG8U": 33336,
                "RGB8U": 36221,
                "RGBA8U": 36220,
                "R16U": 33332,
                "RG16U": 33338,
                "RGB16U": 36215,
                "RGBA16U": 36214,
                "R32U": 33334,
                "RG32U": 33340,
                "RGB32U": 36209,
                "RGBA32U": 36208,
                "R8I": 33329,
                "RG8I": 33335,
                "RGB8I": 36239,
                "RGBA8I": 36238,
                "R16I": 33331,
                "RG16I": 33337,
                "RGB16I": 36233,
                "RGBA16I": 36232,
                "R32I": 33333,
                "RG32I": 33339,
                "RGB32I": 36227,
                "RGBA32I": 36226,
                "R16F": 33325,
                "RG16F": 33327,
                "RGB16F": 34843,
                "RGBA16F": 34842,
                "R32F": 33326,
                "RG32F": 33328,
                "RGB32F": 34837,
                "RGBA32F": 34836,
                "RGB9E5": 35901,
                "RG11B10F": 35898,
                "RG3B2": 10768,
                "R5G6B5": 36194,
                "RGB5A1": 32855,
                "RGBA4": 32854,
                "RGB10A2": 32857,
                "RGB10A2U": 36975,
                "D16": 33189,
                "D24": 33190,
                "D24S8": 35056,
                "D32": 33191,
                "D32F": 36012,
                "D32FS8X24": 36013,
                "RGB_DXT1": 33776,
                "RGBA_DXT1": 33777,
                "RGBA_DXT3": 33778,
                "RGBA_DXT5": 33779,
                "R_ATI1N_UNorm": 36283,
                "R_ATI1N_SNorm": 36284,
                "RG_ATI2N_UNorm": 36285,
                "RG_ATI2N_SNorm": 36286,
                "RGB_BP_UNSIGNED_FLOAT": 36495,
                "RGB_BP_SIGNED_FLOAT": 36494,
                "RGB_BP_UNorm": 36492,
                "R11_EAC_UNorm": 37488,
                "R11_EAC_SNorm": 37489,
                "RG11_EAC_UNorm": 37490,
                "RG11_EAC_SNorm": 37491,
                "RGB8_ETC2": 37492,
                "SRGB8_ETC2": 37493,
                "RGB8_PunchThrough_Alpha1_ETC2": 37494,
                "SRGB8_PunchThrough_Alpha1_ETC2": 37495,
                "RGBA8_ETC2_EAC": 37496,
                "SRGB8_Alpha8_ETC2_EAC": 37497,
                "RGB8_ETC1": 36196,
                "SRGB8": 35905,
                "SRGB8_Alpha8": 35907,
                "SRGB_DXT1": 35916,
                "SRGB_Alpha_DXT1": 35917,
                "SRGB_Alpha_DXT3": 35918,
                "SRGB_Alpha_DXT5": 35919,
                "SRGB_BP_UNorm": 36493,
                "DepthFormat": 6402,
                "AlphaFormat": 6406,
                "RGBFormat": 6407,
                "RGBAFormat": 6408,
                "LuminanceFormat": 6409,
                "LuminanceAlphaFormat": 6410
            }
        }
        Enum {
            name: "Filter"
            values: {
                "Nearest": 9728,
                "Linear": 9729,
                "NearestMipMapNearest": 9984,
                "NearestMipMapLinear": 9986,
                "LinearMipMapNearest": 9985,
                "LinearMipMapLinear": 9987
            }
        }
        Enum {
            name: "CubeMapFace"
            values: {
                "CubeMapPositiveX": 34069,
                "CubeMapNegativeX": 34070,
                "CubeMapPositiveY": 34071,
                "CubeMapNegativeY": 34072,
                "CubeMapPositiveZ": 34073,
                "CubeMapNegativeZ": 34074,
                "AllFaces": 34075
            }
        }
        Enum {
            name: "ComparisonFunction"
            values: {
                "CompareLessEqual": 515,
                "CompareGreaterEqual": 518,
                "CompareLess": 513,
                "CompareGreater": 516,
                "CompareEqual": 514,
                "CommpareNotEqual": 517,
                "CompareAlways": 519,
                "CompareNever": 512
            }
        }
        Enum {
            name: "ComparisonMode"
            values: {
                "CompareRefToTexture": 34894,
                "CompareNone": 0
            }
        }
        Enum {
            name: "HandleType"
            values: {
                "NoHandle": 0,
                "OpenGLTextureId": 1
            }
        }
        Property { name: "target"; type: "Target"; isReadonly: true }
        Property { name: "format"; type: "TextureFormat" }
        Property { name: "generateMipMaps"; type: "bool" }
        Property {
            name: "wrapMode"
            type: "Qt3DRender::QTextureWrapMode"
            isReadonly: true
            isPointer: true
        }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Property { name: "depth"; type: "int" }
        Property { name: "magnificationFilter"; type: "Filter" }
        Property { name: "minificationFilter"; type: "Filter" }
        Property { name: "maximumAnisotropy"; type: "float" }
        Property { name: "comparisonFunction"; type: "ComparisonFunction" }
        Property { name: "comparisonMode"; type: "ComparisonMode" }
        Property { name: "layers"; type: "int" }
        Property { name: "samples"; type: "int" }
        Property { name: "handleType"; revision: 13; type: "HandleType"; isReadonly: true }
        Property { name: "handle"; revision: 13; type: "QVariant"; isReadonly: true }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "TextureFormat" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Signal {
            name: "generateMipMapsChanged"
            Parameter { name: "generateMipMaps"; type: "bool" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "int" }
        }
        Signal {
            name: "depthChanged"
            Parameter { name: "depth"; type: "int" }
        }
        Signal {
            name: "magnificationFilterChanged"
            Parameter { name: "magnificationFilter"; type: "Filter" }
        }
        Signal {
            name: "minificationFilterChanged"
            Parameter { name: "minificationFilter"; type: "Filter" }
        }
        Signal {
            name: "maximumAnisotropyChanged"
            Parameter { name: "maximumAnisotropy"; type: "float" }
        }
        Signal {
            name: "comparisonFunctionChanged"
            Parameter { name: "comparisonFunction"; type: "ComparisonFunction" }
        }
        Signal {
            name: "comparisonModeChanged"
            Parameter { name: "comparisonMode"; type: "ComparisonMode" }
        }
        Signal {
            name: "layersChanged"
            Parameter { name: "layers"; type: "int" }
        }
        Signal {
            name: "samplesChanged"
            Parameter { name: "samples"; type: "int" }
        }
        Signal {
            name: "handleTypeChanged"
            revision: 13
            Parameter { name: "handleType"; type: "HandleType" }
        }
        Signal {
            name: "handleChanged"
            revision: 13
            Parameter { name: "handle"; type: "QVariant" }
        }
        Method {
            name: "setFormat"
            Parameter { name: "format"; type: "TextureFormat" }
        }
        Method {
            name: "setGenerateMipMaps"
            Parameter { name: "gen"; type: "bool" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "int" }
        }
        Method {
            name: "setDepth"
            Parameter { name: "depth"; type: "int" }
        }
        Method {
            name: "setMinificationFilter"
            Parameter { name: "f"; type: "Filter" }
        }
        Method {
            name: "setMagnificationFilter"
            Parameter { name: "f"; type: "Filter" }
        }
        Method {
            name: "setMaximumAnisotropy"
            Parameter { name: "anisotropy"; type: "float" }
        }
        Method {
            name: "setComparisonFunction"
            Parameter { name: "function"; type: "ComparisonFunction" }
        }
        Method {
            name: "setComparisonMode"
            Parameter { name: "mode"; type: "ComparisonMode" }
        }
        Method {
            name: "setLayers"
            Parameter { name: "layers"; type: "int" }
        }
        Method {
            name: "setSamples"
            Parameter { name: "samples"; type: "int" }
        }
        Method {
            name: "updateData"
            Parameter { name: "update"; type: "QTextureDataUpdate" }
        }
    }
    Component {
        name: "Qt3DRender::QAbstractTextureImage"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/QAbstractTextureImage 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "mipLevel"; type: "int" }
        Property { name: "layer"; type: "int" }
        Property { name: "face"; type: "Qt3DRender::QAbstractTexture::CubeMapFace" }
        Signal {
            name: "mipLevelChanged"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Signal {
            name: "layerChanged"
            Parameter { name: "layer"; type: "int" }
        }
        Signal {
            name: "faceChanged"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
        Method {
            name: "setMipLevel"
            Parameter { name: "level"; type: "int" }
        }
        Method {
            name: "setLayer"
            Parameter { name: "layer"; type: "int" }
        }
        Method {
            name: "setFace"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
    }
    Component {
        name: "Qt3DRender::QAlphaCoverage"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/AlphaCoverage 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QAlphaTest"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/AlphaTest 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "AlphaFunction"
            values: {
                "Never": 512,
                "Always": 519,
                "Less": 513,
                "LessOrEqual": 515,
                "Equal": 514,
                "GreaterOrEqual": 518,
                "Greater": 516,
                "NotEqual": 517
            }
        }
        Property { name: "alphaFunction"; type: "AlphaFunction" }
        Property { name: "referenceValue"; type: "float" }
        Signal {
            name: "alphaFunctionChanged"
            Parameter { name: "alphaFunction"; type: "AlphaFunction" }
        }
        Signal {
            name: "referenceValueChanged"
            Parameter { name: "referenceValue"; type: "float" }
        }
        Method {
            name: "setAlphaFunction"
            Parameter { name: "alphaFunction"; type: "AlphaFunction" }
        }
        Method {
            name: "setReferenceValue"
            Parameter { name: "referenceValue"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QAttribute"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/Attribute 2.0",
            "Qt3D.Render/Attribute 2.10",
            "Qt3D.Render/Attribute 2.11"
        ]
        exportMetaObjectRevisions: [0, 10, 11]
        Enum {
            name: "AttributeType"
            values: {
                "VertexAttribute": 0,
                "IndexAttribute": 1,
                "DrawIndirectAttribute": 2
            }
        }
        Enum {
            name: "VertexBaseType"
            values: {
                "Byte": 0,
                "UnsignedByte": 1,
                "Short": 2,
                "UnsignedShort": 3,
                "Int": 4,
                "UnsignedInt": 5,
                "HalfFloat": 6,
                "Float": 7,
                "Double": 8
            }
        }
        Property { name: "buffer"; type: "Qt3DRender::QBuffer"; isPointer: true }
        Property { name: "name"; type: "string" }
        Property { name: "vertexBaseType"; type: "VertexBaseType" }
        Property { name: "vertexSize"; type: "uint" }
        Property { name: "count"; type: "uint" }
        Property { name: "byteStride"; type: "uint" }
        Property { name: "byteOffset"; type: "uint" }
        Property { name: "divisor"; type: "uint" }
        Property { name: "attributeType"; type: "AttributeType" }
        Property { name: "defaultPositionAttributeName"; type: "string"; isReadonly: true }
        Property { name: "defaultNormalAttributeName"; type: "string"; isReadonly: true }
        Property { name: "defaultColorAttributeName"; type: "string"; isReadonly: true }
        Property { name: "defaultTextureCoordinateAttributeName"; type: "string"; isReadonly: true }
        Property {
            name: "defaultTextureCoordinate1AttributeName"
            revision: 11
            type: "string"
            isReadonly: true
        }
        Property {
            name: "defaultTextureCoordinate2AttributeName"
            revision: 11
            type: "string"
            isReadonly: true
        }
        Property { name: "defaultTangentAttributeName"; type: "string"; isReadonly: true }
        Property {
            name: "defaultJointIndicesAttributeName"
            revision: 10
            type: "string"
            isReadonly: true
        }
        Property {
            name: "defaultJointWeightsAttributeName"
            revision: 10
            type: "string"
            isReadonly: true
        }
        Signal {
            name: "bufferChanged"
            Parameter { name: "buffer"; type: "QBuffer"; isPointer: true }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "vertexBaseTypeChanged"
            Parameter { name: "vertexBaseType"; type: "VertexBaseType" }
        }
        Signal {
            name: "vertexSizeChanged"
            Parameter { name: "vertexSize"; type: "uint" }
        }
        Signal {
            name: "dataTypeChanged"
            Parameter { name: "vertexBaseType"; type: "VertexBaseType" }
        }
        Signal {
            name: "dataSizeChanged"
            Parameter { name: "vertexSize"; type: "uint" }
        }
        Signal {
            name: "countChanged"
            Parameter { name: "count"; type: "uint" }
        }
        Signal {
            name: "byteStrideChanged"
            Parameter { name: "byteStride"; type: "uint" }
        }
        Signal {
            name: "byteOffsetChanged"
            Parameter { name: "byteOffset"; type: "uint" }
        }
        Signal {
            name: "divisorChanged"
            Parameter { name: "divisor"; type: "uint" }
        }
        Signal {
            name: "attributeTypeChanged"
            Parameter { name: "attributeType"; type: "AttributeType" }
        }
        Method {
            name: "setBuffer"
            Parameter { name: "buffer"; type: "QBuffer"; isPointer: true }
        }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setVertexBaseType"
            Parameter { name: "type"; type: "VertexBaseType" }
        }
        Method {
            name: "setVertexSize"
            Parameter { name: "size"; type: "uint" }
        }
        Method {
            name: "setDataType"
            Parameter { name: "type"; type: "VertexBaseType" }
        }
        Method {
            name: "setDataSize"
            Parameter { name: "size"; type: "uint" }
        }
        Method {
            name: "setCount"
            Parameter { name: "count"; type: "uint" }
        }
        Method {
            name: "setByteStride"
            Parameter { name: "byteStride"; type: "uint" }
        }
        Method {
            name: "setByteOffset"
            Parameter { name: "byteOffset"; type: "uint" }
        }
        Method {
            name: "setDivisor"
            Parameter { name: "divisor"; type: "uint" }
        }
        Method {
            name: "setAttributeType"
            Parameter { name: "attributeType"; type: "AttributeType" }
        }
        Method { name: "defaultPositionAttributeName"; type: "string" }
        Method { name: "defaultNormalAttributeName"; type: "string" }
        Method { name: "defaultColorAttributeName"; type: "string" }
        Method { name: "defaultTextureCoordinateAttributeName"; type: "string" }
        Method { name: "defaultTangentAttributeName"; type: "string" }
    }
    Component {
        name: "Qt3DRender::QBlendEquation"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/BlendEquation 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "BlendFunction"
            values: {
                "Add": 32774,
                "Subtract": 32778,
                "ReverseSubtract": 32779,
                "Min": 32775,
                "Max": 32776
            }
        }
        Property { name: "blendFunction"; type: "BlendFunction" }
        Signal {
            name: "blendFunctionChanged"
            Parameter { name: "blendFunction"; type: "BlendFunction" }
        }
        Method {
            name: "setBlendFunction"
            Parameter { name: "blendFunction"; type: "BlendFunction" }
        }
    }
    Component {
        name: "Qt3DRender::QBlendEquationArguments"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/BlendEquationArguments 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Blending"
            values: {
                "Zero": 0,
                "One": 1,
                "SourceColor": 768,
                "SourceAlpha": 770,
                "Source1Alpha": 771,
                "Source1Color": 772,
                "DestinationColor": 774,
                "DestinationAlpha": 772,
                "SourceAlphaSaturate": 776,
                "ConstantColor": 32769,
                "ConstantAlpha": 32771,
                "OneMinusSourceColor": 769,
                "OneMinusSourceAlpha": 771,
                "OneMinusDestinationAlpha": 773,
                "OneMinusDestinationColor": 775,
                "OneMinusConstantColor": 32770,
                "OneMinusConstantAlpha": 32772,
                "OneMinusSource1Alpha": 32773,
                "OneMinusSource1Color": 32774,
                "OneMinusSource1Color0": 32774
            }
        }
        Property { name: "sourceRgb"; type: "Blending" }
        Property { name: "sourceAlpha"; type: "Blending" }
        Property { name: "destinationRgb"; type: "Blending" }
        Property { name: "destinationAlpha"; type: "Blending" }
        Property { name: "bufferIndex"; type: "int" }
        Signal {
            name: "sourceRgbChanged"
            Parameter { name: "sourceRgb"; type: "Blending" }
        }
        Signal {
            name: "sourceAlphaChanged"
            Parameter { name: "sourceAlpha"; type: "Blending" }
        }
        Signal {
            name: "destinationRgbChanged"
            Parameter { name: "destinationRgb"; type: "Blending" }
        }
        Signal {
            name: "destinationAlphaChanged"
            Parameter { name: "destinationAlpha"; type: "Blending" }
        }
        Signal {
            name: "sourceRgbaChanged"
            Parameter { name: "sourceRgba"; type: "Blending" }
        }
        Signal {
            name: "destinationRgbaChanged"
            Parameter { name: "destinationRgba"; type: "Blending" }
        }
        Signal {
            name: "bufferIndexChanged"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setSourceRgb"
            Parameter { name: "sourceRgb"; type: "Blending" }
        }
        Method {
            name: "setDestinationRgb"
            Parameter { name: "destinationRgb"; type: "Blending" }
        }
        Method {
            name: "setSourceAlpha"
            Parameter { name: "sourceAlpha"; type: "Blending" }
        }
        Method {
            name: "setDestinationAlpha"
            Parameter { name: "destinationAlpha"; type: "Blending" }
        }
        Method {
            name: "setSourceRgba"
            Parameter { name: "sourceRgba"; type: "Blending" }
        }
        Method {
            name: "setDestinationRgba"
            Parameter { name: "destinationRgba"; type: "Blending" }
        }
        Method {
            name: "setBufferIndex"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        name: "Qt3DRender::QBlitFramebuffer"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/BlitFramebuffer 2.10"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "InterpolationMethod"
            values: {
                "Nearest": 0,
                "Linear": 1
            }
        }
        Property { name: "source"; type: "Qt3DRender::QRenderTarget"; isPointer: true }
        Property { name: "destination"; type: "Qt3DRender::QRenderTarget"; isPointer: true }
        Property { name: "sourceRect"; type: "QRectF" }
        Property { name: "destinationRect"; type: "QRectF" }
        Property {
            name: "sourceAttachmentPoint"
            type: "Qt3DRender::QRenderTargetOutput::AttachmentPoint"
        }
        Property {
            name: "destinationAttachmentPoint"
            type: "Qt3DRender::QRenderTargetOutput::AttachmentPoint"
        }
        Property { name: "interpolationMethod"; type: "InterpolationMethod" }
    }
    Component {
        name: "Qt3DRender::QBuffer"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/BufferBase 2.0", "Qt3D.Render/BufferBase 2.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0, 9]
        Enum {
            name: "BufferType"
            values: {
                "VertexBuffer": 34962,
                "IndexBuffer": 34963,
                "PixelPackBuffer": 35051,
                "PixelUnpackBuffer": 35052,
                "UniformBuffer": 35345,
                "ShaderStorageBuffer": 37074,
                "DrawIndirectBuffer": 36671
            }
        }
        Enum {
            name: "UsageType"
            values: {
                "StreamDraw": 35040,
                "StreamRead": 35041,
                "StreamCopy": 35042,
                "StaticDraw": 35044,
                "StaticRead": 35045,
                "StaticCopy": 35046,
                "DynamicDraw": 35048,
                "DynamicRead": 35049,
                "DynamicCopy": 35050
            }
        }
        Enum {
            name: "AccessType"
            values: {
                "Write": 1,
                "Read": 2,
                "ReadWrite": 3
            }
        }
        Property { name: "type"; type: "BufferType" }
        Property { name: "usage"; type: "UsageType" }
        Property { name: "syncData"; type: "bool" }
        Property { name: "accessType"; revision: 9; type: "AccessType" }
        Signal {
            name: "dataChanged"
            Parameter { name: "bytes"; type: "QByteArray" }
        }
        Signal {
            name: "typeChanged"
            Parameter { name: "type"; type: "BufferType" }
        }
        Signal {
            name: "usageChanged"
            Parameter { name: "usage"; type: "UsageType" }
        }
        Signal {
            name: "syncDataChanged"
            Parameter { name: "syncData"; type: "bool" }
        }
        Signal {
            name: "accessTypeChanged"
            Parameter { name: "access"; type: "AccessType" }
        }
        Signal { name: "dataAvailable" }
        Method {
            name: "setType"
            Parameter { name: "type"; type: "BufferType" }
        }
        Method {
            name: "setUsage"
            Parameter { name: "usage"; type: "UsageType" }
        }
        Method {
            name: "setSyncData"
            Parameter { name: "syncData"; type: "bool" }
        }
        Method {
            name: "setAccessType"
            Parameter { name: "access"; type: "AccessType" }
        }
        Method {
            name: "updateData"
            Parameter { name: "offset"; type: "int" }
            Parameter { name: "bytes"; type: "QByteArray" }
        }
    }
    Component {
        name: "Qt3DRender::QBufferCapture"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/BufferCapture 2.9"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QCamera"
        prototype: "Qt3DCore::QEntity"
        exports: [
            "Qt3D.Render/Camera 2.0",
            "Qt3D.Render/Camera 2.14",
            "Qt3D.Render/Camera 2.9"
        ]
        exportMetaObjectRevisions: [0, 14, 9]
        Enum {
            name: "CameraTranslationOption"
            values: {
                "TranslateViewCenter": 0,
                "DontTranslateViewCenter": 1
            }
        }
        Property { name: "projectionType"; type: "Qt3DRender::QCameraLens::ProjectionType" }
        Property { name: "nearPlane"; type: "float" }
        Property { name: "farPlane"; type: "float" }
        Property { name: "fieldOfView"; type: "float" }
        Property { name: "aspectRatio"; type: "float" }
        Property { name: "left"; type: "float" }
        Property { name: "right"; type: "float" }
        Property { name: "bottom"; type: "float" }
        Property { name: "top"; type: "float" }
        Property { name: "projectionMatrix"; type: "QMatrix4x4" }
        Property { name: "exposure"; revision: 9; type: "float" }
        Property { name: "position"; type: "QVector3D" }
        Property { name: "upVector"; type: "QVector3D" }
        Property { name: "viewCenter"; type: "QVector3D" }
        Property { name: "viewVector"; type: "QVector3D"; isReadonly: true }
        Property { name: "viewMatrix"; type: "QMatrix4x4"; isReadonly: true }
        Property {
            name: "lens"
            revision: 14
            type: "Qt3DRender::QCameraLens"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "transform"
            revision: 14
            type: "Qt3DCore::QTransform"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "projectionTypeChanged"
            Parameter { name: "projectionType"; type: "QCameraLens::ProjectionType" }
        }
        Signal {
            name: "nearPlaneChanged"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Signal {
            name: "farPlaneChanged"
            Parameter { name: "farPlane"; type: "float" }
        }
        Signal {
            name: "fieldOfViewChanged"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Signal {
            name: "aspectRatioChanged"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Signal {
            name: "leftChanged"
            Parameter { name: "left"; type: "float" }
        }
        Signal {
            name: "rightChanged"
            Parameter { name: "right"; type: "float" }
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "float" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "float" }
        }
        Signal {
            name: "projectionMatrixChanged"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Signal {
            name: "exposureChanged"
            Parameter { name: "exposure"; type: "float" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Signal {
            name: "upVectorChanged"
            Parameter { name: "upVector"; type: "QVector3D" }
        }
        Signal {
            name: "viewCenterChanged"
            Parameter { name: "viewCenter"; type: "QVector3D" }
        }
        Signal {
            name: "viewVectorChanged"
            Parameter { name: "viewVector"; type: "QVector3D" }
        }
        Method {
            name: "setProjectionType"
            Parameter { name: "type"; type: "QCameraLens::ProjectionType" }
        }
        Method {
            name: "setNearPlane"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Method {
            name: "setFarPlane"
            Parameter { name: "farPlane"; type: "float" }
        }
        Method {
            name: "setFieldOfView"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Method {
            name: "setAspectRatio"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "float" }
        }
        Method {
            name: "setRight"
            Parameter { name: "right"; type: "float" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "float" }
        }
        Method {
            name: "setTop"
            Parameter { name: "top"; type: "float" }
        }
        Method {
            name: "setProjectionMatrix"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Method {
            name: "setExposure"
            Parameter { name: "exposure"; type: "float" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "setUpVector"
            Parameter { name: "upVector"; type: "QVector3D" }
        }
        Method {
            name: "setViewCenter"
            Parameter { name: "viewCenter"; type: "QVector3D" }
        }
        Method { name: "viewAll" }
        Method {
            name: "viewSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "viewEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "tiltRotation"
            type: "QQuaternion"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "panRotation"
            type: "QQuaternion"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "rollRotation"
            type: "QQuaternion"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "rotation"
            type: "QQuaternion"
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "translate"
            Parameter { name: "vLocal"; type: "QVector3D" }
            Parameter { name: "option"; type: "CameraTranslationOption" }
        }
        Method {
            name: "translate"
            Parameter { name: "vLocal"; type: "QVector3D" }
        }
        Method {
            name: "translateWorld"
            Parameter { name: "vWorld"; type: "QVector3D" }
            Parameter { name: "option"; type: "CameraTranslationOption" }
        }
        Method {
            name: "translateWorld"
            Parameter { name: "vWorld"; type: "QVector3D" }
        }
        Method {
            name: "tilt"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "pan"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "pan"
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "roll"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "tiltAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "panAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "panAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "rollAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "rotate"
            Parameter { name: "q"; type: "QQuaternion" }
        }
        Method {
            name: "rotateAboutViewCenter"
            Parameter { name: "q"; type: "QQuaternion" }
        }
    }
    Component {
        name: "Qt3DRender::QCameraLens"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/CameraLens 2.0", "Qt3D.Render/CameraLens 2.9"]
        exportMetaObjectRevisions: [0, 9]
        Enum {
            name: "ProjectionType"
            values: {
                "OrthographicProjection": 0,
                "PerspectiveProjection": 1,
                "FrustumProjection": 2,
                "CustomProjection": 3
            }
        }
        Property { name: "projectionType"; type: "ProjectionType" }
        Property { name: "nearPlane"; type: "float" }
        Property { name: "farPlane"; type: "float" }
        Property { name: "fieldOfView"; type: "float" }
        Property { name: "aspectRatio"; type: "float" }
        Property { name: "left"; type: "float" }
        Property { name: "right"; type: "float" }
        Property { name: "bottom"; type: "float" }
        Property { name: "top"; type: "float" }
        Property { name: "projectionMatrix"; type: "QMatrix4x4" }
        Property { name: "exposure"; revision: 9; type: "float" }
        Signal {
            name: "projectionTypeChanged"
            Parameter { name: "projectionType"; type: "QCameraLens::ProjectionType" }
        }
        Signal {
            name: "nearPlaneChanged"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Signal {
            name: "farPlaneChanged"
            Parameter { name: "farPlane"; type: "float" }
        }
        Signal {
            name: "fieldOfViewChanged"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Signal {
            name: "aspectRatioChanged"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Signal {
            name: "leftChanged"
            Parameter { name: "left"; type: "float" }
        }
        Signal {
            name: "rightChanged"
            Parameter { name: "right"; type: "float" }
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "float" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "float" }
        }
        Signal {
            name: "projectionMatrixChanged"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Signal {
            name: "exposureChanged"
            Parameter { name: "exposure"; type: "float" }
        }
        Signal {
            name: "viewSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setProjectionType"
            Parameter { name: "projectionType"; type: "ProjectionType" }
        }
        Method {
            name: "setNearPlane"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Method {
            name: "setFarPlane"
            Parameter { name: "farPlane"; type: "float" }
        }
        Method {
            name: "setFieldOfView"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Method {
            name: "setAspectRatio"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "float" }
        }
        Method {
            name: "setRight"
            Parameter { name: "right"; type: "float" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "float" }
        }
        Method {
            name: "setTop"
            Parameter { name: "top"; type: "float" }
        }
        Method {
            name: "setProjectionMatrix"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Method {
            name: "setExposure"
            Parameter { name: "exposure"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QCameraSelector"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/CameraSelector 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        Signal {
            name: "cameraChanged"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Property { name: "data"; revision: 200; type: "QObject"; isList: true; isReadonly: true }
        Property {
            name: "childNodes"
            revision: 200
            type: "Qt3DCore::QNode"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QClearBuffers"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/ClearBuffers 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "BufferType"
            values: {
                "None": 0,
                "ColorBuffer": 1,
                "DepthBuffer": 2,
                "StencilBuffer": 4,
                "DepthStencilBuffer": 6,
                "ColorDepthBuffer": 3,
                "ColorDepthStencilBuffer": 7,
                "AllBuffers": -1
            }
        }
        Property { name: "buffers"; type: "BufferType" }
        Property { name: "clearColor"; type: "QColor" }
        Property { name: "clearDepthValue"; type: "float" }
        Property { name: "clearStencilValue"; type: "int" }
        Property { name: "colorBuffer"; type: "Qt3DRender::QRenderTargetOutput"; isPointer: true }
        Signal {
            name: "buffersChanged"
            Parameter { name: "buffers"; type: "BufferType" }
        }
        Signal {
            name: "clearColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "clearDepthValueChanged"
            Parameter { name: "clearDepthValue"; type: "float" }
        }
        Signal {
            name: "clearStencilValueChanged"
            Parameter { name: "clearStencilValue"; type: "int" }
        }
        Signal {
            name: "colorBufferChanged"
            Parameter { name: "buffer"; type: "QRenderTargetOutput"; isPointer: true }
        }
        Method {
            name: "setBuffers"
            Parameter { name: "buffers"; type: "BufferType" }
        }
        Method {
            name: "setClearColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setClearDepthValue"
            Parameter { name: "clearDepthValue"; type: "float" }
        }
        Method {
            name: "setClearStencilValue"
            Parameter { name: "clearStencilValue"; type: "int" }
        }
        Method {
            name: "setColorBuffer"
            Parameter { name: "buffer"; type: "QRenderTargetOutput"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DRender::QClipPlane"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/ClipPlane 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "planeIndex"; type: "int" }
        Property { name: "normal"; type: "QVector3D" }
        Property { name: "distance"; type: "float" }
        Signal {
            name: "planeIndexChanged"
            Parameter { name: "planeIndex"; type: "int" }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "QVector3D" }
        }
        Signal {
            name: "distanceChanged"
            Parameter { name: "distance"; type: "float" }
        }
        Method {
            name: "setPlaneIndex"
            Parameter { type: "int" }
        }
        Method {
            name: "setNormal"
            Parameter { type: "QVector3D" }
        }
        Method {
            name: "setDistance"
            Parameter { type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QColorMask"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/ColorMask 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "redMasked"; type: "bool" }
        Property { name: "greenMasked"; type: "bool" }
        Property { name: "blueMasked"; type: "bool" }
        Property { name: "alphaMasked"; type: "bool" }
        Signal {
            name: "redMaskedChanged"
            Parameter { name: "redMasked"; type: "bool" }
        }
        Signal {
            name: "greenMaskedChanged"
            Parameter { name: "greenMasked"; type: "bool" }
        }
        Signal {
            name: "blueMaskedChanged"
            Parameter { name: "blueMasked"; type: "bool" }
        }
        Signal {
            name: "alphaMaskedChanged"
            Parameter { name: "alphaMasked"; type: "bool" }
        }
        Method {
            name: "setRedMasked"
            Parameter { name: "redMasked"; type: "bool" }
        }
        Method {
            name: "setGreenMasked"
            Parameter { name: "greenMasked"; type: "bool" }
        }
        Method {
            name: "setBlueMasked"
            Parameter { name: "blueMasked"; type: "bool" }
        }
        Method {
            name: "setAlphaMasked"
            Parameter { name: "alphaMasked"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DRender::QComputeCommand"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/ComputeCommand 2.0",
            "Qt3D.Render/ComputeCommand 2.13"
        ]
        exportMetaObjectRevisions: [0, 13]
        Enum {
            name: "RunType"
            values: {
                "Continuous": 0,
                "Manual": 1
            }
        }
        Property { name: "workGroupX"; type: "int" }
        Property { name: "workGroupY"; type: "int" }
        Property { name: "workGroupZ"; type: "int" }
        Property { name: "runType"; revision: 13; type: "RunType" }
        Method {
            name: "setWorkGroupX"
            Parameter { name: "workGroupX"; type: "int" }
        }
        Method {
            name: "setWorkGroupY"
            Parameter { name: "workGroupY"; type: "int" }
        }
        Method {
            name: "setWorkGroupZ"
            Parameter { name: "workGroupZ"; type: "int" }
        }
        Method {
            name: "setRunType"
            revision: 13
            Parameter { name: "runType"; type: "RunType" }
        }
        Method {
            name: "trigger"
            revision: 13
            Parameter { name: "frameCount"; type: "int" }
        }
        Method { name: "trigger"; revision: 13 }
        Method {
            name: "trigger"
            revision: 13
            Parameter { name: "workGroupX"; type: "int" }
            Parameter { name: "workGroupY"; type: "int" }
            Parameter { name: "workGroupZ"; type: "int" }
            Parameter { name: "frameCount"; type: "int" }
        }
        Method {
            name: "trigger"
            revision: 13
            Parameter { name: "workGroupX"; type: "int" }
            Parameter { name: "workGroupY"; type: "int" }
            Parameter { name: "workGroupZ"; type: "int" }
        }
    }
    Component {
        name: "Qt3DRender::QCullFace"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/CullFace 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "CullingMode"
            values: {
                "NoCulling": 0,
                "Front": 1028,
                "Back": 1029,
                "FrontAndBack": 1032
            }
        }
        Property { name: "mode"; type: "CullingMode" }
        Signal {
            name: "modeChanged"
            Parameter { name: "mode"; type: "CullingMode" }
        }
        Method {
            name: "setMode"
            Parameter { name: "mode"; type: "CullingMode" }
        }
    }
    Component {
        name: "Qt3DRender::QDepthRange"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/DepthRange 2.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "nearValue"; type: "double" }
        Property { name: "farValue"; type: "double" }
        Signal {
            name: "nearValueChanged"
            Parameter { name: "nearValue"; type: "double" }
        }
        Signal {
            name: "farValueChanged"
            Parameter { name: "farValue"; type: "double" }
        }
        Method {
            name: "setNearValue"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "setFarValue"
            Parameter { name: "value"; type: "double" }
        }
    }
    Component {
        name: "Qt3DRender::QDepthTest"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/DepthTest 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "DepthFunction"
            values: {
                "Never": 512,
                "Always": 519,
                "Less": 513,
                "LessOrEqual": 515,
                "Equal": 514,
                "GreaterOrEqual": 518,
                "Greater": 516,
                "NotEqual": 517
            }
        }
        Property { name: "depthFunction"; type: "DepthFunction" }
        Signal {
            name: "depthFunctionChanged"
            Parameter { name: "depthFunction"; type: "DepthFunction" }
        }
        Method {
            name: "setDepthFunction"
            Parameter { name: "depthFunction"; type: "DepthFunction" }
        }
    }
    Component {
        name: "Qt3DRender::QDirectionalLight"
        prototype: "Qt3DRender::QAbstractLight"
        exports: ["Qt3D.Render/DirectionalLight 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "worldDirection"; type: "QVector3D" }
        Signal {
            name: "worldDirectionChanged"
            Parameter { name: "worldDirection"; type: "QVector3D" }
        }
        Method {
            name: "setWorldDirection"
            Parameter { name: "worldDirection"; type: "QVector3D" }
        }
    }
    Component {
        name: "Qt3DRender::QDispatchCompute"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/DispatchCompute 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "workGroupX"; type: "int" }
        Property { name: "workGroupY"; type: "int" }
        Property { name: "workGroupZ"; type: "int" }
        Method {
            name: "setWorkGroupX"
            Parameter { name: "workGroupX"; type: "int" }
        }
        Method {
            name: "setWorkGroupY"
            Parameter { name: "workGroupY"; type: "int" }
        }
        Method {
            name: "setWorkGroupZ"
            Parameter { name: "workGroupZ"; type: "int" }
        }
    }
    Component {
        name: "Qt3DRender::QDithering"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/Dithering 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QEffect"
        defaultProperty: "data"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/Effect 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "techniques"
            revision: 200
            type: "Qt3DRender::QTechnique"
            isList: true
            isReadonly: true
        }
        Property {
            name: "parameters"
            revision: 200
            type: "Qt3DRender::QParameter"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QEnvironmentLight"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/EnvironmentLight 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "irradiance"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Signal {
            name: "irradianceChanged"
            Parameter {
                name: "environmentIrradiance"
                type: "Qt3DRender::QAbstractTexture"
                isPointer: true
            }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "environmentSpecular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setIrradiance"
            Parameter { name: "irradiance"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DRender::QFilterKey"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/FilterKey 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "value"; type: "QVariant" }
        Property { name: "name"; type: "string" }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "setValue"
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "setName"
            Parameter { name: "customType"; type: "string" }
        }
    }
    Component {
        name: "Qt3DRender::QFrameGraphNode"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/FrameGraphNode 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QFrontFace"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/FrontFace 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "WindingDirection"
            values: {
                "ClockWise": 2304,
                "CounterClockWise": 2305
            }
        }
        Property { name: "direction"; type: "WindingDirection" }
        Signal {
            name: "directionChanged"
            Parameter { name: "direction"; type: "WindingDirection" }
        }
        Method {
            name: "setDirection"
            Parameter { name: "direction"; type: "WindingDirection" }
        }
    }
    Component {
        name: "Qt3DRender::QFrustumCulling"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/FrustumCulling 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QGeometry"
        defaultProperty: "attributes"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/Geometry 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "boundingVolumePositionAttribute"
            type: "Qt3DRender::QAttribute"
            isPointer: true
        }
        Property { name: "minExtent"; revision: 13; type: "QVector3D"; isReadonly: true }
        Property { name: "maxExtent"; revision: 13; type: "QVector3D"; isReadonly: true }
        Signal {
            name: "boundingVolumePositionAttributeChanged"
            Parameter { name: "boundingVolumePositionAttribute"; type: "QAttribute"; isPointer: true }
        }
        Signal {
            name: "minExtentChanged"
            revision: 13
            Parameter { name: "minExtent"; type: "QVector3D" }
        }
        Signal {
            name: "maxExtentChanged"
            revision: 13
            Parameter { name: "maxExtent"; type: "QVector3D" }
        }
        Method {
            name: "setBoundingVolumePositionAttribute"
            Parameter { name: "boundingVolumePositionAttribute"; type: "QAttribute"; isPointer: true }
        }
        Method {
            name: "addAttribute"
            Parameter { name: "attribute"; type: "Qt3DRender::QAttribute"; isPointer: true }
        }
        Method {
            name: "removeAttribute"
            Parameter { name: "attribute"; type: "Qt3DRender::QAttribute"; isPointer: true }
        }
        Property {
            name: "attributes"
            revision: 200
            type: "Qt3DRender::QAttribute"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QGeometryRenderer"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/GeometryRenderer 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "PrimitiveType"
            values: {
                "Points": 0,
                "Lines": 1,
                "LineLoop": 2,
                "LineStrip": 3,
                "Triangles": 4,
                "TriangleStrip": 5,
                "TriangleFan": 6,
                "LinesAdjacency": 10,
                "TrianglesAdjacency": 12,
                "LineStripAdjacency": 11,
                "TriangleStripAdjacency": 13,
                "Patches": 14
            }
        }
        Property { name: "instanceCount"; type: "int" }
        Property { name: "vertexCount"; type: "int" }
        Property { name: "indexOffset"; type: "int" }
        Property { name: "firstInstance"; type: "int" }
        Property { name: "firstVertex"; type: "int" }
        Property { name: "indexBufferByteOffset"; type: "int" }
        Property { name: "restartIndexValue"; type: "int" }
        Property { name: "verticesPerPatch"; type: "int" }
        Property { name: "primitiveRestartEnabled"; type: "bool" }
        Property { name: "geometry"; type: "Qt3DRender::QGeometry"; isPointer: true }
        Property { name: "primitiveType"; type: "PrimitiveType" }
        Signal {
            name: "instanceCountChanged"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Signal {
            name: "vertexCountChanged"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Signal {
            name: "indexOffsetChanged"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Signal {
            name: "firstInstanceChanged"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Signal {
            name: "firstVertexChanged"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Signal {
            name: "indexBufferByteOffsetChanged"
            Parameter { name: "offset"; type: "int" }
        }
        Signal {
            name: "restartIndexValueChanged"
            Parameter { name: "restartIndexValue"; type: "int" }
        }
        Signal {
            name: "verticesPerPatchChanged"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Signal {
            name: "primitiveRestartEnabledChanged"
            Parameter { name: "primitiveRestartEnabled"; type: "bool" }
        }
        Signal {
            name: "geometryChanged"
            Parameter { name: "geometry"; type: "QGeometry"; isPointer: true }
        }
        Signal {
            name: "primitiveTypeChanged"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
        Method {
            name: "setInstanceCount"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Method {
            name: "setVertexCount"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Method {
            name: "setIndexOffset"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Method {
            name: "setFirstInstance"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Method {
            name: "setFirstVertex"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Method {
            name: "setIndexBufferByteOffset"
            Parameter { name: "offset"; type: "int" }
        }
        Method {
            name: "setRestartIndexValue"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setVerticesPerPatch"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Method {
            name: "setPrimitiveRestartEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "geometry"; type: "QGeometry"; isPointer: true }
        }
        Method {
            name: "setPrimitiveType"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
    }
    Component {
        name: "Qt3DRender::QGraphicsApiFilter"
        prototype: "QObject"
        exports: ["Qt3D.Render/GraphicsApiFilter 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Api"
            values: {
                "OpenGLES": 2,
                "OpenGL": 1,
                "Vulkan": 3,
                "DirectX": 4,
                "RHI": 5
            }
        }
        Enum {
            name: "OpenGLProfile"
            values: {
                "NoProfile": 0,
                "CoreProfile": 1,
                "CompatibilityProfile": 2
            }
        }
        Property { name: "api"; type: "Qt3DRender::QGraphicsApiFilter::Api" }
        Property { name: "profile"; type: "Qt3DRender::QGraphicsApiFilter::OpenGLProfile" }
        Property { name: "minorVersion"; type: "int" }
        Property { name: "majorVersion"; type: "int" }
        Property { name: "extensions"; type: "QStringList" }
        Property { name: "vendor"; type: "string" }
        Signal {
            name: "apiChanged"
            Parameter { name: "api"; type: "Qt3DRender::QGraphicsApiFilter::Api" }
        }
        Signal {
            name: "profileChanged"
            Parameter { name: "profile"; type: "Qt3DRender::QGraphicsApiFilter::OpenGLProfile" }
        }
        Signal {
            name: "minorVersionChanged"
            Parameter { name: "minorVersion"; type: "int" }
        }
        Signal {
            name: "majorVersionChanged"
            Parameter { name: "majorVersion"; type: "int" }
        }
        Signal {
            name: "extensionsChanged"
            Parameter { name: "extensions"; type: "QStringList" }
        }
        Signal {
            name: "vendorChanged"
            Parameter { name: "vendor"; type: "string" }
        }
        Signal { name: "graphicsApiFilterChanged" }
        Method {
            name: "setApi"
            Parameter { name: "api"; type: "Api" }
        }
        Method {
            name: "setProfile"
            Parameter { name: "profile"; type: "OpenGLProfile" }
        }
        Method {
            name: "setMinorVersion"
            Parameter { name: "minorVersion"; type: "int" }
        }
        Method {
            name: "setMajorVersion"
            Parameter { name: "majorVersion"; type: "int" }
        }
        Method {
            name: "setExtensions"
            Parameter { name: "extensions"; type: "QStringList" }
        }
        Method {
            name: "setVendor"
            Parameter { name: "vendor"; type: "string" }
        }
    }
    Component {
        name: "Qt3DRender::QLayer"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/Layer 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "recursive"; type: "bool" }
    }
    Component {
        name: "Qt3DRender::QLayerFilter"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/LayerFilter 2.0"]
        exportMetaObjectRevisions: [200]
        Enum {
            name: "FilterMode"
            values: {
                "AcceptAnyMatchingLayers": 0,
                "AcceptAllMatchingLayers": 1,
                "DiscardAnyMatchingLayers": 2,
                "DiscardAllMatchingLayers": 3
            }
        }
        Property { name: "filterMode"; type: "FilterMode" }
        Signal {
            name: "filterModeChanged"
            Parameter { name: "filterMode"; type: "FilterMode" }
        }
        Property {
            name: "layers"
            revision: 200
            type: "Qt3DRender::QLayer"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QLevelOfDetail"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/LevelOfDetail 2.9"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "ThresholdType"
            values: {
                "DistanceToCameraThreshold": 0,
                "ProjectedScreenPixelSizeThreshold": 1
            }
        }
        Property { name: "camera"; type: "Qt3DRender::QCamera"; isPointer: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "thresholdType"; type: "ThresholdType" }
        Property { name: "thresholds"; type: "QVector<qreal>" }
        Property { name: "volumeOverride"; type: "Qt3DRender::QLevelOfDetailBoundingSphere" }
        Signal {
            name: "cameraChanged"
            Parameter { name: "camera"; type: "QCamera"; isPointer: true }
        }
        Signal {
            name: "currentIndexChanged"
            Parameter { name: "currentIndex"; type: "int" }
        }
        Signal {
            name: "thresholdTypeChanged"
            Parameter { name: "thresholdType"; type: "ThresholdType" }
        }
        Signal {
            name: "thresholdsChanged"
            Parameter { name: "thresholds"; type: "QVector<qreal>" }
        }
        Signal {
            name: "volumeOverrideChanged"
            Parameter { name: "volumeOverride"; type: "QLevelOfDetailBoundingSphere" }
        }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "QCamera"; isPointer: true }
        }
        Method {
            name: "setCurrentIndex"
            Parameter { name: "currentIndex"; type: "int" }
        }
        Method {
            name: "setThresholdType"
            Parameter { name: "thresholdType"; type: "ThresholdType" }
        }
        Method {
            name: "setThresholds"
            Parameter { name: "thresholds"; type: "QVector<qreal>" }
        }
        Method {
            name: "setVolumeOverride"
            Parameter { name: "volumeOverride"; type: "QLevelOfDetailBoundingSphere" }
        }
        Method {
            name: "createBoundingSphere"
            type: "Qt3DRender::QLevelOfDetailBoundingSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QLevelOfDetailSwitch"
        prototype: "Qt3DRender::QLevelOfDetail"
        exports: ["Qt3D.Render/LevelOfDetailSwitch 2.9"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QLineWidth"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/LineWidth 2.10"]
        exportMetaObjectRevisions: [0]
        Property { name: "value"; type: "float" }
        Property { name: "smooth"; type: "bool" }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setValue"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setSmooth"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DRender::QMaterial"
        defaultProperty: "data"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/Material 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "effect"; type: "Qt3DRender::QEffect"; isPointer: true }
        Signal {
            name: "effectChanged"
            Parameter { name: "effect"; type: "QEffect"; isPointer: true }
        }
        Method {
            name: "setEffect"
            Parameter { name: "effect"; type: "QEffect"; isPointer: true }
        }
        Property {
            name: "parameters"
            revision: 200
            type: "Qt3DRender::QParameter"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QMemoryBarrier"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/MemoryBarrier 2.9"]
        exportMetaObjectRevisions: [209]
        Enum {
            name: "Operation"
            values: {
                "None": 0,
                "VertexAttributeArray": 1,
                "ElementArray": 2,
                "Uniform": 4,
                "TextureFetch": 8,
                "ShaderImageAccess": 16,
                "Command": 32,
                "PixelBuffer": 64,
                "TextureUpdate": 128,
                "BufferUpdate": 256,
                "FrameBuffer": 512,
                "TransformFeedback": 1024,
                "AtomicCounter": 2048,
                "ShaderStorage": 4096,
                "QueryBuffer": 8192,
                "All": -1
            }
        }
        Signal {
            name: "waitOperationsChanged"
            Parameter { name: "barrierTypes"; type: "QMemoryBarrier::Operations" }
        }
        Method {
            name: "setWaitOperations"
            Parameter { name: "operations"; type: "QMemoryBarrier::Operations" }
        }
        Property { name: "waitFor"; revision: 209; type: "int" }
    }
    Component {
        name: "Qt3DRender::QMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Render/Mesh 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "None": 0,
                "Loading": 1,
                "Ready": 2,
                "Error": 3
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "meshName"; type: "string" }
        Property { name: "status"; revision: 11; type: "Status"; isReadonly: true }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "meshNameChanged"
            Parameter { name: "meshName"; type: "string" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setMeshName"
            Parameter { name: "meshName"; type: "string" }
        }
    }
    Component {
        name: "Qt3DRender::QMultiSampleAntiAliasing"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/MultiSampleAntiAliasing 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QNoDepthMask"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/NoDepthMask 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QNoDraw"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/NoDraw 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QNoPicking"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/NoPicking 2.14"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QObjectPicker"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/ObjectPicker 2.0",
            "Qt3D.Render/ObjectPicker 2.13",
            "Qt3D.Render/ObjectPicker 2.9"
        ]
        exportMetaObjectRevisions: [0, 13, 9]
        Property { name: "hoverEnabled"; type: "bool" }
        Property { name: "dragEnabled"; type: "bool" }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
        Property { name: "containsMouse"; type: "bool"; isReadonly: true }
        Property { name: "priority"; revision: 13; type: "int" }
        Signal {
            name: "pressed"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal {
            name: "clicked"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal {
            name: "moved"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal {
            name: "hoverEnabledChanged"
            Parameter { name: "hoverEnabled"; type: "bool" }
        }
        Signal {
            name: "dragEnabledChanged"
            Parameter { name: "dragEnabled"; type: "bool" }
        }
        Signal {
            name: "pressedChanged"
            Parameter { name: "pressed"; type: "bool" }
        }
        Signal {
            name: "containsMouseChanged"
            Parameter { name: "containsMouse"; type: "bool" }
        }
        Signal {
            name: "priorityChanged"
            revision: 13
            Parameter { name: "priority"; type: "int" }
        }
        Method {
            name: "setHoverEnabled"
            Parameter { name: "hoverEnabled"; type: "bool" }
        }
        Method {
            name: "setDragEnabled"
            Parameter { name: "dragEnabled"; type: "bool" }
        }
        Method {
            name: "setPriority"
            revision: 13
            Parameter { name: "priority"; type: "int" }
        }
    }
    Component {
        name: "Qt3DRender::QParameter"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/QParameter 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "name"; type: "string" }
        Property { name: "value"; type: "QVariant" }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setValue"
            Parameter { name: "dv"; type: "QVariant" }
        }
    }
    Component {
        name: "Qt3DRender::QPickEvent"
        prototype: "QObject"
        exports: ["Qt3D.Render/PickEvent 2.0", "Qt3D.Render/PickEvent 2.14"]
        isCreatable: false
        exportMetaObjectRevisions: [0, 14]
        Enum {
            name: "Buttons"
            values: {
                "LeftButton": 1,
                "RightButton": 2,
                "MiddleButton": 4,
                "BackButton": 8,
                "NoButton": 0
            }
        }
        Enum {
            name: "Modifiers"
            values: {
                "NoModifier": 0,
                "ShiftModifier": 33554432,
                "ControlModifier": 67108864,
                "AltModifier": 134217728,
                "MetaModifier": 268435456,
                "KeypadModifier": 536870912
            }
        }
        Property { name: "accepted"; type: "bool" }
        Property { name: "position"; type: "QPointF"; isReadonly: true }
        Property { name: "distance"; type: "float"; isReadonly: true }
        Property { name: "localIntersection"; type: "QVector3D"; isReadonly: true }
        Property { name: "worldIntersection"; type: "QVector3D"; isReadonly: true }
        Property { name: "button"; type: "Qt3DRender::QPickEvent::Buttons"; isReadonly: true }
        Property { name: "buttons"; type: "int"; isReadonly: true }
        Property { name: "modifiers"; type: "int"; isReadonly: true }
        Property {
            name: "viewport"
            revision: 14
            type: "Qt3DRender::QViewport"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "entity"
            revision: 14
            type: "Qt3DCore::QEntity"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "acceptedChanged"
            Parameter { name: "accepted"; type: "bool" }
        }
        Method {
            name: "setAccepted"
            Parameter { name: "accepted"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DRender::QPickingSettings"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/PickingSettings 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "PickMethod"
            values: {
                "BoundingVolumePicking": 0,
                "TrianglePicking": 1,
                "LinePicking": 2,
                "PointPicking": 4,
                "PrimitivePicking": 7
            }
        }
        Enum {
            name: "PickResultMode"
            values: {
                "NearestPick": 0,
                "AllPicks": 1,
                "NearestPriorityPick": 2
            }
        }
        Enum {
            name: "FaceOrientationPickingMode"
            values: {
                "FrontFace": 1,
                "BackFace": 2,
                "FrontAndBackFace": 3
            }
        }
        Property { name: "pickMethod"; type: "PickMethod" }
        Property { name: "pickResultMode"; type: "PickResultMode" }
        Property { name: "faceOrientationPickingMode"; type: "FaceOrientationPickingMode" }
        Property { name: "worldSpaceTolerance"; revision: 10; type: "float" }
        Signal {
            name: "pickMethodChanged"
            Parameter { name: "pickMethod"; type: "QPickingSettings::PickMethod" }
        }
        Signal {
            name: "pickResultModeChanged"
            Parameter { name: "pickResult"; type: "QPickingSettings::PickResultMode" }
        }
        Signal {
            name: "faceOrientationPickingModeChanged"
            Parameter {
                name: "faceOrientationPickingMode"
                type: "QPickingSettings::FaceOrientationPickingMode"
            }
        }
        Signal {
            name: "worldSpaceToleranceChanged"
            Parameter { name: "worldSpaceTolerance"; type: "float" }
        }
        Method {
            name: "setPickMethod"
            Parameter { name: "pickMethod"; type: "PickMethod" }
        }
        Method {
            name: "setPickResultMode"
            Parameter { name: "pickResultMode"; type: "PickResultMode" }
        }
        Method {
            name: "setFaceOrientationPickingMode"
            Parameter { name: "faceOrientationPickingMode"; type: "FaceOrientationPickingMode" }
        }
        Method {
            name: "setWorldSpaceTolerance"
            Parameter { name: "worldSpaceTolerance"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QPointLight"
        prototype: "Qt3DRender::QAbstractLight"
        exports: ["Qt3D.Render/PointLight 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "constantAttenuation"; type: "float" }
        Property { name: "linearAttenuation"; type: "float" }
        Property { name: "quadraticAttenuation"; type: "float" }
        Signal {
            name: "constantAttenuationChanged"
            Parameter { name: "constantAttenuation"; type: "float" }
        }
        Signal {
            name: "linearAttenuationChanged"
            Parameter { name: "linearAttenuation"; type: "float" }
        }
        Signal {
            name: "quadraticAttenuationChanged"
            Parameter { name: "quadraticAttenuation"; type: "float" }
        }
        Method {
            name: "setConstantAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setLinearAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setQuadraticAttenuation"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QPointSize"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/PointSize 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "SizeMode"
            values: {
                "Fixed": 0,
                "Programmable": 1
            }
        }
        Property { name: "sizeMode"; type: "SizeMode" }
        Property { name: "value"; type: "float" }
        Signal {
            name: "sizeModeChanged"
            Parameter { name: "sizeMode"; type: "SizeMode" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setSizeMode"
            Parameter { name: "sizeMode"; type: "SizeMode" }
        }
        Method {
            name: "setValue"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QPolygonOffset"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/PolygonOffset 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "scaleFactor"; type: "float" }
        Property { name: "depthSteps"; type: "float" }
        Signal {
            name: "scaleFactorChanged"
            Parameter { name: "scaleFactor"; type: "float" }
        }
        Signal {
            name: "depthStepsChanged"
            Parameter { name: "depthSteps"; type: "float" }
        }
        Method {
            name: "setScaleFactor"
            Parameter { name: "scaleFactor"; type: "float" }
        }
        Method {
            name: "setDepthSteps"
            Parameter { name: "depthSteps"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QProximityFilter"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/ProximityFilter 2.10"]
        exportMetaObjectRevisions: [0]
        Property { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        Property { name: "distanceThreshold"; type: "float" }
        Signal {
            name: "entityChanged"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Signal {
            name: "distanceThresholdChanged"
            Parameter { name: "distanceThreshold"; type: "float" }
        }
        Method {
            name: "setEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setDistanceThreshold"
            Parameter { name: "distanceThreshold"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QRasterMode"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/RasterMode 2.13"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "RasterMode"
            values: {
                "Points": 6912,
                "Lines": 6913,
                "Fill": 6914
            }
        }
        Enum {
            name: "FaceMode"
            values: {
                "Front": 1028,
                "Back": 1029,
                "FrontAndBack": 1032
            }
        }
        Property { name: "rasterMode"; type: "RasterMode" }
        Property { name: "faceMode"; type: "FaceMode" }
        Signal {
            name: "rasterModeChanged"
            Parameter { name: "rasterMode"; type: "RasterMode" }
        }
        Signal {
            name: "faceModeChanged"
            Parameter { name: "faceMode"; type: "FaceMode" }
        }
        Method {
            name: "setRasterMode"
            Parameter { name: "rasterMode"; type: "RasterMode" }
        }
        Method {
            name: "setFaceMode"
            Parameter { name: "faceMode"; type: "FaceMode" }
        }
    }
    Component {
        name: "Qt3DRender::QRayCaster"
        prototype: "Qt3DRender::QAbstractRayCaster"
        Property { name: "origin"; type: "QVector3D" }
        Property { name: "direction"; type: "QVector3D" }
        Property { name: "length"; type: "float" }
        Signal {
            name: "originChanged"
            Parameter { name: "origin"; type: "QVector3D" }
        }
        Signal {
            name: "directionChanged"
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setOrigin"
            Parameter { name: "origin"; type: "QVector3D" }
        }
        Method {
            name: "setDirection"
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
        Method { name: "trigger" }
        Method {
            name: "trigger"
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QRenderCapabilities"
        prototype: "QObject"
        exports: ["Qt3D.Render/RenderCapabilities 2.15"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "API"
            values: {
                "OpenGL": 1,
                "OpenGLES": 2
            }
        }
        Enum {
            name: "Profile"
            values: {
                "NoProfile": 0,
                "CoreProfile": 1,
                "CompatibilityProfile": 2
            }
        }
        Property { name: "valid"; type: "bool"; isReadonly: true }
        Property { name: "api"; type: "API"; isReadonly: true }
        Property { name: "profile"; type: "Profile"; isReadonly: true }
        Property { name: "majorVersion"; type: "int"; isReadonly: true }
        Property { name: "minorVersion"; type: "int"; isReadonly: true }
        Property { name: "extensions"; type: "QStringList"; isReadonly: true }
        Property { name: "vendor"; type: "string"; isReadonly: true }
        Property { name: "renderer"; type: "string"; isReadonly: true }
        Property { name: "driverVersion"; type: "string"; isReadonly: true }
        Property { name: "glslVersion"; type: "string"; isReadonly: true }
        Property { name: "maxSamples"; type: "int"; isReadonly: true }
        Property { name: "maxTextureSize"; type: "int"; isReadonly: true }
        Property { name: "maxTextureUnits"; type: "int"; isReadonly: true }
        Property { name: "maxTextureLayers"; type: "int"; isReadonly: true }
        Property { name: "supportsUBO"; type: "bool"; isReadonly: true }
        Property { name: "maxUBOSize"; type: "int"; isReadonly: true }
        Property { name: "maxUBOBindings"; type: "int"; isReadonly: true }
        Property { name: "supportsSSBO"; type: "bool"; isReadonly: true }
        Property { name: "maxSSBOSize"; type: "int"; isReadonly: true }
        Property { name: "maxSSBOBindings"; type: "int"; isReadonly: true }
        Property { name: "supportsImageStore"; type: "bool"; isReadonly: true }
        Property { name: "maxImageUnits"; type: "int"; isReadonly: true }
        Property { name: "supportsCompute"; type: "bool"; isReadonly: true }
        Property { name: "maxWorkGroupCountX"; type: "int"; isReadonly: true }
        Property { name: "maxWorkGroupCountY"; type: "int"; isReadonly: true }
        Property { name: "maxWorkGroupCountZ"; type: "int"; isReadonly: true }
        Property { name: "maxWorkGroupSizeX"; type: "int"; isReadonly: true }
        Property { name: "maxWorkGroupSizeY"; type: "int"; isReadonly: true }
        Property { name: "maxWorkGroupSizeZ"; type: "int"; isReadonly: true }
        Property { name: "maxComputeInvocations"; type: "int"; isReadonly: true }
        Property { name: "maxComputeSharedMemorySize"; type: "int"; isReadonly: true }
    }
    Component {
        name: "Qt3DRender::QRenderCapture"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/RenderCapture 2.1",
            "Qt3D.Render/RenderCapture 2.9"
        ]
        exportMetaObjectRevisions: [0, 9]
        Method {
            name: "requestCapture"
            type: "Qt3DRender::QRenderCaptureReply*"
            Parameter { name: "captureId"; type: "int" }
        }
        Method { name: "requestCapture"; revision: 9; type: "Qt3DRender::QRenderCaptureReply*" }
        Method {
            name: "requestCapture"
            revision: 10
            type: "Qt3DRender::QRenderCaptureReply*"
            Parameter { name: "rect"; type: "QRect" }
        }
    }
    Component {
        name: "Qt3DRender::QRenderCaptureReply"
        prototype: "QObject"
        exports: ["Qt3D.Render/RenderCaptureReply 2.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "image"; type: "QImage"; isReadonly: true }
        Property { name: "captureId"; type: "int"; isReadonly: true }
        Property { name: "complete"; type: "bool"; isReadonly: true }
        Signal {
            name: "completeChanged"
            Parameter { name: "isComplete"; type: "bool" }
        }
        Signal { name: "completed" }
        Method {
            name: "saveImage"
            type: "bool"
            Parameter { name: "fileName"; type: "string" }
        }
        Method {
            name: "saveToFile"
            Parameter { name: "fileName"; type: "string" }
        }
    }
    Component {
        name: "Qt3DRender::QRenderPass"
        defaultProperty: "data"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/RenderPass 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "shaderProgram"; type: "Qt3DRender::QShaderProgram"; isPointer: true }
        Signal {
            name: "shaderProgramChanged"
            Parameter { name: "shaderProgram"; type: "QShaderProgram"; isPointer: true }
        }
        Method {
            name: "setShaderProgram"
            Parameter { name: "shaderProgram"; type: "QShaderProgram"; isPointer: true }
        }
        Property {
            name: "filterKeys"
            revision: 200
            type: "Qt3DRender::QFilterKey"
            isList: true
            isReadonly: true
        }
        Property {
            name: "renderStates"
            revision: 200
            type: "Qt3DRender::QRenderState"
            isList: true
            isReadonly: true
        }
        Property {
            name: "parameters"
            revision: 200
            type: "Qt3DRender::QParameter"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QRenderPassFilter"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/RenderPassFilter 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "matchAny"
            revision: 200
            type: "Qt3DRender::QFilterKey"
            isList: true
            isReadonly: true
        }
        Property {
            name: "parameters"
            revision: 200
            type: "Qt3DRender::QParameter"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QRenderSettings"
        defaultProperty: "activeFrameGraph"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/RenderSettings 2.0",
            "Qt3D.Render/RenderSettings 2.15"
        ]
        exportMetaObjectRevisions: [0, 15]
        Enum {
            name: "RenderPolicy"
            values: {
                "OnDemand": 0,
                "Always": 1
            }
        }
        Property {
            name: "renderCapabilities"
            revision: 15
            type: "Qt3DRender::QRenderCapabilities"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "pickingSettings"
            type: "Qt3DRender::QPickingSettings"
            isReadonly: true
            isPointer: true
        }
        Property { name: "renderPolicy"; type: "RenderPolicy" }
        Property { name: "activeFrameGraph"; type: "Qt3DRender::QFrameGraphNode"; isPointer: true }
        Signal {
            name: "activeFrameGraphChanged"
            Parameter { name: "activeFrameGraph"; type: "QFrameGraphNode"; isPointer: true }
        }
        Signal {
            name: "renderPolicyChanged"
            Parameter { name: "renderPolicy"; type: "RenderPolicy" }
        }
        Method {
            name: "setActiveFrameGraph"
            Parameter { name: "activeFrameGraph"; type: "QFrameGraphNode"; isPointer: true }
        }
        Method {
            name: "setRenderPolicy"
            Parameter { name: "renderPolicy"; type: "RenderPolicy" }
        }
    }
    Component {
        name: "Qt3DRender::QRenderState"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/RenderState 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QRenderStateSet"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/RenderStateSet 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "renderStates"
            revision: 200
            type: "Qt3DRender::QRenderState"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QRenderSurfaceSelector"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/RenderSurfaceSelector 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "surface"; type: "QObject"; isPointer: true }
        Property { name: "externalRenderTargetSize"; type: "QSize" }
        Property { name: "surfacePixelRatio"; type: "float" }
        Signal {
            name: "surfaceChanged"
            Parameter { name: "surface"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "externalRenderTargetSizeChanged"
            Parameter { name: "size"; type: "QSize" }
        }
        Signal {
            name: "surfacePixelRatioChanged"
            Parameter { name: "ratio"; type: "float" }
        }
        Method {
            name: "setSurface"
            Parameter { name: "surfaceObject"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "setSurfacePixelRatio"
            Parameter { name: "ratio"; type: "float" }
        }
        Method {
            name: "setExternalRenderTargetSize"
            Parameter { name: "size"; type: "QSize" }
        }
    }
    Component {
        name: "Qt3DRender::QRenderTarget"
        defaultProperty: "data"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/RenderTarget 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "attachments"
            revision: 200
            type: "Qt3DRender::QRenderTargetOutput"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QRenderTargetOutput"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/RenderTargetOutput 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "AttachmentPoint"
            values: {
                "Color0": 0,
                "Color1": 1,
                "Color2": 2,
                "Color3": 3,
                "Color4": 4,
                "Color5": 5,
                "Color6": 6,
                "Color7": 7,
                "Color8": 8,
                "Color9": 9,
                "Color10": 10,
                "Color11": 11,
                "Color12": 12,
                "Color13": 13,
                "Color14": 14,
                "Color15": 15,
                "Depth": 16,
                "Stencil": 17,
                "DepthStencil": 18
            }
        }
        Property { name: "attachmentPoint"; type: "AttachmentPoint" }
        Property { name: "texture"; type: "QAbstractTexture"; isPointer: true }
        Property { name: "mipLevel"; type: "int" }
        Property { name: "layer"; type: "int" }
        Property { name: "face"; type: "Qt3DRender::QAbstractTexture::CubeMapFace" }
        Signal {
            name: "attachmentPointChanged"
            Parameter { name: "attachmentPoint"; type: "AttachmentPoint" }
        }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "mipLevelChanged"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Signal {
            name: "layerChanged"
            Parameter { name: "layer"; type: "int" }
        }
        Signal {
            name: "faceChanged"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
        Method {
            name: "setAttachmentPoint"
            Parameter { name: "attachmentPoint"; type: "AttachmentPoint" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setMipLevel"
            Parameter { name: "level"; type: "int" }
        }
        Method {
            name: "setLayer"
            Parameter { name: "layer"; type: "int" }
        }
        Method {
            name: "setFace"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
    }
    Component {
        name: "Qt3DRender::QRenderTargetSelector"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/RenderTargetSelector 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "target"; type: "Qt3DRender::QRenderTarget"; isPointer: true }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "QRenderTarget"; isPointer: true }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "QRenderTarget"; isPointer: true }
        }
        Property { name: "drawBuffers"; revision: 200; type: "QVariantList" }
    }
    Component {
        name: "Qt3DRender::QSceneLoader"
        defaultProperty: "data"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/SceneLoader 2.0",
            "Qt3D.Render/SceneLoader 2.9"
        ]
        exportMetaObjectRevisions: [200, 9]
        Enum {
            name: "Status"
            values: {
                "None": 0,
                "Loading": 1,
                "Ready": 2,
                "Error": 3
            }
        }
        Enum {
            name: "ComponentType"
            values: {
                "UnknownComponent": 0,
                "GeometryRendererComponent": 1,
                "TransformComponent": 2,
                "MaterialComponent": 3,
                "LightComponent": 4,
                "CameraLensComponent": 5
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setStatus"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "entity"
            revision: 9
            type: "Qt3DCore::QEntity*"
            Parameter { name: "entityName"; type: "string" }
        }
        Method { name: "entityNames"; revision: 9; type: "QStringList" }
        Method {
            name: "component"
            revision: 9
            type: "Qt3DCore::QComponent*"
            Parameter { name: "entityName"; type: "string" }
            Parameter { name: "componentType"; type: "ComponentType" }
        }
    }
    Component {
        name: "Qt3DRender::QScissorTest"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/ScissorTest 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "left"; type: "int" }
        Property { name: "bottom"; type: "int" }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Signal {
            name: "leftChanged"
            Parameter { name: "left"; type: "int" }
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "int" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "int" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "int" }
        }
    }
    Component {
        name: "Qt3DRender::QScreenRayCaster"
        prototype: "Qt3DRender::QAbstractRayCaster"
        Property { name: "position"; type: "QPoint" }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QPoint" }
        }
        Method { name: "trigger" }
        Method {
            name: "trigger"
            Parameter { name: "position"; type: "QPoint" }
        }
    }
    Component {
        name: "Qt3DRender::QSeamlessCubemap"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/SeamlessCubemap 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QSetFence"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/SetFence 2.13"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "HandleType"
            values: {
                "NoHandle": 0,
                "OpenGLFenceId": 1
            }
        }
        Property { name: "handleType"; type: "HandleType"; isReadonly: true }
        Property { name: "handle"; type: "QVariant"; isReadonly: true }
        Signal {
            name: "handleTypeChanged"
            Parameter { name: "handleType"; type: "HandleType" }
        }
        Signal {
            name: "handleChanged"
            Parameter { name: "handle"; type: "QVariant" }
        }
    }
    Component {
        name: "Qt3DRender::QShaderData"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/QShaderData 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::QShaderImage"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/ShaderImage 2.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Access"
            values: {
                "ReadOnly": 0,
                "WriteOnly": 1,
                "ReadWrite": 2
            }
        }
        Enum {
            name: "ImageFormat"
            values: {
                "NoFormat": 0,
                "Automatic": 1,
                "R8_UNorm": 33321,
                "RG8_UNorm": 33323,
                "RGBA8_UNorm": 32856,
                "R16_UNorm": 33322,
                "RG16_UNorm": 33324,
                "RGBA16_UNorm": 32859,
                "R8_SNorm": 36756,
                "RG8_SNorm": 36757,
                "RGBA8_SNorm": 36759,
                "R16_SNorm": 36760,
                "RG16_SNorm": 36761,
                "RGBA16_SNorm": 36763,
                "R8U": 33330,
                "RG8U": 33336,
                "RGBA8U": 36220,
                "R16U": 33332,
                "RG16U": 33338,
                "RGBA16U": 36214,
                "R32U": 33334,
                "RG32U": 33340,
                "RGBA32U": 36208,
                "R8I": 33329,
                "RG8I": 33335,
                "RGBA8I": 36238,
                "R16I": 33331,
                "RG16I": 33337,
                "RGBA16I": 36232,
                "R32I": 33333,
                "RG32I": 33339,
                "RGBA32I": 36226,
                "R16F": 33325,
                "RG16F": 33327,
                "RGBA16F": 34842,
                "R32F": 33326,
                "RG32F": 33328,
                "RGBA32F": 34836,
                "RG11B10F": 35898,
                "RGB10A2": 32857,
                "RGB10A2U": 36975
            }
        }
        Property { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "layered"; type: "bool" }
        Property { name: "mipLevel"; type: "int" }
        Property { name: "layer"; type: "int" }
        Property { name: "access"; type: "Access" }
        Property { name: "format"; type: "ImageFormat" }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "layeredChanged"
            Parameter { name: "layered"; type: "bool" }
        }
        Signal {
            name: "mipLevelChanged"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Signal {
            name: "layerChanged"
            Parameter { name: "layer"; type: "int" }
        }
        Signal {
            name: "accessChanged"
            Parameter { name: "access"; type: "Access" }
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "ImageFormat" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setLayered"
            Parameter { name: "layered"; type: "bool" }
        }
        Method {
            name: "setMipLevel"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Method {
            name: "setLayer"
            Parameter { name: "layer"; type: "int" }
        }
        Method {
            name: "setAccess"
            Parameter { name: "access"; type: "Access" }
        }
        Method {
            name: "setFormat"
            Parameter { name: "format"; type: "ImageFormat" }
        }
    }
    Component {
        name: "Qt3DRender::QShaderProgram"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/ShaderProgram 2.0",
            "Qt3D.Render/ShaderProgram 2.15",
            "Qt3D.Render/ShaderProgram 2.9"
        ]
        exportMetaObjectRevisions: [0, 15, 9]
        Enum {
            name: "ShaderType"
            values: {
                "Vertex": 0,
                "Fragment": 1,
                "TessellationControl": 2,
                "TessellationEvaluation": 3,
                "Geometry": 4,
                "Compute": 5
            }
        }
        Enum {
            name: "Status"
            values: {
                "NotReady": 0,
                "Ready": 1,
                "Error": 2
            }
        }
        Enum {
            name: "Format"
            values: {
                "GLSL": 0,
                "SPIRV": 1
            }
        }
        Property { name: "vertexShaderCode"; type: "QByteArray" }
        Property { name: "tessellationControlShaderCode"; type: "QByteArray" }
        Property { name: "tessellationEvaluationShaderCode"; type: "QByteArray" }
        Property { name: "geometryShaderCode"; type: "QByteArray" }
        Property { name: "fragmentShaderCode"; type: "QByteArray" }
        Property { name: "computeShaderCode"; type: "QByteArray" }
        Property { name: "log"; revision: 9; type: "string"; isReadonly: true }
        Property { name: "status"; revision: 9; type: "Status"; isReadonly: true }
        Property { name: "format"; revision: 15; type: "Format" }
        Signal {
            name: "vertexShaderCodeChanged"
            Parameter { name: "vertexShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationControlShaderCodeChanged"
            Parameter { name: "tessellationControlShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationEvaluationShaderCodeChanged"
            Parameter { name: "tessellationEvaluationShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "geometryShaderCodeChanged"
            Parameter { name: "geometryShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "fragmentShaderCodeChanged"
            Parameter { name: "fragmentShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "computeShaderCodeChanged"
            Parameter { name: "computeShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "logChanged"
            Parameter { name: "log"; type: "string" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "Format" }
        }
        Method {
            name: "setVertexShaderCode"
            Parameter { name: "vertexShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setTessellationControlShaderCode"
            Parameter { name: "tessellationControlShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setTessellationEvaluationShaderCode"
            Parameter { name: "tessellationEvaluationShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setGeometryShaderCode"
            Parameter { name: "geometryShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setFragmentShaderCode"
            Parameter { name: "fragmentShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setComputeShaderCode"
            Parameter { name: "computeShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "loadSource"
            type: "QByteArray"
            Parameter { name: "sourceUrl"; type: "QUrl" }
        }
    }
    Component {
        name: "Qt3DRender::QShaderProgramBuilder"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/ShaderProgramBuilder 2.10",
            "Qt3D.Render/ShaderProgramBuilder 2.13"
        ]
        exportMetaObjectRevisions: [0, 13]
        Property { name: "shaderProgram"; type: "Qt3DRender::QShaderProgram"; isPointer: true }
        Property { name: "enabledLayers"; type: "QStringList" }
        Property { name: "vertexShaderGraph"; type: "QUrl" }
        Property { name: "tessellationControlShaderGraph"; type: "QUrl" }
        Property { name: "tessellationEvaluationShaderGraph"; type: "QUrl" }
        Property { name: "geometryShaderGraph"; type: "QUrl" }
        Property { name: "fragmentShaderGraph"; type: "QUrl" }
        Property { name: "computeShaderGraph"; type: "QUrl" }
        Property { name: "vertexShaderCode"; revision: 13; type: "QByteArray"; isReadonly: true }
        Property {
            name: "tessellationControlShaderCode"
            revision: 13
            type: "QByteArray"
            isReadonly: true
        }
        Property {
            name: "tessellationEvaluationShaderCode"
            revision: 13
            type: "QByteArray"
            isReadonly: true
        }
        Property { name: "geometryShaderCode"; revision: 13; type: "QByteArray"; isReadonly: true }
        Property { name: "fragmentShaderCode"; revision: 13; type: "QByteArray"; isReadonly: true }
        Property { name: "computeShaderCode"; revision: 13; type: "QByteArray"; isReadonly: true }
        Signal {
            name: "shaderProgramChanged"
            Parameter { name: "shaderProgram"; type: "Qt3DRender::QShaderProgram"; isPointer: true }
        }
        Signal {
            name: "enabledLayersChanged"
            Parameter { name: "layers"; type: "QStringList" }
        }
        Signal {
            name: "vertexShaderGraphChanged"
            Parameter { name: "vertexShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "tessellationControlShaderGraphChanged"
            Parameter { name: "tessellationControlShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "tessellationEvaluationShaderGraphChanged"
            Parameter { name: "tessellationEvaluationShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "geometryShaderGraphChanged"
            Parameter { name: "geometryShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "fragmentShaderGraphChanged"
            Parameter { name: "fragmentShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "computeShaderGraphChanged"
            Parameter { name: "computeShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "vertexShaderCodeChanged"
            revision: 13
            Parameter { name: "vertexShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationControlShaderCodeChanged"
            revision: 13
            Parameter { name: "tessellationControlShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationEvaluationShaderCodeChanged"
            revision: 13
            Parameter { name: "tessellationEvaluationShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "geometryShaderCodeChanged"
            revision: 13
            Parameter { name: "geometryShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "fragmentShaderCodeChanged"
            revision: 13
            Parameter { name: "fragmentShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "computeShaderCodeChanged"
            revision: 13
            Parameter { name: "computeShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setShaderProgram"
            Parameter { name: "program"; type: "Qt3DRender::QShaderProgram"; isPointer: true }
        }
        Method {
            name: "setEnabledLayers"
            Parameter { name: "layers"; type: "QStringList" }
        }
        Method {
            name: "setVertexShaderGraph"
            Parameter { name: "vertexShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setTessellationControlShaderGraph"
            Parameter { name: "tessellationControlShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setTessellationEvaluationShaderGraph"
            Parameter { name: "tessellationEvaluationShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setGeometryShaderGraph"
            Parameter { name: "geometryShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setFragmentShaderGraph"
            Parameter { name: "fragmentShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setComputeShaderGraph"
            Parameter { name: "computeShaderGraph"; type: "QUrl" }
        }
    }
    Component {
        name: "Qt3DRender::QSharedGLTexture"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/SharedGLTexture 2.13"]
        exportMetaObjectRevisions: [0]
        Property { name: "textureId"; type: "int" }
        Signal {
            name: "textureIdChanged"
            Parameter { name: "textureId"; type: "int" }
        }
        Method {
            name: "setTextureId"
            Parameter { name: "id"; type: "int" }
        }
    }
    Component {
        name: "Qt3DRender::QSortPolicy"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/SortPolicy 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "SortType"
            values: {
                "StateChangeCost": 1,
                "BackToFront": 2,
                "Material": 4,
                "FrontToBack": 8,
                "Texture": 16,
                "Uniform": 32
            }
        }
        Property { name: "sortTypes"; type: "QVector<int>" }
        Signal {
            name: "sortTypesChanged"
            Parameter { name: "sortTypes"; type: "QVector<SortType>" }
        }
        Signal {
            name: "sortTypesChanged"
            Parameter { name: "sortTypes"; type: "QVector<int>" }
        }
        Method {
            name: "setSortTypes"
            Parameter { name: "sortTypes"; type: "QVector<SortType>" }
        }
        Method {
            name: "setSortTypes"
            Parameter { name: "sortTypesInt"; type: "QVector<int>" }
        }
    }
    Component {
        name: "Qt3DRender::QSpotLight"
        prototype: "Qt3DRender::QAbstractLight"
        exports: ["Qt3D.Render/SpotLight 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "constantAttenuation"; type: "float" }
        Property { name: "linearAttenuation"; type: "float" }
        Property { name: "quadraticAttenuation"; type: "float" }
        Property { name: "localDirection"; type: "QVector3D" }
        Property { name: "cutOffAngle"; type: "float" }
        Signal {
            name: "constantAttenuationChanged"
            Parameter { name: "constantAttenuation"; type: "float" }
        }
        Signal {
            name: "linearAttenuationChanged"
            Parameter { name: "linearAttenuation"; type: "float" }
        }
        Signal {
            name: "quadraticAttenuationChanged"
            Parameter { name: "quadraticAttenuation"; type: "float" }
        }
        Signal {
            name: "localDirectionChanged"
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Signal {
            name: "cutOffAngleChanged"
            Parameter { name: "cutOffAngle"; type: "float" }
        }
        Method {
            name: "setConstantAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setLinearAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setQuadraticAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setLocalDirection"
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Method {
            name: "setCutOffAngle"
            Parameter { name: "cutOffAngle"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QStencilMask"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/StencilMask 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "frontOutputMask"; type: "uint" }
        Property { name: "backOutputMask"; type: "uint" }
        Signal {
            name: "frontOutputMaskChanged"
            Parameter { name: "frontOutputMask"; type: "uint" }
        }
        Signal {
            name: "backOutputMaskChanged"
            Parameter { name: "backOutputMask"; type: "uint" }
        }
        Method {
            name: "setFrontOutputMask"
            Parameter { name: "frontOutputMask"; type: "uint" }
        }
        Method {
            name: "setBackOutputMask"
            Parameter { name: "backOutputMask"; type: "uint" }
        }
    }
    Component {
        name: "Qt3DRender::QStencilOperation"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/StencilOperation 2.0"]
        exportMetaObjectRevisions: [0]
        Property {
            name: "front"
            type: "Qt3DRender::QStencilOperationArguments"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "back"
            type: "Qt3DRender::QStencilOperationArguments"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "Qt3DRender::QStencilOperationArguments"
        prototype: "QObject"
        exports: ["Qt3D.Render/StencilOperationArguments 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "FaceMode"
            values: {
                "Front": 1028,
                "Back": 1029,
                "FrontAndBack": 1032
            }
        }
        Enum {
            name: "Operation"
            values: {
                "Zero": 0,
                "Keep": 7680,
                "Replace": 7681,
                "Increment": 7682,
                "Decrement": 7683,
                "IncrementWrap": 34055,
                "DecrementWrap": 34056,
                "Invert": 5386
            }
        }
        Property { name: "faceMode"; type: "FaceMode"; isReadonly: true }
        Property { name: "stencilTestFailureOperation"; type: "Operation" }
        Property { name: "depthTestFailureOperation"; type: "Operation" }
        Property { name: "allTestsPassOperation"; type: "Operation" }
        Signal {
            name: "stencilTestFailureOperationChanged"
            Parameter { name: "stencilFail"; type: "Operation" }
        }
        Signal {
            name: "depthTestFailureOperationChanged"
            Parameter { name: "depthFail"; type: "Operation" }
        }
        Signal {
            name: "allTestsPassOperationChanged"
            Parameter { name: "stencilDepthPass"; type: "Operation" }
        }
        Signal {
            name: "faceModeChanged"
            Parameter { name: "faceMode"; type: "FaceMode" }
        }
        Method {
            name: "setStencilTestFailureOperation"
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "setDepthTestFailureOperation"
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "setAllTestsPassOperation"
            Parameter { name: "operation"; type: "Operation" }
        }
    }
    Component {
        name: "Qt3DRender::QStencilTest"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/StencilTest 2.0"]
        exportMetaObjectRevisions: [0]
        Property {
            name: "front"
            type: "Qt3DRender::QStencilTestArguments"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "back"
            type: "Qt3DRender::QStencilTestArguments"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "Qt3DRender::QStencilTestArguments"
        prototype: "QObject"
        exports: ["Qt3D.Render/StencilTestArguments 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "StencilFaceMode"
            values: {
                "Front": 1028,
                "Back": 1029,
                "FrontAndBack": 1032
            }
        }
        Enum {
            name: "StencilFunction"
            values: {
                "Never": 512,
                "Always": 519,
                "Less": 513,
                "LessOrEqual": 515,
                "Equal": 514,
                "GreaterOrEqual": 518,
                "Greater": 516,
                "NotEqual": 517
            }
        }
        Property { name: "faceMode"; type: "StencilFaceMode"; isReadonly: true }
        Property { name: "comparisonMask"; type: "uint" }
        Property { name: "referenceValue"; type: "int" }
        Property { name: "stencilFunction"; type: "StencilFunction" }
        Signal {
            name: "comparisonMaskChanged"
            Parameter { name: "comparisonMask"; type: "uint" }
        }
        Signal {
            name: "stencilFunctionChanged"
            Parameter { name: "stencilFunction"; type: "StencilFunction" }
        }
        Signal {
            name: "referenceValueChanged"
            Parameter { name: "referenceValue"; type: "int" }
        }
        Signal {
            name: "faceModeChanged"
            Parameter { name: "faceMode"; type: "StencilFaceMode" }
        }
        Method {
            name: "setComparisonMask"
            Parameter { name: "comparisonMask"; type: "uint" }
        }
        Method {
            name: "setReferenceValue"
            Parameter { name: "referenceValue"; type: "int" }
        }
        Method {
            name: "setStencilFunction"
            Parameter { name: "stencilFunction"; type: "StencilFunction" }
        }
    }
    Component {
        name: "Qt3DRender::QSubtreeEnabler"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/SubtreeEnabler 2.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Enablement"
            values: {
                "Persistent": 0,
                "SingleShot": 1
            }
        }
        Property { name: "enablement"; type: "Enablement" }
        Signal {
            name: "enablementChanged"
            Parameter { name: "enablement"; type: "Qt3DRender::QSubtreeEnabler::Enablement" }
        }
        Method { name: "requestUpdate" }
    }
    Component {
        name: "Qt3DRender::QTechnique"
        defaultProperty: "data"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/Technique 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "graphicsApiFilter"
            type: "Qt3DRender::QGraphicsApiFilter"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "filterKeys"
            revision: 200
            type: "Qt3DRender::QFilterKey"
            isList: true
            isReadonly: true
        }
        Property {
            name: "renderPasses"
            revision: 200
            type: "Qt3DRender::QRenderPass"
            isList: true
            isReadonly: true
        }
        Property {
            name: "parameters"
            revision: 200
            type: "Qt3DRender::QParameter"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTechniqueFilter"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/TechniqueFilter 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "matchAll"
            revision: 200
            type: "Qt3DRender::QFilterKey"
            isList: true
            isReadonly: true
        }
        Property {
            name: "parameters"
            revision: 200
            type: "Qt3DRender::QParameter"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTexture1D"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/Texture1D 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTexture1DArray"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/Texture1DArray 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTexture2D"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/Texture2D 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTexture2DArray"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/Texture2DArray 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTexture2DMultisample"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/Texture2DMultisample 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTexture2DMultisampleArray"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/Texture2DMultisampleArray 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTexture3D"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/Texture3D 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTextureBuffer"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/TextureBuffer 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTextureCubeMap"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/TextureCubeMap 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTextureCubeMapArray"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/TextureCubeMapArray 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTextureImage"
        prototype: "Qt3DRender::QAbstractTextureImage"
        exports: ["Qt3D.Render/TextureImage 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "None": 0,
                "Loading": 1,
                "Ready": 2,
                "Error": 3
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "mirrored"; type: "bool" }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DRender::QTextureLoader"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/TextureLoader 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "source"; type: "QUrl" }
        Property { name: "mirrored"; type: "bool" }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTextureRectangle"
        defaultProperty: "textureImages"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: ["Qt3D.Render/TextureRectangle 2.0"]
        exportMetaObjectRevisions: [200]
        Property {
            name: "textureImages"
            revision: 200
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DRender::QTextureWrapMode"
        prototype: "QObject"
        exports: ["Qt3D.Render/WrapMode 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "WrapMode"
            values: {
                "Repeat": 10497,
                "MirroredRepeat": 33648,
                "ClampToEdge": 33071,
                "ClampToBorder": 33069
            }
        }
        Property { name: "x"; type: "WrapMode" }
        Property { name: "y"; type: "WrapMode" }
        Property { name: "z"; type: "WrapMode" }
        Signal {
            name: "xChanged"
            Parameter { name: "x"; type: "WrapMode" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "y"; type: "WrapMode" }
        }
        Signal {
            name: "zChanged"
            Parameter { name: "z"; type: "WrapMode" }
        }
        Method {
            name: "setX"
            Parameter { name: "x"; type: "WrapMode" }
        }
        Method {
            name: "setY"
            Parameter { name: "y"; type: "WrapMode" }
        }
        Method {
            name: "setZ"
            Parameter { name: "z"; type: "WrapMode" }
        }
    }
    Component {
        name: "Qt3DRender::QViewport"
        defaultProperty: "data"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/Viewport 2.0", "Qt3D.Render/Viewport 2.9"]
        exportMetaObjectRevisions: [200, 9]
        Property { name: "normalizedRect"; type: "QRectF" }
        Property { name: "gamma"; revision: 9; type: "float" }
        Signal {
            name: "normalizedRectChanged"
            Parameter { name: "normalizedRect"; type: "QRectF" }
        }
        Signal {
            name: "gammaChanged"
            Parameter { name: "gamma"; type: "float" }
        }
        Method {
            name: "setNormalizedRect"
            Parameter { name: "normalizedRect"; type: "QRectF" }
        }
        Method {
            name: "setGamma"
            Parameter { name: "gamma"; type: "float" }
        }
    }
    Component {
        name: "Qt3DRender::QWaitFence"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/WaitFence 2.13"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "HandleType"
            values: {
                "NoHandle": 0,
                "OpenGLFenceId": 1
            }
        }
        Property { name: "handleType"; type: "HandleType" }
        Property { name: "handle"; type: "QVariant" }
        Property { name: "waitOnCPU"; type: "bool" }
        Property { name: "timeout"; type: "qulonglong" }
        Signal {
            name: "waitOnCPUChanged"
            Parameter { name: "waitOnCPU"; type: "bool" }
        }
        Signal {
            name: "timeoutChanged"
            Parameter { name: "timeoutChanged"; type: "qulonglong" }
        }
        Signal {
            name: "handleTypeChanged"
            Parameter { name: "handleType"; type: "HandleType" }
        }
        Signal {
            name: "handleChanged"
            Parameter { name: "handle"; type: "QVariant" }
        }
    }
    Component {
        name: "Qt3DRender::Render::Quick::Quick3DBuffer"
        prototype: "Qt3DRender::QBuffer"
        exports: ["Qt3D.Render/Buffer 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "data"; type: "QVariant" }
        Signal { name: "bufferDataChanged" }
        Method {
            name: "updateData"
            Parameter { name: "offset"; type: "int" }
            Parameter { name: "bytes"; type: "QVariant" }
        }
        Method {
            name: "readBinaryFile"
            type: "QVariant"
            Parameter { name: "fileUrl"; type: "QUrl" }
        }
    }
    Component {
        name: "Qt3DRender::Render::Quick::Quick3DParameter"
        prototype: "Qt3DRender::QParameter"
        exports: ["Qt3D.Render/Parameter 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::Render::Quick::Quick3DRayCaster"
        prototype: "Qt3DRender::QRayCaster"
        exports: ["Qt3D.Render/RayCaster 2.11"]
        exportMetaObjectRevisions: [0]
        Property { name: "hits"; type: "QJSValue"; isReadonly: true }
        Property { name: "layers"; type: "Qt3DRender::QLayer"; isList: true; isReadonly: true }
        Signal {
            name: "hitsChanged"
            Parameter { name: "hits"; type: "QJSValue" }
        }
    }
    Component {
        name: "Qt3DRender::Render::Quick::Quick3DScreenRayCaster"
        prototype: "Qt3DRender::QScreenRayCaster"
        exports: ["Qt3D.Render/ScreenRayCaster 2.11"]
        exportMetaObjectRevisions: [0]
        Property { name: "hits"; type: "QJSValue"; isReadonly: true }
        Property { name: "layers"; type: "Qt3DRender::QLayer"; isList: true; isReadonly: true }
        Signal {
            name: "hitsChanged"
            Parameter { name: "hits"; type: "QJSValue" }
        }
    }
    Component {
        name: "Qt3DRender::Render::Quick::Quick3DShaderData"
        prototype: "Qt3DRender::QShaderData"
        exports: ["Qt3D.Render/ShaderData 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DRender::Render::Quick::Quick3DShaderDataArray"
        defaultProperty: "values"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/ShaderDataArray 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "values"; type: "Qt3DRender::QShaderData"; isList: true; isReadonly: true }
    }
}
