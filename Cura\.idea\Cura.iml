<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/build_windows/generators/cura_venv" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.12 virtualenv at C:\Mac\Home\Desktop\CuraProject\Cura\build_windows\generators\cura_venv" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="PLAIN" />
    <option name="myDocStringFormat" value="Plain" />
  </component>
</module>