[{"classes": [{"className": "QQuick3DProfiler", "object": true, "qualifiedClassName": "QQuick3DProfiler", "signals": [{"access": "public", "arguments": [{"name": "data", "type": "QList<QQuick3DProfilerData>"}, {"name": "eventData", "type": "QHash<int,QByteArray>"}], "name": "dataReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlProfilerDefinitions"}]}], "inputFile": "qquick3dprofiler_p.h", "outputRevision": 68}]