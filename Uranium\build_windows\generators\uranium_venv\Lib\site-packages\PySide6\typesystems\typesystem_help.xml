<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.QtHelp">
    <load-typesystem name="typesystem_widgets.xml" generate="no"/>

    <value-type name="QCompressedHelpInfo"/>
    <value-type name="QHelpContentItem">
      <modify-function signature="parent()const">
        <modify-argument index="return">
          <define-ownership owner="default"/>
        </modify-argument>
      </modify-function>
    </value-type>
    <object-type name="QHelpContentModel" polymorphic-id-expression="qobject_cast&lt;QHelpContentModel*&gt;(%1)"/>
    <object-type name="QHelpContentWidget"/>
    <object-type name="QHelpEngine"/>
    <object-type name="QHelpEngineCore"/>
    <value-type name="QHelpFilterData"/>
    <object-type name="QHelpFilterEngine"/>
    <object-type name="QHelpFilterSettingsWidget"/>
    <object-type name="QHelpIndexModel"/>
    <object-type name="QHelpIndexWidget"/>
    <value-type name="QHelpLink"/>
    <object-type name="QHelpSearchEngine"/>
    <value-type name="QHelpSearchQuery">
        <enum-type name="FieldName"/>
    </value-type>
    <object-type name="QHelpSearchQueryWidget"/>
    <object-type name="QHelpSearchResult"/>
    <object-type name="QHelpSearchResultWidget"/>
</typesystem>
