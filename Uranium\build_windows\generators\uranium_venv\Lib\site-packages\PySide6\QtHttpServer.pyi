# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtHttpServer, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtHttpServer`

import PySide6.QtHttpServer
import PySide6.QtCore
import PySide6.QtNetwork

import enum
from typing import Callable, ClassVar, Dict, List, Optional, Tuple, Type, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QAbstractHttpServer(PySide6.QtCore.QObject):

    newWebSocketConnection   : ClassVar[Signal] = ... # newWebSocketConnection()

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def bind(self, server: Optional[PySide6.QtNetwork.QTcpServer] = ...) -> None: ...
    def listen(self, address: Union[PySide6.QtNetwork.QHostAddress, PySide6.QtNetwork.QHostAddress.SpecialAddress] = ..., port: int = ...) -> int: ...
    def serverPorts(self) -> List[int]: ...
    def servers(self) -> List[PySide6.QtNetwork.QTcpServer]: ...
    @overload
    def sslSetup(self, certificate: Union[PySide6.QtNetwork.QSslCertificate, PySide6.QtCore.QIODevice], privateKey: Union[PySide6.QtNetwork.QSslKey, int], protocol: PySide6.QtNetwork.QSsl.SslProtocol = ...) -> None: ...
    @overload
    def sslSetup(self, sslConfiguration: PySide6.QtNetwork.QSslConfiguration) -> None: ...


class QFutureHttpServerResponse(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, QFutureHttpServerResponse: PySide6.QtHttpServer.QFutureHttpServerResponse) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def cancel(self) -> None: ...
    def isCanceled(self) -> bool: ...
    def isFinished(self) -> bool: ...
    def isPaused(self) -> bool: ...
    def isRunning(self) -> bool: ...
    def isStarted(self) -> bool: ...
    def isSuspended(self) -> bool: ...
    def isSuspending(self) -> bool: ...
    def isValid(self) -> bool: ...
    def pause(self) -> None: ...
    def progressMaximum(self) -> int: ...
    def progressMinimum(self) -> int: ...
    def progressText(self) -> str: ...
    def progressValue(self) -> int: ...
    def resultCount(self) -> int: ...
    def resume(self) -> None: ...
    def setPaused(self, paused: bool) -> None: ...
    def setSuspended(self, suspend: bool) -> None: ...
    def suspend(self) -> None: ...
    def togglePaused(self) -> None: ...
    def toggleSuspended(self) -> None: ...
    def waitForFinished(self) -> None: ...


class QHttpServer(PySide6.QtHttpServer.QAbstractHttpServer):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def afterRequest(self, callback: Callable) -> None: ...
    def handleRequest(self, request: PySide6.QtHttpServer.QHttpServerRequest, responder: PySide6.QtHttpServer.QHttpServerResponder) -> bool: ...
    def route(self, rule: str, callback: Callable) -> bool: ...
    def router(self) -> PySide6.QtHttpServer.QHttpServerRouter: ...


class QHttpServerRequest(Shiboken.Object):

    class Method(enum.Flag):

        Unknown                  : QHttpServerRequest.Method = ... # 0x0
        Get                      : QHttpServerRequest.Method = ... # 0x1
        Put                      : QHttpServerRequest.Method = ... # 0x2
        Delete                   : QHttpServerRequest.Method = ... # 0x4
        Post                     : QHttpServerRequest.Method = ... # 0x8
        Head                     : QHttpServerRequest.Method = ... # 0x10
        Options                  : QHttpServerRequest.Method = ... # 0x20
        Patch                    : QHttpServerRequest.Method = ... # 0x40
        Connect                  : QHttpServerRequest.Method = ... # 0x80
        Trace                    : QHttpServerRequest.Method = ... # 0x100
        AnyKnown                 : QHttpServerRequest.Method = ... # 0x1ff


    def body(self) -> PySide6.QtCore.QByteArray: ...
    def headers(self) -> List[Tuple[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]]: ...
    def localAddress(self) -> PySide6.QtNetwork.QHostAddress: ...
    def localPort(self) -> int: ...
    def method(self) -> PySide6.QtHttpServer.QHttpServerRequest.Method: ...
    def query(self) -> PySide6.QtCore.QUrlQuery: ...
    def remoteAddress(self) -> PySide6.QtNetwork.QHostAddress: ...
    def remotePort(self) -> int: ...
    def url(self) -> PySide6.QtCore.QUrl: ...
    def value(self, key: Union[PySide6.QtCore.QByteArray, bytes]) -> PySide6.QtCore.QByteArray: ...


class QHttpServerResponder(Shiboken.Object):

    class StatusCode(enum.Enum):

        Continue                 : QHttpServerResponder.StatusCode = ... # 0x64
        SwitchingProtocols       : QHttpServerResponder.StatusCode = ... # 0x65
        Processing               : QHttpServerResponder.StatusCode = ... # 0x66
        Ok                       : QHttpServerResponder.StatusCode = ... # 0xc8
        Created                  : QHttpServerResponder.StatusCode = ... # 0xc9
        Accepted                 : QHttpServerResponder.StatusCode = ... # 0xca
        NonAuthoritativeInformation: QHttpServerResponder.StatusCode = ... # 0xcb
        NoContent                : QHttpServerResponder.StatusCode = ... # 0xcc
        ResetContent             : QHttpServerResponder.StatusCode = ... # 0xcd
        PartialContent           : QHttpServerResponder.StatusCode = ... # 0xce
        MultiStatus              : QHttpServerResponder.StatusCode = ... # 0xcf
        AlreadyReported          : QHttpServerResponder.StatusCode = ... # 0xd0
        IMUsed                   : QHttpServerResponder.StatusCode = ... # 0xe2
        MultipleChoices          : QHttpServerResponder.StatusCode = ... # 0x12c
        MovedPermanently         : QHttpServerResponder.StatusCode = ... # 0x12d
        Found                    : QHttpServerResponder.StatusCode = ... # 0x12e
        SeeOther                 : QHttpServerResponder.StatusCode = ... # 0x12f
        NotModified              : QHttpServerResponder.StatusCode = ... # 0x130
        UseProxy                 : QHttpServerResponder.StatusCode = ... # 0x131
        TemporaryRedirect        : QHttpServerResponder.StatusCode = ... # 0x133
        PermanentRedirect        : QHttpServerResponder.StatusCode = ... # 0x134
        BadRequest               : QHttpServerResponder.StatusCode = ... # 0x190
        Unauthorized             : QHttpServerResponder.StatusCode = ... # 0x191
        PaymentRequired          : QHttpServerResponder.StatusCode = ... # 0x192
        Forbidden                : QHttpServerResponder.StatusCode = ... # 0x193
        NotFound                 : QHttpServerResponder.StatusCode = ... # 0x194
        MethodNotAllowed         : QHttpServerResponder.StatusCode = ... # 0x195
        NotAcceptable            : QHttpServerResponder.StatusCode = ... # 0x196
        ProxyAuthenticationRequired: QHttpServerResponder.StatusCode = ... # 0x197
        RequestTimeout           : QHttpServerResponder.StatusCode = ... # 0x198
        Conflict                 : QHttpServerResponder.StatusCode = ... # 0x199
        Gone                     : QHttpServerResponder.StatusCode = ... # 0x19a
        LengthRequired           : QHttpServerResponder.StatusCode = ... # 0x19b
        PreconditionFailed       : QHttpServerResponder.StatusCode = ... # 0x19c
        PayloadTooLarge          : QHttpServerResponder.StatusCode = ... # 0x19d
        UriTooLong               : QHttpServerResponder.StatusCode = ... # 0x19e
        UnsupportedMediaType     : QHttpServerResponder.StatusCode = ... # 0x19f
        RequestRangeNotSatisfiable: QHttpServerResponder.StatusCode = ... # 0x1a0
        ExpectationFailed        : QHttpServerResponder.StatusCode = ... # 0x1a1
        ImATeapot                : QHttpServerResponder.StatusCode = ... # 0x1a2
        MisdirectedRequest       : QHttpServerResponder.StatusCode = ... # 0x1a5
        UnprocessableEntity      : QHttpServerResponder.StatusCode = ... # 0x1a6
        Locked                   : QHttpServerResponder.StatusCode = ... # 0x1a7
        FailedDependency         : QHttpServerResponder.StatusCode = ... # 0x1a8
        UpgradeRequired          : QHttpServerResponder.StatusCode = ... # 0x1aa
        PreconditionRequired     : QHttpServerResponder.StatusCode = ... # 0x1ac
        TooManyRequests          : QHttpServerResponder.StatusCode = ... # 0x1ad
        RequestHeaderFieldsTooLarge: QHttpServerResponder.StatusCode = ... # 0x1af
        UnavailableForLegalReasons: QHttpServerResponder.StatusCode = ... # 0x1c3
        InternalServerError      : QHttpServerResponder.StatusCode = ... # 0x1f4
        NotImplemented           : QHttpServerResponder.StatusCode = ... # 0x1f5
        BadGateway               : QHttpServerResponder.StatusCode = ... # 0x1f6
        ServiceUnavailable       : QHttpServerResponder.StatusCode = ... # 0x1f7
        GatewayTimeout           : QHttpServerResponder.StatusCode = ... # 0x1f8
        HttpVersionNotSupported  : QHttpServerResponder.StatusCode = ... # 0x1f9
        VariantAlsoNegotiates    : QHttpServerResponder.StatusCode = ... # 0x1fa
        InsufficientStorage      : QHttpServerResponder.StatusCode = ... # 0x1fb
        LoopDetected             : QHttpServerResponder.StatusCode = ... # 0x1fc
        NotExtended              : QHttpServerResponder.StatusCode = ... # 0x1fe
        NetworkAuthenticationRequired: QHttpServerResponder.StatusCode = ... # 0x1ff
        NetworkConnectTimeoutError: QHttpServerResponder.StatusCode = ... # 0x257


    def sendResponse(self, response: PySide6.QtHttpServer.QHttpServerResponse) -> None: ...
    @overload
    def write(self, data: PySide6.QtCore.QIODevice, mimeType: Union[PySide6.QtCore.QByteArray, bytes], status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def write(self, data: Union[PySide6.QtCore.QByteArray, bytes], mimeType: Union[PySide6.QtCore.QByteArray, bytes], status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def write(self, document: PySide6.QtCore.QJsonDocument, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def write(self, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def writeBody(self, body: bytes) -> None: ...
    @overload
    def writeBody(self, body: bytes, size: int) -> None: ...
    @overload
    def writeBody(self, body: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def writeHeader(self, key: Union[PySide6.QtCore.QByteArray, bytes], value: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def writeStatusLine(self, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...


class QHttpServerResponse(Shiboken.Object):

    @overload
    def __init__(self, data: PySide6.QtCore.QJsonArray, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def __init__(self, data: Dict[str, PySide6.QtCore.QJsonValue], status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def __init__(self, data: str, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def __init__(self, data: bytes, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def __init__(self, data: Union[PySide6.QtCore.QByteArray, bytes], status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def __init__(self, mimeType: Union[PySide6.QtCore.QByteArray, bytes], data: Union[PySide6.QtCore.QByteArray, bytes], status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @overload
    def __init__(self, statusCode: PySide6.QtHttpServer.QHttpServerResponder.StatusCode) -> None: ...

    def addHeader(self, name: Union[PySide6.QtCore.QByteArray, bytes], value: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def clearHeader(self, name: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def clearHeaders(self) -> None: ...
    def data(self) -> PySide6.QtCore.QByteArray: ...
    @staticmethod
    def fromFile(fileName: str) -> PySide6.QtHttpServer.QHttpServerResponse: ...
    @overload
    def hasHeader(self, name: Union[PySide6.QtCore.QByteArray, bytes]) -> bool: ...
    @overload
    def hasHeader(self, name: Union[PySide6.QtCore.QByteArray, bytes], value: Union[PySide6.QtCore.QByteArray, bytes]) -> bool: ...
    def headers(self, name: Union[PySide6.QtCore.QByteArray, bytes]) -> List[PySide6.QtCore.QByteArray]: ...
    def mimeType(self) -> PySide6.QtCore.QByteArray: ...
    def setHeader(self, name: Union[PySide6.QtCore.QByteArray, bytes], value: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def statusCode(self) -> PySide6.QtHttpServer.QHttpServerResponder.StatusCode: ...


class QHttpServerRouter(Shiboken.Object):

    def __init__(self) -> None: ...

    def addConverter(self, metaType: Union[PySide6.QtCore.QMetaType, PySide6.QtCore.QMetaType.Type], regexp: str) -> None: ...
    def clearConverters(self) -> None: ...
    def converters(self) -> Dict[PySide6.QtCore.QMetaType, str]: ...
    def handleRequest(self, request: PySide6.QtHttpServer.QHttpServerRequest, responder: PySide6.QtHttpServer.QHttpServerResponder) -> bool: ...
    def removeConverter(self, metaType: Union[PySide6.QtCore.QMetaType, PySide6.QtCore.QMetaType.Type]) -> None: ...


class QHttpServerRouterRule(Shiboken.Object):
    def exec(self, request: PySide6.QtHttpServer.QHttpServerRequest, responder: PySide6.QtHttpServer.QHttpServerResponder) -> bool: ...
    def hasValidMethods(self) -> bool: ...
    def matches(self, request: PySide6.QtHttpServer.QHttpServerRequest, match: PySide6.QtCore.QRegularExpressionMatch) -> bool: ...


class QIntList(object): ...


# eof
