import os
from pathlib import Path

from conan import ConanFile
from conan.tools.env import VirtualBuildEnv, VirtualRunEnv
from conan.tools.files import copy, mkdir, update_conandata
from conan.tools.microsoft import unix_path
from conan.tools.scm import Version, Git
from conan.errors import ConanInvalidConfiguration

required_conan_version = ">=2.7.0"


class CuraConan(ConanFile):
    name = "cura"
    license = "LGPL-3.0"
    author = "UltiMaker"
    url = "https://github.com/Ultimaker/Cura"
    description = "3D printer / slicing GUI built on top of the Uranium framework"
    topics = ("conan", "python", "pyqt6", "qt", "3d-printing", "slicer")
    exports = "LICENSE*"
    settings = "os", "compiler", "build_type", "arch"
    package_type = "application"

    python_requires = "translationextractor/[>=2.2.0]"
    tool_requires = "gettext/0.22.5"

    options = {
        "devtools": [True, False],
        "i18n_extract": [True, False],
    }
    default_options = {
        "devtools": False,
        "i18n_extract": False,
    }

    def set_version(self):
        if not self.version:
            # 从 resources/conandata.yml 读取版本
            conandata_path = Path(self.recipe_folder) / "resources" / "conandata.yml"
            if conandata_path.exists():
                try:
                    with open(conandata_path, 'r') as f:
                        content = f.read()
                        for line in content.split('\n'):
                            if line.strip().startswith('version:'):
                                version_part = line.split(':', 1)[1].strip()
                                self.version = version_part.strip('"\'')
                                break
                except:
                    pass
            
            if not self.version:
                self.version = "5.11.0-alpha.0"

    @property
    def _base_dir(self):
        # Add system suffix to venv folder for cross-platform development
        system_suffix = "windows" if self.settings.os == "Windows" else "macos" if self.settings.os == "Macos" else "linux"
        if self.install_folder is None:
            if self.build_folder is not None:
                return Path(self.build_folder)
            return Path(os.getcwd(), f"venv_{system_suffix}")
        if self.in_local_cache:
            return Path(self.install_folder)
        else:
            return Path(self.source_folder, f"venv_{system_suffix}")

    @property
    def _share_dir(self):
        return self._base_dir.joinpath("share")

    @property
    def _script_dir(self):
        if self.settings.os == "Windows":
            return self._base_dir.joinpath("Scripts")
        return self._base_dir.joinpath("bin")

    @property
    def _site_packages(self):
        if self.settings.os == "Windows":
            return self._base_dir.joinpath("Lib", "site-packages")
        py_version = Version(self.deps_cpp_info["cpython"].version)
        return self._base_dir.joinpath("lib", f"python{py_version.major}.{py_version.minor}", "site-packages")

    @property
    def _py_interp(self):
        py_interp = self._script_dir.joinpath(Path(self.deps_user_info["cpython"].python).name)
        if self.settings.os == "Windows":
            py_interp = Path(*[f'"{p}"' if " " in p else p for p in py_interp.parts])
        return py_interp

    def export(self):
        git = Git(self)
        update_conandata(self, {"version": self.version, "commit": git.get_commit()})

    def export_sources(self):
        copy(self, "*", os.path.join(self.recipe_folder, "cura"),
             os.path.join(self.export_sources_folder, "cura"))
        copy(self, "*", os.path.join(self.recipe_folder, "plugins"),
             os.path.join(self.export_sources_folder, "plugins"), excludes="*.pyc")
        copy(self, "*", os.path.join(self.recipe_folder, "resources"),
             os.path.join(self.export_sources_folder, "resources"), excludes="*.mo")
        copy(self, "*", os.path.join(self.recipe_folder, "tests"), 
             os.path.join(self.export_sources_folder, "tests"))
        copy(self, "cura_app.py", self.recipe_folder, self.export_sources_folder)

    def validate(self):
        if self.options.i18n_extract and self.settings.os == "Windows" and not self.conf.get("tools.microsoft.bash:path", check_type=str):
            raise ConanInvalidConfiguration("Unable to extract translations on Windows without Bash installed")

    def configure(self):
        # Configure dependencies
        self.options["uranium"].shared = True
        self.options["cpython"].shared = True
        if self.settings.os == "Linux":
            self.options["openssl"].shared = True

    def requirements(self):
        # Core dependencies
        self.requires("uranium/[>=5.11.0-alpha.0]")
        self.requires("cpython/3.12.2")
        
        # Exclude CuraEngine as requested
        # self.requires("curaengine/[>=5.11.0-alpha.0]")  # Commented out to exclude CuraEngine

    def generate(self):
        vr = VirtualRunEnv(self)
        vr.generate()

        if self.options.i18n_extract:
            vb = VirtualBuildEnv(self)
            vb.generate()

            pot = self.python_requires["translationextractor"].module.ExtractTranslations(self)
            pot.generate()

    def build(self):
        # Build translations if needed
        for po_file in Path(self.source_folder, "resources", "i18n").glob("**/*.po"):
            mo_file = Path(self.build_folder, po_file.with_suffix('.mo').relative_to(self.source_folder))
            mo_file = mo_file.parent.joinpath("LC_MESSAGES", mo_file.name)
            mkdir(self, str(unix_path(self, Path(mo_file).parent)))
            self.run(f"msgfmt {po_file} -o {mo_file} -f", env="conanbuild")

    def layout(self):
        self.folders.source = "."
        # Use system suffix for build folder to avoid cross-platform conflicts
        system_suffix = "windows" if self.settings.os == "Windows" else "macos" if self.settings.os == "Macos" else "linux"
        self.folders.build = f"build_{system_suffix}"
        self.folders.generators = os.path.join(self.folders.build, "generators")

        self.cpp.package.libdirs = [os.path.join("site-packages", "cura")]
        self.cpp.package.resdirs = ["resources", "plugins"]

        self.layouts.source.runenv_info.prepend_path("PYTHONPATH", ".")
        self.layouts.source.runenv_info.prepend_path("PYTHONPATH", "plugins")
        self.layouts.package.runenv_info.prepend_path("PYTHONPATH", "site-packages")
        self.layouts.package.runenv_info.prepend_path("PYTHONPATH", self.cpp.package.resdirs[1])

    def package(self):
        copy(self, "*", src=os.path.join(self.source_folder, "cura"),
             dst=os.path.join(self.package_folder, self.cpp.package.libdirs[0]))
        copy(self, "*", src=os.path.join(self.source_folder, "resources"),
             dst=os.path.join(self.package_folder, self.cpp.package.resdirs[0]))
        copy(self, "*.mo", src=os.path.join(self.build_folder, "resources"),
             dst=os.path.join(self.package_folder, self.cpp.package.resdirs[0]))
        copy(self, "*", src=os.path.join(self.source_folder, "plugins"),
             dst=os.path.join(self.package_folder, self.cpp.package.resdirs[1]))
        copy(self, "cura_app.py", src=self.source_folder, dst=self.package_folder)

    def package_id(self):
        self.info.clear()
        self.info.options.rm_safe("devtools")
        self.info.options.rm_safe("i18n_extract")
