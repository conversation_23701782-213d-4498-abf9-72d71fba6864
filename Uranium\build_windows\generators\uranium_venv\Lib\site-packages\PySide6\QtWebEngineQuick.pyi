# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtWebEngineQuick, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtWebEngineQuick`

import PySide6.QtWebEngineQuick
import PySide6.QtCore

import enum
from typing import ClassVar, List, Optional, Sequence, Union
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QIntList(object): ...


class QQuickWebEngineProfile(PySide6.QtCore.QObject):

    cachePathChanged         : ClassVar[Signal] = ... # cachePathChanged()
    downloadFinished         : ClassVar[Signal] = ... # downloadFinished(QQuickWebEngineDownloadRequest*)
    downloadPathChanged      : ClassVar[Signal] = ... # downloadPathChanged()
    downloadRequested        : ClassVar[Signal] = ... # downloadRequested(QQuickWebEngineDownloadRequest*)
    httpAcceptLanguageChanged: ClassVar[Signal] = ... # httpAcceptLanguageChanged()
    httpCacheMaximumSizeChanged: ClassVar[Signal] = ... # httpCacheMaximumSizeChanged()
    httpCacheTypeChanged     : ClassVar[Signal] = ... # httpCacheTypeChanged()
    httpUserAgentChanged     : ClassVar[Signal] = ... # httpUserAgentChanged()
    offTheRecordChanged      : ClassVar[Signal] = ... # offTheRecordChanged()
    persistentCookiesPolicyChanged: ClassVar[Signal] = ... # persistentCookiesPolicyChanged()
    persistentStoragePathChanged: ClassVar[Signal] = ... # persistentStoragePathChanged()
    presentNotification      : ClassVar[Signal] = ... # presentNotification(QWebEngineNotification*)
    pushServiceEnabledChanged: ClassVar[Signal] = ... # pushServiceEnabledChanged()
    spellCheckEnabledChanged : ClassVar[Signal] = ... # spellCheckEnabledChanged()
    spellCheckLanguagesChanged: ClassVar[Signal] = ... # spellCheckLanguagesChanged()
    storageNameChanged       : ClassVar[Signal] = ... # storageNameChanged()

    class HttpCacheType(enum.Enum):

        MemoryHttpCache          : QQuickWebEngineProfile.HttpCacheType = ... # 0x0
        DiskHttpCache            : QQuickWebEngineProfile.HttpCacheType = ... # 0x1
        NoCache                  : QQuickWebEngineProfile.HttpCacheType = ... # 0x2

    class PersistentCookiesPolicy(enum.Enum):

        NoPersistentCookies      : QQuickWebEngineProfile.PersistentCookiesPolicy = ... # 0x0
        AllowPersistentCookies   : QQuickWebEngineProfile.PersistentCookiesPolicy = ... # 0x1
        ForcePersistentCookies   : QQuickWebEngineProfile.PersistentCookiesPolicy = ... # 0x2


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def cachePath(self) -> str: ...
    def clearHttpCache(self) -> None: ...
    @staticmethod
    def defaultProfile() -> PySide6.QtWebEngineQuick.QQuickWebEngineProfile: ...
    def downloadPath(self) -> str: ...
    def httpAcceptLanguage(self) -> str: ...
    def httpCacheMaximumSize(self) -> int: ...
    def httpCacheType(self) -> PySide6.QtWebEngineQuick.QQuickWebEngineProfile.HttpCacheType: ...
    def httpUserAgent(self) -> str: ...
    def isOffTheRecord(self) -> bool: ...
    def isPushServiceEnabled(self) -> bool: ...
    def isSpellCheckEnabled(self) -> bool: ...
    def persistentCookiesPolicy(self) -> PySide6.QtWebEngineQuick.QQuickWebEngineProfile.PersistentCookiesPolicy: ...
    def persistentStoragePath(self) -> str: ...
    def removeAllUrlSchemeHandlers(self) -> None: ...
    def removeUrlScheme(self, scheme: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
    def setCachePath(self, path: str) -> None: ...
    def setDownloadPath(self, path: str) -> None: ...
    def setHttpAcceptLanguage(self, httpAcceptLanguage: str) -> None: ...
    def setHttpCacheMaximumSize(self, maxSize: int) -> None: ...
    def setHttpCacheType(self, arg__1: PySide6.QtWebEngineQuick.QQuickWebEngineProfile.HttpCacheType) -> None: ...
    def setHttpUserAgent(self, userAgent: str) -> None: ...
    def setOffTheRecord(self, offTheRecord: bool) -> None: ...
    def setPersistentCookiesPolicy(self, arg__1: PySide6.QtWebEngineQuick.QQuickWebEngineProfile.PersistentCookiesPolicy) -> None: ...
    def setPersistentStoragePath(self, path: str) -> None: ...
    def setPushServiceEnabled(self, enable: bool) -> None: ...
    def setSpellCheckEnabled(self, enabled: bool) -> None: ...
    def setSpellCheckLanguages(self, languages: Sequence[str]) -> None: ...
    def setStorageName(self, name: str) -> None: ...
    def spellCheckLanguages(self) -> List[str]: ...
    def storageName(self) -> str: ...


class QtWebEngineQuick(Shiboken.Object):
    @staticmethod
    def initialize() -> None: ...


# eof
