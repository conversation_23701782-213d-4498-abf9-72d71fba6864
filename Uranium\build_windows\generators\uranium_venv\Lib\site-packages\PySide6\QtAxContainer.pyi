# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtAxContainer, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtAxContainer`

import PySide6.QtAxContainer
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets

import enum
from typing import Any, ClassVar, Dict, List, Optional, Sequence, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QAxBase(Shiboken.Object):

    def __init__(self) -> None: ...

    def __lshift__(self, s: PySide6.QtCore.QDataStream) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, s: PySide6.QtCore.QDataStream) -> PySide6.QtCore.QDataStream: ...
    @staticmethod
    def argumentsToList(var1: Any, var2: Any, var3: Any, var4: Any, var5: Any, var6: Any, var7: Any, var8: Any) -> List[Any]: ...
    def asVariant(self) -> Any: ...
    def axBaseMetaObject(self) -> PySide6.QtCore.QMetaObject: ...
    def classContext(self) -> int: ...
    def className(self) -> bytes: ...
    def clear(self) -> None: ...
    def control(self) -> str: ...
    def disableClassInfo(self) -> None: ...
    def disableEventSink(self) -> None: ...
    def disableMetaObject(self) -> None: ...
    @overload
    def dynamicCall(self, name: bytes, v1: Any = ..., v2: Any = ..., v3: Any = ..., v4: Any = ..., v5: Any = ..., v6: Any = ..., v7: Any = ..., v8: Any = ...) -> Any: ...
    @overload
    def dynamicCall(self, name: bytes, vars: Sequence[Any]) -> Any: ...
    def generateDocumentation(self) -> str: ...
    def indexOfVerb(self, verb: str) -> int: ...
    def initializeFrom(self, that: PySide6.QtAxContainer.QAxBase) -> None: ...
    def internalRelease(self) -> None: ...
    def isNull(self) -> bool: ...
    def propertyBag(self) -> Dict[str, Any]: ...
    def propertyWritable(self, arg__1: bytes) -> bool: ...
    def qObject(self) -> PySide6.QtCore.QObject: ...
    @overload
    def querySubObject(self, name: bytes, v1: Any = ..., v2: Any = ..., v3: Any = ..., v4: Any = ..., v5: Any = ..., v6: Any = ..., v7: Any = ..., v8: Any = ...) -> PySide6.QtAxContainer.QAxObject: ...
    @overload
    def querySubObject(self, name: bytes, vars: Sequence[Any]) -> PySide6.QtAxContainer.QAxObject: ...
    def setClassContext(self, classContext: int) -> None: ...
    def setControl(self, arg__1: str) -> bool: ...
    def setPropertyBag(self, arg__1: Dict[str, Any]) -> None: ...
    def setPropertyWritable(self, arg__1: bytes, arg__2: bool) -> None: ...
    def verbs(self) -> List[str]: ...


class QAxBaseObject(PySide6.QtCore.QObject, PySide6.QtAxContainer.QAxObjectInterface):

    exception                : ClassVar[Signal] = ... # exception(int,QString,QString,QString)
    propertyChanged          : ClassVar[Signal] = ... # propertyChanged(QString)
    signal                   : ClassVar[Signal] = ... # signal(QString,int,void*)


class QAxBaseWidget(PySide6.QtWidgets.QWidget, PySide6.QtAxContainer.QAxObjectInterface):

    exception                : ClassVar[Signal] = ... # exception(int,QString,QString,QString)
    propertyChanged          : ClassVar[Signal] = ... # propertyChanged(QString)
    signal                   : ClassVar[Signal] = ... # signal(QString,int,void*)


class QAxObject(PySide6.QtAxContainer.QAxBaseObject, PySide6.QtAxContainer.QAxBase):

    exception                : ClassVar[Signal] = ... # exception(int,QString,QString,QString)
    propertyChanged          : ClassVar[Signal] = ... # propertyChanged(QString)
    signal                   : ClassVar[Signal] = ... # signal(QString,int,void*)

    @overload
    def __init__(self, c: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def classContext(self) -> int: ...
    def clear(self) -> None: ...
    def control(self) -> str: ...
    def doVerb(self, verb: str) -> bool: ...
    def resetControl(self) -> None: ...
    def setClassContext(self, classContext: int) -> None: ...
    def setControl(self, c: str) -> bool: ...


class QAxObjectInterface(Shiboken.Object):

    def __init__(self) -> None: ...

    def classContext(self) -> int: ...
    def control(self) -> str: ...
    def resetControl(self) -> None: ...
    def setClassContext(self, classContext: int) -> None: ...
    def setControl(self, c: str) -> bool: ...


class QAxScript(PySide6.QtCore.QObject):

    entered                  : ClassVar[Signal] = ... # entered()
    error                    : ClassVar[Signal] = ... # error(int,QString,int,QString)
    finished                 : ClassVar[Signal] = ... # finished()
    stateChanged             : ClassVar[Signal] = ... # stateChanged(int)

    class FunctionFlags(enum.Enum):

        FunctionNames            : QAxScript.FunctionFlags = ... # 0x0
        FunctionSignatures       : QAxScript.FunctionFlags = ... # 0x1


    def __init__(self, name: str, manager: PySide6.QtAxContainer.QAxScriptManager) -> None: ...

    @overload
    def call(self, function: str, arguments: Sequence[Any]) -> Any: ...
    @overload
    def call(self, function: str, v1: Any = ..., v2: Any = ..., v3: Any = ..., v4: Any = ..., v5: Any = ..., v6: Any = ..., v7: Any = ..., v8: Any = ...) -> Any: ...
    def functions(self, arg__1: PySide6.QtAxContainer.QAxScript.FunctionFlags = ...) -> List[str]: ...
    def load(self, code: str, language: str = ...) -> bool: ...
    def scriptCode(self) -> str: ...
    def scriptEngine(self) -> PySide6.QtAxContainer.QAxScriptEngine: ...
    def scriptName(self) -> str: ...


class QAxScriptEngine(PySide6.QtAxContainer.QAxObject):

    class State(enum.Enum):

        Uninitialized            : QAxScriptEngine.State = ... # 0x0
        Started                  : QAxScriptEngine.State = ... # 0x1
        Connected                : QAxScriptEngine.State = ... # 0x2
        Disconnected             : QAxScriptEngine.State = ... # 0x3
        Closed                   : QAxScriptEngine.State = ... # 0x4
        Initialized              : QAxScriptEngine.State = ... # 0x5


    def __init__(self, language: str, script: PySide6.QtAxContainer.QAxScript) -> None: ...

    def addItem(self, name: str) -> None: ...
    def hasIntrospection(self) -> bool: ...
    def isValid(self) -> bool: ...
    def scriptLanguage(self) -> str: ...
    def setState(self, st: PySide6.QtAxContainer.QAxScriptEngine.State) -> None: ...
    def state(self) -> PySide6.QtAxContainer.QAxScriptEngine.State: ...


class QAxScriptManager(PySide6.QtCore.QObject):

    error                    : ClassVar[Signal] = ... # error(QAxScript*,int,QString,int,QString)

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def addObject(self, object: PySide6.QtAxContainer.QAxBase) -> None: ...
    @overload
    def call(self, function: str, arguments: Sequence[Any]) -> Any: ...
    @overload
    def call(self, function: str, v1: Any = ..., v2: Any = ..., v3: Any = ..., v4: Any = ..., v5: Any = ..., v6: Any = ..., v7: Any = ..., v8: Any = ...) -> Any: ...
    def functions(self, arg__1: PySide6.QtAxContainer.QAxScript.FunctionFlags = ...) -> List[str]: ...
    @overload
    def load(self, code: str, name: str, language: str) -> PySide6.QtAxContainer.QAxScript: ...
    @overload
    def load(self, file: str, name: str) -> PySide6.QtAxContainer.QAxScript: ...
    @staticmethod
    def registerEngine(name: str, extension: str, code: str = ...) -> bool: ...
    def script(self, name: str) -> PySide6.QtAxContainer.QAxScript: ...
    @staticmethod
    def scriptFileFilter() -> str: ...
    def scriptNames(self) -> List[str]: ...


class QAxSelect(PySide6.QtWidgets.QDialog):

    class SandboxingLevel(enum.Enum):

        SandboxingNone           : QAxSelect.SandboxingLevel = ... # 0x0
        SandboxingProcess        : QAxSelect.SandboxingLevel = ... # 0x1
        SandboxingLowIntegrity   : QAxSelect.SandboxingLevel = ... # 0x2
        SandboxingAppContainer   : QAxSelect.SandboxingLevel = ... # 0x3


    def __init__(self, parent: Optional[PySide6.QtWidgets.QWidget] = ..., flags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def clsid(self) -> str: ...
    def sandboxingLevel(self) -> PySide6.QtAxContainer.QAxSelect.SandboxingLevel: ...


class QAxWidget(PySide6.QtAxContainer.QAxBaseWidget, PySide6.QtAxContainer.QAxBase):

    exception                : ClassVar[Signal] = ... # exception(int,QString,QString,QString)
    propertyChanged          : ClassVar[Signal] = ... # propertyChanged(QString)
    signal                   : ClassVar[Signal] = ... # signal(QString,int,void*)

    @overload
    def __init__(self, c: str, parent: Optional[PySide6.QtWidgets.QWidget] = ..., f: PySide6.QtCore.Qt.WindowType = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtWidgets.QWidget] = ..., f: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def changeEvent(self, e: PySide6.QtCore.QEvent) -> None: ...
    def classContext(self) -> int: ...
    def clear(self) -> None: ...
    def control(self) -> str: ...
    @overload
    def createHostWindow(self, arg__1: bool) -> bool: ...
    @overload
    def createHostWindow(self, arg__1: bool, arg__2: Union[PySide6.QtCore.QByteArray, bytes]) -> bool: ...
    def doVerb(self, verb: str) -> bool: ...
    def minimumSizeHint(self) -> PySide6.QtCore.QSize: ...
    def resetControl(self) -> None: ...
    def resizeEvent(self, arg__1: PySide6.QtGui.QResizeEvent) -> None: ...
    def setClassContext(self, classContext: int) -> None: ...
    def setControl(self, arg__1: str) -> bool: ...
    def sizeHint(self) -> PySide6.QtCore.QSize: ...
    def translateKeyEvent(self, message: int, keycode: int) -> bool: ...


class QIntList(object): ...


# eof
