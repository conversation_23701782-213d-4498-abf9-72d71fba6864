<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem>
    <template name="callArrayFunction">
        Py_ssize_t _size = PySequence_Size(%PYARG_2);
        if (_size) {
            $ATTR_TYPE *_list = new $ATTR_TYPE[_size];
            if (_size) {
              Shiboken::AutoDecRef fast(PySequence_Fast(%PYARG_2,
                "Failed to parse sequence with type %VECTOR_TYPE."));
              for(Py_ssize_t i=0; i &lt; _size; i++) {
                  PyObject* pv = PySequence_Fast_GET_ITEM(fast.object(), i);
                  _list[i] = %CONVERTTOCPP[$ATTR_TYPE](pv);
              }
            }
            %CPPSELF.%FUNCTION_NAME(%1, _list, $ARG0);
            delete[] _list;
         } else {
            %CPPSELF.%FUNCTION_NAME(%1, reinterpret_cast&lt;$ATTR_TYPE*&gt;(nullptr), $ARG1);
         }
    </template>
    <template name="glGetString_return_QString">
        const GLubyte *us = %CPPSELF.%FUNCTION_NAME(%ARGUMENT_NAMES);
        const QString s = QString::fromLocal8Bit(reinterpret_cast&lt;const char *&gt;(us));
        %PYARG_0 = %CONVERTTOPYTHON[QString](s);
    </template>
</typesystem>
