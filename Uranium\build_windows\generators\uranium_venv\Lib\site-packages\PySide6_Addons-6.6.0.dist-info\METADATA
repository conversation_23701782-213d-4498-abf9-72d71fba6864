Metadata-Version: 2.1
Name: PySide6-Addons
Version: 6.6.0
Summary: Python bindings for the Qt cross-platform application and UI framework (Addons)
Home-page: https://www.pyside.org
Download-URL: https://download.qt.io/official_releases/QtForPython
Author: Qt for Python Team
Author-email: <EMAIL>
License: LGPL
Keywords: Qt
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Environment :: X11 Applications :: Qt
Classifier: Environment :: Win32 (MS Windows)
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Library or Lesser General Public License (LGPL)
Classifier: License :: Other/Proprietary License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: User Interfaces
Classifier: Topic :: Software Development :: Widget Sets
Requires-Python: <3.13,>=3.8
Description-Content-Type: text/markdown
License-File: LicenseRef-Qt-Commercial.txt
Requires-Dist: shiboken6 (==6.6.0)
Requires-Dist: PySide6-Essentials (==6.6.0)

# PySide6 Addons

PySide6 is the official Python module from the
[Qt for Python project](https://wiki.qt.io/Qt_for_Python),
which provides access to the complete Qt 6.0+ framework.

The Qt for Python project is developed in the open, with all facilities you'd expect
from any modern OSS project such as all code in a git repository and an open
design process. We welcome any contribution conforming to the
[Qt Contribution Agreement](https://www.qt.io/contributionagreement/).

This is a complementary wheel for [PySide6](https://pypi.org/project/PySide6),
it includes the following Qt modules:

* Qt3DAnimation
* Qt3DCore
* Qt3DExtras
* Qt3DInput
* Qt3DLogic
* Qt3DRender
* QtAxContainer
* QtBluetooth
* QtCharts
* QtDataVisualization
* QtMultimedia
* QtMultimediaWidgets
* QtNetworkAuth
* QtNfc
* QtPositioning
* QtQuick3D
* QtRemoteObjects
* QtScxml
* QtSensors
* QtSerialPort
* QtStateMachine
* QtVirtualKeyboard
* QtWebChannel
* QtWebEngineCore
* QtWebEngineQuick
* QtWebEngineWidgets
* QtWebSockets
* QtPdf
* QtPdfWidgets
* QtHttpServer

### Documentation and Bugs

You can find more information about the PySide6 module API in the
[official Qt for Python documentation](https://doc.qt.io/qtforpython/).

If you come across any issue, please file a bug report at our
[JIRA tracker](https://bugreports.qt.io/projects/PYSIDE) following
our [guidelines](https://wiki.qt.io/Qt_for_Python/Reporting_Bugs).

### Community

Check our channels on IRC (Libera), Telegram, Gitter, Matrix, and mailing list,
and [join our community](https://wiki.qt.io/Qt_for_Python#Community)!

### Licensing

PySide6 is available under both Open Source (LGPLv3/GPLv2) and commercial
license.  Using PyPi is the recommended installation source, because the
content of the wheels is valid for both cases.  For more information, refer to
the [Qt Licensing page](https://www.qt.io/licensing/).
