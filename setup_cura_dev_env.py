#!/usr/bin/env python3
"""
Cura & Uranium 并行开发环境自动化设置脚本
支持 macOS 和 Windows 跨平台开发，自动添加系统后缀避免冲突
排除 CuraEngine 构建，严格按照官方文档依赖版本
"""

import os
import sys
import platform
import subprocess
import shutil
from pathlib import Path
import json


class CuraDevEnvSetup:
    def __init__(self):
        self.system = platform.system().lower()
        self.system_suffix = self._get_system_suffix()
        self.project_root = Path.cwd()
        self.cura_path = self.project_root / "Cura"
        self.uranium_path = self.project_root / "Uranium"
        self.cura_engine_path = self.project_root / "CuraEngine"
        
        # 检查必要的目录是否存在
        self._validate_project_structure()
        
    def _get_system_suffix(self):
        """获取系统后缀"""
        if self.system == "windows":
            return "windows"
        elif self.system == "darwin":
            return "macos"
        else:
            return "linux"
    
    def _validate_project_structure(self):
        """验证项目结构"""
        if not self.cura_path.exists():
            raise FileNotFoundError(f"Cura 目录不存在: {self.cura_path}")
        if not self.uranium_path.exists():
            raise FileNotFoundError(f"Uranium 目录不存在: {self.uranium_path}")
        
        print(f"✓ 项目结构验证通过")
        print(f"  - Cura: {self.cura_path}")
        print(f"  - Uranium: {self.uranium_path}")
        print(f"  - 系统后缀: {self.system_suffix}")
    
    def _run_command(self, cmd, cwd=None, check=True):
        """执行命令"""
        if cwd is None:
            cwd = self.project_root
        
        print(f"\n执行命令: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
        print(f"工作目录: {cwd}")
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=cwd, 
                check=check, 
                capture_output=True, 
                text=True,
                shell=True if isinstance(cmd, str) else False
            )
            if result.stdout:
                print(f"输出: {result.stdout}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            if e.stdout:
                print(f"标准输出: {e.stdout}")
            if e.stderr:
                print(f"错误输出: {e.stderr}")
            if check:
                raise
            return e
    
    def check_dependencies(self):
        """检查系统依赖"""
        print("\n🔍 检查系统依赖...")
        
        dependencies = {
            "python": ["python", "--version"],
            "conan": ["conan", "--version"],
            "cmake": ["cmake", "--version"],
            "ninja": ["ninja", "--version"] if self.system != "windows" else ["ninja.exe", "--version"]
        }
        
        missing_deps = []
        for name, cmd in dependencies.items():
            try:
                result = self._run_command(cmd, check=False)
                if result.returncode == 0:
                    print(f"✓ {name}: 已安装")
                else:
                    missing_deps.append(name)
            except FileNotFoundError:
                missing_deps.append(name)
        
        if missing_deps:
            print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
            print("请参考官方文档安装缺少的依赖")
            return False
        
        return True
    
    def setup_conan_config(self):
        """配置 Conan"""
        print("\n⚙️ 配置 Conan...")

        # 检查 Conan 版本
        result = self._run_command(["conan", "--version"])
        version_line = result.stdout.strip()
        print(f"Conan 版本: {version_line}")

        # 配置 Conan
        print("配置 Conan 远程仓库...")
        self._run_command([
            "conan", "config", "install",
            "https://github.com/ultimaker/conan-config.git"
        ])

        # 检测 profile
        print("检测 Conan profile...")
        self._run_command(["conan", "profile", "detect", "--force"])

        # 检查并修复架构问题
        self._fix_conan_profile()

        print("✓ Conan 配置完成")

    def _fix_conan_profile(self):
        """修复 Conan profile 架构问题"""
        print("检查 Conan profile 架构设置...")

        # 获取默认 profile
        result = self._run_command(["conan", "profile", "show", "default"], check=False)
        if result.returncode != 0:
            print("警告: 无法获取默认 profile")
            return

        profile_content = result.stdout

        # 检查是否是 ARM64 架构
        if "arch=armv8" in profile_content:
            print("⚠️  检测到 ARM64 架构，某些依赖包不支持 ARM64")
            print("将架构设置为 x86_64 以确保兼容性...")

            # 设置架构为 x86_64
            self._run_command([
                "conan", "profile", "update",
                "settings.arch=x86_64", "default"
            ])

            print("✓ 架构已设置为 x86_64")
        else:
            print("✓ 架构设置正常")
    
    def setup_uranium_dev_env(self):
        """设置 Uranium 开发环境"""
        print(f"\n🔧 设置 Uranium 开发环境 (系统后缀: {self.system_suffix})...")

        # 构建 Uranium，排除 CuraEngine
        conan_cmd = [
            "conan", "install", ".",
            "--build=missing",
            "--update",
            "-g", "VirtualPythonEnv",
            "-g", "PyCharmRunEnv",
            "-c", "user.generator.virtual_python_env:dev_tools=True",
            "-pr", str(self.project_root / "conan_profile_x64")  # 使用自定义profile
        ]

        print("执行 Uranium conan install...")
        self._run_command(conan_cmd, cwd=self.uranium_path)

        print("✓ Uranium 开发环境设置完成")
    
    def setup_uranium_editable(self):
        """设置 Uranium 为可编辑模式"""
        print("\n📝 设置 Uranium 为可编辑模式...")

        # 从 conandata.yml 读取版本
        conandata_path = self.uranium_path / "conandata.yml"
        version = '5.11.0-alpha.0'  # 默认版本

        if conandata_path.exists():
            try:
                with open(conandata_path, 'r') as f:
                    content = f.read()
                    # 简单的版本提取，查找 version: 行
                    for line in content.split('\n'):
                        if line.strip().startswith('version:'):
                            version_part = line.split(':', 1)[1].strip()
                            # 移除引号
                            version = version_part.strip('"\'')
                            break
            except Exception as e:
                print(f"警告: 无法读取版本信息，使用默认版本 {version}: {e}")

        print(f"使用 Uranium 版本: {version}")

        # 设置为可编辑模式
        editable_cmd = [
            "conan", "editable", "add", ".",
            f"uranium/{version}@dev/local",
            "-pr", str(self.project_root / "conan_profile_x64")
        ]

        self._run_command(editable_cmd, cwd=self.uranium_path)
        print(f"✓ Uranium 设置为可编辑模式: uranium/{version}@dev/local")

        return version

    def setup_cura_dev_env(self, uranium_version):
        """设置 Cura 开发环境"""
        print(f"\n🎯 设置 Cura 开发环境 (系统后缀: {self.system_suffix})...")

        # 检查是否存在 conanfile.py
        if not (self.cura_path / "conanfile.py").exists():
            print("Cura 没有 conanfile.py，将通过 Uranium 依赖来设置环境...")
            # 我们需要在 Uranium 环境中运行 Cura
            return self._setup_cura_via_uranium(uranium_version)

        # 如果有 conanfile.py，则直接构建
        conan_cmd = [
            "conan", "install", ".",
            "--build=missing",
            "--update",
            "-g", "VirtualPythonEnv",
            "-g", "PyCharmRunEnv",
            "-c", "user.generator.virtual_python_env:dev_tools=True",
            f"--require-override=uranium/{uranium_version}@dev/local",
            "-pr", str(self.project_root / "conan_profile_x64")
        ]

        self._run_command(conan_cmd, cwd=self.cura_path)
        print("✓ Cura 开发环境设置完成")

    def _setup_cura_via_uranium(self, uranium_version):
        """通过 Uranium 环境设置 Cura"""
        print(f"通过 Uranium 环境设置 Cura (版本: {uranium_version})...")

        # 检查虚拟环境是否存在
        venv_path = self.uranium_path / f"build_{self.system_suffix}" / "generators"
        if not venv_path.exists():
            print("Uranium 虚拟环境不存在，请先运行 Uranium 设置")
            return False

        print("✓ Cura 将使用 Uranium 的开发环境")
        return True

    def create_pycharm_config(self):
        """创建 PyCharm 配置"""
        print("\n🔧 创建 PyCharm 配置...")

        # 创建 .idea 目录（如果不存在）
        idea_dir = self.project_root / ".idea"
        idea_dir.mkdir(exist_ok=True)

        # 创建 modules.xml
        modules_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/Cura/Cura.iml" filepath="$PROJECT_DIR$/Cura/Cura.iml" />
      <module fileurl="file://$PROJECT_DIR$/Uranium/Uranium.iml" filepath="$PROJECT_DIR$/Uranium/Uranium.iml" />
    </modules>
  </component>
</project>"""

        with open(idea_dir / "modules.xml", "w") as f:
            f.write(modules_xml)

        # 创建 Cura.iml
        cura_iml_dir = self.cura_path
        cura_iml = f"""<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/plugins" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/build_{self.system_suffix}" />
      <excludeFolder url="file://$MODULE_DIR$/venv_{self.system_suffix}" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.12 (Cura)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>"""

        with open(cura_iml_dir / "Cura.iml", "w") as f:
            f.write(cura_iml)

        # 创建 Uranium.iml
        uranium_iml_dir = self.uranium_path
        uranium_iml = f"""<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/plugins" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/build_{self.system_suffix}" />
      <excludeFolder url="file://$MODULE_DIR$/venv_{self.system_suffix}" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.12 (Uranium)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>"""

        with open(uranium_iml_dir / "Uranium.iml", "w") as f:
            f.write(uranium_iml)

        print("✓ PyCharm 配置文件创建完成")

    def create_run_scripts(self):
        """创建运行脚本"""
        print("\n📜 创建运行脚本...")

        # Windows 脚本
        if self.system == "windows":
            # Uranium 激活脚本
            uranium_activate_script = f"""@echo off
echo 激活 Uranium 开发环境...
cd /d "{self.uranium_path}"
call build_{self.system_suffix}\\generators\\virtual_python_env.bat
echo Uranium 开发环境已激活
echo 使用方法: python -m pytest tests/ (运行测试)
cmd /k
"""
            with open(self.project_root / f"activate_uranium_{self.system_suffix}.bat", "w") as f:
                f.write(uranium_activate_script)

            # Cura 运行脚本
            cura_run_script = f"""@echo off
echo 启动 Cura 开发环境...
cd /d "{self.uranium_path}"
call build_{self.system_suffix}\\generators\\virtual_python_env.bat
cd /d "{self.cura_path}"
echo 启动 Cura...
python cura_app.py %*
"""
            with open(self.project_root / f"run_cura_{self.system_suffix}.bat", "w") as f:
                f.write(cura_run_script)

        else:
            # Unix 脚本 (macOS/Linux)
            # Uranium 激活脚本
            uranium_activate_script = f"""#!/bin/bash
echo "激活 Uranium 开发环境..."
cd "{self.uranium_path}"
source build_{self.system_suffix}/generators/virtual_python_env.sh
echo "Uranium 开发环境已激活"
echo "使用方法: python -m pytest tests/ (运行测试)"
exec "$SHELL"
"""
            script_path = self.project_root / f"activate_uranium_{self.system_suffix}.sh"
            with open(script_path, "w") as f:
                f.write(uranium_activate_script)
            script_path.chmod(0o755)

            # Cura 运行脚本
            cura_run_script = f"""#!/bin/bash
echo "启动 Cura 开发环境..."
cd "{self.uranium_path}"
source build_{self.system_suffix}/generators/virtual_python_env.sh
cd "{self.cura_path}"
echo "启动 Cura..."
python cura_app.py "$@"
"""
            script_path = self.project_root / f"run_cura_{self.system_suffix}.sh"
            with open(script_path, "w") as f:
                f.write(cura_run_script)
            script_path.chmod(0o755)

        print("✓ 运行脚本创建完成")

    def verify_setup(self):
        """验证设置"""
        print("\n✅ 验证环境设置...")

        # 检查 Uranium 构建目录
        uranium_build_dir = self.uranium_path / f"build_{self.system_suffix}"
        if uranium_build_dir.exists():
            print(f"✓ Uranium 构建目录存在: {uranium_build_dir}")
        else:
            print(f"❌ Uranium 构建目录不存在: {uranium_build_dir}")
            return False

        # 检查虚拟环境
        venv_dir = uranium_build_dir / "generators"
        if venv_dir.exists():
            print(f"✓ 虚拟环境目录存在: {venv_dir}")
        else:
            print(f"❌ 虚拟环境目录不存在: {venv_dir}")
            return False

        # 检查 Uranium 是否在可编辑模式
        try:
            result = self._run_command(["conan", "editable", "list"], check=False)
            if "uranium" in result.stdout:
                print("✓ Uranium 处于可编辑模式")
            else:
                print("❌ Uranium 未处于可编辑模式")
                return False
        except:
            print("❌ 无法检查 Uranium 可编辑模式状态")
            return False

        print("✅ 环境设置验证通过！")
        return True

    def print_usage_instructions(self):
        """打印使用说明"""
        print("\n" + "="*60)
        print("🎉 Cura & Uranium 并行开发环境设置完成！")
        print("="*60)

        print(f"\n📁 项目结构:")
        print(f"  - 项目根目录: {self.project_root}")
        print(f"  - Cura: {self.cura_path}")
        print(f"  - Uranium: {self.uranium_path}")
        print(f"  - 系统后缀: {self.system_suffix}")

        print(f"\n🚀 使用方法:")

        if self.system == "windows":
            print(f"  1. 激活 Uranium 开发环境:")
            print(f"     activate_uranium_{self.system_suffix}.bat")
            print(f"  2. 运行 Cura:")
            print(f"     run_cura_{self.system_suffix}.bat")
        else:
            print(f"  1. 激活 Uranium 开发环境:")
            print(f"     ./activate_uranium_{self.system_suffix}.sh")
            print(f"  2. 运行 Cura:")
            print(f"     ./run_cura_{self.system_suffix}.sh")

        print(f"\n🔧 PyCharm 设置:")
        print(f"  1. 打开 PyCharm")
        print(f"  2. 选择 'Open' 并选择项目根目录: {self.project_root}")
        print(f"  3. PyCharm 会自动识别多模块项目结构")
        print(f"  4. 配置 Python 解释器:")
        print(f"     - Uranium: {self.uranium_path}/build_{self.system_suffix}/generators/cura_venv/bin/python")
        print(f"     - Cura: 使用相同的解释器")

        print(f"\n📝 开发说明:")
        print(f"  - Uranium 处于可编辑模式，修改会立即生效")
        print(f"  - CuraEngine 已排除，不会被构建")
        print(f"  - 虚拟环境和构建文件夹使用系统后缀 '{self.system_suffix}'")
        print(f"  - 支持 macOS 和 Windows 跨平台开发")

        print(f"\n🧪 测试:")
        print(f"  - 运行 Uranium 测试: cd Uranium && python -m pytest tests/")
        print(f"  - 运行 Cura 测试: cd Cura && python -m pytest tests/")

        print(f"\n⚠️  注意事项:")
        print(f"  - 确保严格按照官方文档的依赖版本")
        print(f"  - Python 3.12+ 是必需的")
        print(f"  - Conan >=2.7.0 <3.0.0 是必需的")
        print(f"  - 如需重新构建，删除 build_{self.system_suffix} 目录后重新运行此脚本")

    def run_setup(self):
        """运行完整设置流程"""
        try:
            print("🚀 开始设置 Cura & Uranium 并行开发环境...")
            print(f"系统: {self.system} ({self.system_suffix})")

            # 1. 检查依赖
            if not self.check_dependencies():
                return False

            # 2. 配置 Conan
            self.setup_conan_config()

            # 3. 设置 Uranium 开发环境
            self.setup_uranium_dev_env()

            # 4. 设置 Uranium 为可编辑模式
            uranium_version = self.setup_uranium_editable()

            # 5. 设置 Cura 开发环境
            self.setup_cura_dev_env(uranium_version)

            # 6. 创建 PyCharm 配置
            self.create_pycharm_config()

            # 7. 创建运行脚本
            self.create_run_scripts()

            # 8. 验证设置
            if not self.verify_setup():
                print("❌ 环境设置验证失败")
                return False

            # 9. 打印使用说明
            self.print_usage_instructions()

            return True

        except Exception as e:
            print(f"❌ 设置过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            # 回到项目根目录
            os.chdir(self.project_root)


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print("Cura & Uranium 并行开发环境自动化设置脚本")
        print("用法: python setup_cura_dev_env.py")
        print("\n功能:")
        print("- 自动配置 Conan 环境")
        print("- 构建 Uranium 开发环境（带系统后缀）")
        print("- 设置 Uranium 为可编辑模式")
        print("- 构建 Cura 开发环境")
        print("- 创建 PyCharm 多仓库项目配置")
        print("- 生成便捷的运行脚本")
        print("- 排除 CuraEngine 构建")
        print("- 支持 macOS/Windows 跨平台开发")
        return

    setup = CuraDevEnvSetup()
    success = setup.run_setup()

    if success:
        print("\n🎉 设置完成！现在可以开始 Cura & Uranium 并行开发了。")
        sys.exit(0)
    else:
        print("\n❌ 设置失败，请检查错误信息并重试。")
        sys.exit(1)


if __name__ == "__main__":
    main()
