[{"classes": [{"className": "QPcscCard", "methods": [{"access": "public", "name": "enableAutodelete", "returnType": "void"}], "object": true, "qualifiedClassName": "QPcscCard", "signals": [{"access": "public", "name": "disconnected", "returnType": "void"}, {"access": "public", "name": "invalidated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}, {"name": "reason", "type": "QNearFieldTarget::<PERSON><PERSON><PERSON>"}, {"name": "result", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "requestCompleted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QNdefMessage"}], "name": "ndefMessageRead", "returnType": "void"}], "slots": [{"access": "public", "name": "onDisconnectRequest", "returnType": "void"}, {"access": "public", "name": "onTargetDestroyed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}, {"name": "command", "type": "QByteArray"}], "name": "onSendCommandRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}], "name": "onReadNdefMessagesRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}, {"name": "messages", "type": "QList<QNdefMessage>"}], "name": "onWriteNdefMessagesRequest", "returnType": "void"}, {"access": "private", "name": "onKeepAliveTimeout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpcsccard_p.h", "outputRevision": 68}, {"classes": [{"className": "QPcscManager", "object": true, "qualifiedClassName": "QPcscManager", "signals": [{"access": "public", "arguments": [{"name": "card", "type": "QPcscCard*"}, {"name": "uid", "type": "QByteArray"}, {"name": "accessMethods", "type": "QNearFieldTarget::AccessMethods"}, {"name": "maxInputLength", "type": "int"}], "name": "cardInserted", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "accessMethod", "type": "QNearFieldTarget::AccessMethod"}], "name": "onStartTargetDetectionRequest", "returnType": "void"}, {"access": "public", "name": "onStopTargetDetectionRequest", "returnType": "void"}, {"access": "private", "name": "onStateUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpcscmanager_p.h", "outputRevision": 68}, {"classes": [{"className": "QPcscSlot", "object": true, "qualifiedClassName": "QPcscSlot", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpcscslot_p.h", "outputRevision": 68}, {"classes": [{"className": "QNearFieldManagerPrivateImpl", "object": true, "qualifiedClassName": "QNearFieldManagerPrivateImpl", "signals": [{"access": "public", "arguments": [{"name": "accessMethod", "type": "QNearFieldTarget::AccessMethod"}], "name": "startTargetDetectionRequest", "returnType": "void"}, {"access": "public", "name": "stopTargetDetectionRequest", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "card", "type": "QPcscCard*"}, {"name": "uid", "type": "QByteArray"}, {"name": "accessMethods", "type": "QNearFieldTarget::AccessMethods"}, {"name": "maxInputLength", "type": "int"}], "name": "onCardInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QNearFieldTargetPrivate*"}], "name": "onTargetLost", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QNearFieldManagerPrivate"}]}], "inputFile": "qnearfieldmanager_pcsc_p.h", "outputRevision": 68}, {"classes": [{"className": "QNearFieldTargetPrivateImpl", "object": true, "qualifiedClassName": "QNearFieldTargetPrivateImpl", "signals": [{"access": "public", "name": "disconnectRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}, {"name": "command", "type": "QByteArray"}], "name": "sendCommandRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}], "name": "readNdefMessagesRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}, {"name": "messages", "type": "QList<QNdefMessage>"}], "name": "writeNdefMessagesRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QNearFieldTargetPrivate*"}], "name": "targetLost", "returnType": "void"}], "slots": [{"access": "public", "name": "onDisconnected", "returnType": "void"}, {"access": "public", "name": "onInvalidated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QNearFieldTarget::RequestId"}, {"name": "reason", "type": "QNearFieldTarget::<PERSON><PERSON><PERSON>"}, {"name": "result", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "onRequestCompleted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QNdefMessage"}], "name": "onNdefMessageRead", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QNearFieldTargetPrivate"}]}], "inputFile": "qnearfieldtarget_pcsc_p.h", "outputRevision": 68}, {"classes": [{"className": "QNearFieldManager", "enums": [{"isClass": true, "isFlag": false, "name": "AdapterState", "values": ["Offline", "TurningOn", "Online", "TurningOff"]}], "object": true, "qualifiedClassName": "QNearFieldManager", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QNearFieldManager::AdapterState"}], "name": "adapterStateChanged", "returnType": "void"}, {"access": "public", "name": "targetDetectionStopped", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QNearFieldTarget*"}], "name": "targetDetected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QNearFieldTarget*"}], "name": "targetLost", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnearfieldmanager.h", "outputRevision": 68}, {"classes": [{"className": "QNearFieldManagerPrivate", "object": true, "qualifiedClassName": "QNearFieldManagerPrivate", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QNearFieldManager::AdapterState"}], "name": "adapterStateChanged", "returnType": "void"}, {"access": "public", "name": "targetDetectionStopped", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QNearFieldTarget*"}], "name": "targetDetected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QNearFieldTarget*"}], "name": "targetLost", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnearfieldmanager_p.h", "outputRevision": 68}, {"classes": [{"className": "QNear<PERSON><PERSON>Target", "enums": [{"isClass": false, "isFlag": false, "name": "Type", "values": ["ProprietaryTag", "NfcTagType1", "NfcTagType2", "NfcTagType3", "NfcTagType4", "NfcTagType4A", "NfcTagType4B", "MifareTag"]}, {"isClass": false, "isFlag": false, "name": "AccessMethod", "values": ["UnknownAccess", "NdefAccess", "TagTypeSpecificAccess", "AnyAccess"]}, {"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "UnknownE<PERSON>r", "UnsupportedError", "TargetOutOfRangeError", "NoResponseError", "ChecksumMismatchError", "InvalidParametersError", "ConnectionError", "NdefReadError", "NdefWriteError", "CommandError", "TimeoutError"]}], "object": true, "qualifiedClassName": "QNear<PERSON><PERSON>Target", "signals": [{"access": "public", "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QNdefMessage"}], "name": "ndefMessageRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QNearFieldTarget::RequestId"}], "name": "requestCompleted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QNearFieldTarget::<PERSON><PERSON><PERSON>"}, {"name": "id", "type": "QNearFieldTarget::RequestId"}], "name": "error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnearfieldtarget.h", "outputRevision": 68}, {"classes": [{"className": "QNearFieldTargetPrivate", "object": true, "qualifiedClassName": "QNearFieldTargetPrivate", "signals": [{"access": "public", "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "message", "type": "QNdefMessage"}], "name": "ndefMessageRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QNearFieldTarget::RequestId"}], "name": "requestCompleted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QNearFieldTarget::<PERSON><PERSON><PERSON>"}, {"name": "id", "type": "QNearFieldTarget::RequestId"}], "name": "error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnearfieldtarget_p.h", "outputRevision": 68}]