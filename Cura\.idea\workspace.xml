<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="3f439d32-b211-4ccd-ac82-20c4f9bdec40" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.printer-linter" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.pylintrc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/CITATION.cff" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/CMakeLists.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/CONTRIBUTING.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/CuraVersion.py.jinja" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/LICENSE" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/SECURITY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/UltiMaker-Cura.spec.jinja" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/conandata.yml" beforeDir="false" afterPath="$PROJECT_DIR$/conandata.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/conanfile.py" beforeDir="false" afterPath="$PROJECT_DIR$/conanfile.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura.sharedmimeinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/BuildVolume.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/BuildVolume.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/CuraApplication.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/CuraApplication.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/LayerDataBuilder.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/LayerDataBuilder.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/PlatformPhysics.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/PlatformPhysics.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/Settings/CuraContainerStack.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/Settings/CuraContainerStack.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/Settings/ExtruderManager.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/Settings/ExtruderManager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/Settings/GlobalStack.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/Settings/GlobalStack.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura/Settings/MachineManager.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura/Settings/MachineManager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cura_app.py" beforeDir="false" afterPath="$PROJECT_DIR$/cura_app.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/3MFReader/ThreeMFReader.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/3MFReader/ThreeMFReader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/3MFReader/ThreeMFWorkspaceReader.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/3MFReader/ThreeMFWorkspaceReader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/CuraEngineBackend/CuraEngineBackend.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/CuraEngineBackend/CuraEngineBackend.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/GCodeReader/GCodeReader.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/GCodeReader/GCodeReader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/MachineSettingsAction/MachineSettingsAction.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/MachineSettingsAction/MachineSettingsAction.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/PostProcessingPlugin/Script.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/PostProcessingPlugin/Script.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/SimulationView/resources/Pause.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/SimulationView/resources/Pause.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/SimulationView/resources/Play.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/SimulationView/resources/Play.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/SliceInfoPlugin/SliceInfo.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/SliceInfoPlugin/SliceInfo.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/SolidView/xray_overlay.shader" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/SolidView/xray_overlay.shader" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Camera.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Camera.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/CancelCircle.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/CancelCircle.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Check.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Check.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/CheckCircle.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/CheckCircle.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/PauseCircle.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/PauseCircle.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Prohibition.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Prohibition.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Warning.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/Warning.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/icons/CameraPhoto.svg" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/UM3NetworkPrinting/resources/svg/icons/CameraPhoto.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pytest.ini" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/README_resources.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/conanfile.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/biqu_b1.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/biqu_b1.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/biqu_b1_abl.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/biqu_b1_abl.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/biqu_base.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/biqu_base.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/biqu_bx_abl.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/biqu_bx_abl.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/biqu_hurakan.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/biqu_hurakan.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/creality_k1max.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/creality_k1max.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/lnl3d_d3.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/lnl3d_d3.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/lnl3d_d3_vulcan.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/lnl3d_d3_vulcan.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/lnl3d_d5.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/lnl3d_d5.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/definitions/renkforce_pro7dual.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/definitions/renkforce_pro7dual.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/extruders/biqu_base_extruder_0.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/extruders/biqu_base_extruder_0.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/extruders/creality_k1max_extruder_0.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/extruders/creality_k1max_extruder_0.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/extruders/lnl3d_extruder_left.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/extruders/lnl3d_extruder_left.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/extruders/lnl3d_extruder_right.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/extruders/lnl3d_extruder_right.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/extruders/renkforce_pro7dual_1st.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/extruders/renkforce_pro7dual_1st.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/extruders/renkforce_pro7dual_2nd.def.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/extruders/renkforce_pro7dual_2nd.def.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/meshes/BIQU_SSS.stl" beforeDir="false" afterPath="$PROJECT_DIR$/resources/meshes/BIQU_SSS.stl" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/meshes/lulzbot_mini.stl" beforeDir="false" afterPath="$PROJECT_DIR$/resources/meshes/lulzbot_mini.stl" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.25_ABS_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.25_ABS_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.25_ABS_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.25_ABS_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.25_ABS_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.25_ABS_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.40_ABS_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_DBE0.60_ABS_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.25_ABS_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.25_ABS_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.25_ABS_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.25_ABS_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.25_ABS_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.25_ABS_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.40_ABS_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_FBE0.60_ABS_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_G.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VDBE0.80_ABS_G.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_G.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/ABS/deltacomb_VFBE0.80_ABS_G.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.25_PLA_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.25_PLA_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.25_PLA_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.25_PLA_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.25_PLA_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.25_PLA_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.40_PLA_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_DBE0.60_PLA_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.25_PLA_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.25_PLA_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.25_PLA_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.25_PLA_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.25_PLA_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.25_PLA_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.40_PLA_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_FBE0.60_PLA_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_G.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VDBE0.80_PLA_G.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_G.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/PLA/deltacomb_VFBE0.80_PLA_G.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/TPU/deltacomb_FBE0.40_TPU_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/TPU/deltacomb_FBE0.40_TPU_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/TPU/deltacomb_FBE0.40_TPU_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/TPU/deltacomb_FBE0.40_TPU_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/TPU/deltacomb_FBE0.40_TPU_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/TPU/deltacomb_FBE0.40_TPU_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_A.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_A.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_B.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_B.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_C.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_C.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_D.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_D.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_E.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_E.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_F.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_F.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_G.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/quality/deltacomb/deltacomb_global_G.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Adhesion.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Adhesion.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ArrowDoubleCircleRight.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ArrowDoubleCircleRight.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ArrowFourWay.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ArrowFourWay.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ArrowReset.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ArrowReset.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Bandage.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Bandage.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/BlackMagic.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/BlackMagic.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Cancel.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Cancel.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/CancelBlock.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/CancelBlock.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/CancelCircle.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/CancelCircle.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Check.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Check.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/CheckCircle.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/CheckCircle.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleDown.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleDown.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleLeft.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleLeft.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleRight.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleRight.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleUp.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ChevronSingleUp.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Clock.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Clock.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Cloud.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Cloud.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/DualExtrusion.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/DualExtrusion.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Experiment.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Experiment.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Eye.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Eye.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Fan.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Fan.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Folder.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Folder.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Formula.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Formula.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Guide.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Guide.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Hamburger.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Hamburger.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/House.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/House.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill0.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill0.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill1.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill1.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill2.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill2.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill3.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Infill3.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/InfillGradual.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/InfillGradual.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Information.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Information.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/LayFlat.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/LayFlat.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/LayFlatOnFace.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/LayFlatOnFace.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Layers.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Layers.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Link.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Link.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/LinkExternal.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/LinkExternal.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Magnifier.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Magnifier.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshType.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshType.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshTypeIntersect.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshTypeIntersect.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshTypeNormal.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshTypeNormal.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshTypeSupport.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/MeshTypeSupport.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Minus.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Minus.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Mirror.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Mirror.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Pen.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Pen.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Plugin.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Plugin.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Plus.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Plus.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrintQuality.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrintQuality.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrintShell.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrintShell.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrintTravel.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrintTravel.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Printer.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Printer.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrinterTriple.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/PrinterTriple.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Quick.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Quick.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Rotate.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Rotate.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Scale.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Scale.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ScaleMax.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ScaleMax.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Shield.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Shield.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Sliders.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Sliders.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/SpeedOMeter.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/SpeedOMeter.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Spool.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Spool.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Sputnik.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Sputnik.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Star.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Star.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/StarFilled.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/StarFilled.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Support.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Support.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/SupportBlocker.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/SupportBlocker.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ThreeDots.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ThreeDots.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/UltimakerCura.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/UltimakerCura.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/View3D.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/View3D.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewFront.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewFront.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewLeft.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewLeft.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewRight.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewRight.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewTop.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/ViewTop.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Warning.svg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/icons/default/Warning.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.2.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.2.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.3.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.3.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.4.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.4.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.5.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.5.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.6.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.6.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.8.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_0.8.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.2.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.2.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.3.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.3.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.4.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.4.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.5.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.5.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.6.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.6.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.8.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_b1_abl_0.8.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.2.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.2.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.3.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.3.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.4.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.4.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.5.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.5.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.6.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.6.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.8.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_base_0.8.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.2.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.2.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.3.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.3.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.4.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.4.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.5.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.5.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.6.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.6.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.8.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_bx_abl_0.8.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.2.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.2.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.3.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.3.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.4.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.4.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.5.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.5.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.6.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.6.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.8.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/biqu/biqu_hurakan_0.8.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/creality/creality_k1_max_0.4.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/creality/creality_k1_max_0.4.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/creality/creality_k1_max_0.6.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/creality/creality_k1_max_0.6.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/creality/creality_k1_max_0.8.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/creality/creality_k1_max_0.8.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20_vfbe080.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20_vfbe080.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_dbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_dbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_dbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_dbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_dbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_dbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_vdbe080.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20dual_vdbe080.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20flux_fbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20flux_fbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20flux_fbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20flux_fbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20flux_fbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc20flux_fbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_fbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_fbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_fbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_fbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_fbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_fbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_vfbe080.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21_vfbe080.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_dbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_dbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_dbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_dbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_dbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_dbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_vdbe080.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21dual_vdbe080.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21flux_fbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21flux_fbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21flux_fbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21flux_fbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21flux_fbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc21flux_fbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_fbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_fbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_fbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_fbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_fbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_fbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_vfbe080.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30_vfbe080.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_dbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_dbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_dbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_dbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_dbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_dbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_vdbe080.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30dual_vdbe080.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30flux_fbe025.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30flux_fbe025.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30flux_fbe040.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30flux_fbe040.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30flux_fbe060.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/deltacomb/deltacomb_dc30flux_fbe060.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.2.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.2.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.4.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.4.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.6.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.6.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.8.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_0.8.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_1.0.inst.cfg" beforeDir="false" afterPath="$PROJECT_DIR$/resources/variants/renkforce/renkforce_pro7dual_1.0.inst.cfg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/check_gcode_buffer.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/check_gcode_buffer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/check_missing_translations.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/check_missing_translations.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/check_shortcut_keys.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/check_shortcut_keys.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/fix_lokalize_translations.sh" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/fix_lokalize_translations.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/get_pypi_hashes.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/get_pypi_hashes.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/rename_cura_1_tags.sh" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/rename_cura_1_tags.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/Settings/TestGlobalStack.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/Settings/TestGlobalStack.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/TestMachineAction.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/TestMachineAction.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="2yqv14RUUDDRHbvmK7BRJRKbkwP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.cura_app.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PY-251.26094.141" />
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3f439d32-b211-4ccd-ac82-20c4f9bdec40" name="更改" comment="" />
      <created>1750574702462</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750574702462</updated>
      <workItem from="1750574703359" duration="605000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>