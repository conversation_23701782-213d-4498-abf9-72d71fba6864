[{"classes": [{"className": "QAbstractAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "AnimationType", "values": ["KeyframeAnimation", "MorphingAnimation", "VertexBlendAnimation"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "animationName", "notify": "animationNameChanged", "read": "animationName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAnimationName"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "animationType", "read": "animationType", "required": false, "scriptable": true, "stored": true, "type": "AnimationType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAbstractAnimation", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "animationNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "float"}], "name": "durationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "setAnimationName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "name": "setPosition", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractanimation.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractAnimationClip", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAbstractAnimationClip", "signals": [{"access": "public", "arguments": [{"name": "duration", "type": "float"}], "name": "durationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractanimationclip.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractChannelMapping", "object": true, "qualifiedClassName": "Qt3DAnimation::QAbstractChannelMapping", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractchannelmapping.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractClipAnimator", "enums": [{"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "running", "notify": "running<PERSON><PERSON>ed", "read": "isRunning", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRunning"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "loops", "notify": "loopCountChanged", "read": "loopCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoopCount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "channelMapper", "notify": "channelMapperChanged", "read": "channelMapper", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QChannelMapper*", "user": false, "write": "setChannelMapper"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "clock", "notify": "clockChanged", "read": "clock", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QClock*", "user": false, "write": "setClock"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "normalizedTime", "notify": "normalizedTimeChanged", "read": "normalizedTime", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setNormalizedTime"}], "qualifiedClassName": "Qt3DAnimation::QAbstractClipAnimator", "signals": [{"access": "public", "arguments": [{"name": "running", "type": "bool"}], "name": "running<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "channelMapper", "type": "Qt3DAnimation::QChannelMapper*"}], "name": "channelMapperChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "loops", "type": "int"}], "name": "loopCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clock", "type": "Qt3DAnimation::QClock*"}], "name": "clockChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "float"}], "name": "normalizedTimeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "running", "type": "bool"}], "name": "setRunning", "returnType": "void"}, {"access": "public", "arguments": [{"name": "channelMapper", "type": "Qt3DAnimation::QChannelMapper*"}], "name": "setChannelMapper", "returnType": "void"}, {"access": "public", "arguments": [{"name": "loops", "type": "int"}], "name": "setLoopCount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clock", "type": "Qt3DAnimation::QClock*"}], "name": "setClock", "returnType": "void"}, {"access": "public", "arguments": [{"name": "timeFraction", "type": "float"}], "name": "setNormalizedTime", "returnType": "void"}, {"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QComponent"}]}], "inputFile": "qabstractclipanimator.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractClipBlendNode", "object": true, "qualifiedClassName": "Qt3DAnimation::QAbstractClipBlendNode", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractclipblendnode.h", "outputRevision": 68}, {"classes": [{"className": "QAdditiveClipBlend", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "baseClip", "notify": "baseClipChanged", "read": "baseClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setBaseClip"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "additiveClip", "notify": "additiveClipChanged", "read": "additiveClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setAdditiveClip"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "additiveFactor", "notify": "additiveFactorChanged", "read": "additiveFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAdditiveFactor"}], "qualifiedClassName": "Qt3DAnimation::QAdditiveClipBlend", "signals": [{"access": "public", "arguments": [{"name": "additiveFactor", "type": "float"}], "name": "additiveFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "baseClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "baseClipChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "additiveClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "additiveClipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "additiveFactor", "type": "float"}], "name": "setAdditiveFactor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "baseClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "setBaseClip", "returnType": "void"}, {"access": "public", "arguments": [{"name": "additiveClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "setAdditiveClip", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractClipBlendNode"}]}], "inputFile": "qadditiveclipblend.h", "outputRevision": 68}, {"classes": [{"className": "QAnimationAspect", "object": true, "qualifiedClassName": "Qt3DAnimation::QAnimationAspect", "superClasses": [{"access": "public", "name": "Qt3DCore::QAbstractAspect"}]}], "inputFile": "qanimationaspect.h", "outputRevision": 68}, {"classes": [{"className": "QAnimationClip", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "clipData", "notify": "clipDataChanged", "read": "clipData", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAnimationClipData", "user": false, "write": "setClipData"}], "qualifiedClassName": "Qt3DAnimation::QAnimationClip", "signals": [{"access": "public", "arguments": [{"name": "clipData", "type": "Qt3DAnimation::QAnimationClipData"}], "name": "clipDataChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "clipData", "type": "Qt3DAnimation::QAnimationClipData"}], "name": "setClipData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimationClip"}]}], "inputFile": "qanimationclip.h", "outputRevision": 68}, {"classes": [{"className": "QAnimationClipLoader", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["NotReady", "Ready", "Error"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAnimationClipLoader", "signals": [{"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "Status"}], "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "name": "setSource", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimationClip"}]}], "inputFile": "qanimationcliploader.h", "outputRevision": 68}, {"classes": [{"className": "QAnimationController", "methods": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "getAnimationIndex", "returnType": "int"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "getGroup", "returnType": "Qt3DAnimation::QAnimationGroup*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "activeAnimationGroup", "notify": "activeAnimationGroupChanged", "read": "activeAnimationGroup", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveAnimationGroup"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "positionScale", "notify": "positionScaleChanged", "read": "positionScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPositionScale"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "positionOffset", "notify": "positionOffsetChanged", "read": "positionOffset", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPositionOffset"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "entity", "notify": "entityChanged", "read": "entity", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QEntity*", "user": false, "write": "setEntity"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "recursive", "notify": "recursiveChanged", "read": "recursive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRecursive"}], "qualifiedClassName": "Qt3DAnimation::QAnimationController", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "activeAnimationGroupChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "float"}], "name": "positionScaleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "name": "positionOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "entity", "type": "Qt3DCore::QEntity*"}], "name": "entityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "recursive", "type": "bool"}], "name": "recursiveChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "setActiveAnimationGroup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "name": "setPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "float"}], "name": "setPositionScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "name": "setPositionOffset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "entity", "type": "Qt3DCore::QEntity*"}], "name": "setEntity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "recursive", "type": "bool"}], "name": "setRecursive", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qanimationcontroller.h", "outputRevision": 68}, {"classes": [{"className": "QAnimationGroup", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAnimationGroup", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "float"}], "name": "durationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "setName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "name": "setPosition", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qanimationgroup.h", "outputRevision": 68}, {"classes": [{"className": "QBlendedClipAnimator", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "blendTree", "notify": "blendTreeChanged", "read": "blendTree", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setBlendTree"}], "qualifiedClassName": "Qt3DAnimation::QBlendedClipAnimator", "signals": [{"access": "public", "arguments": [{"name": "blendTree", "type": "QAbstractClipBlendNode*"}], "name": "blendTreeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "blendTree", "type": "QAbstractClipBlendNode*"}], "name": "setBlendTree", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DAnimation::QAbstractClipAnimator"}]}], "inputFile": "qblendedclipanimator.h", "outputRevision": 68}, {"classes": [{"className": "QCallbackMapping", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "channelName", "notify": "channelNameChanged", "read": "channelName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setChannelName"}], "qualifiedClassName": "Qt3DAnimation::QCallbackMapping", "signals": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "name": "channelNameChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "name": "setChannelName", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractChannelMapping"}]}], "inputFile": "qcallbackmapping.h", "outputRevision": 68}, {"classes": [{"className": "QChannelMapper", "object": true, "qualifiedClassName": "Qt3DAnimation::QChannelMapper", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qchannelmapper.h", "outputRevision": 68}, {"classes": [{"className": "QChannelMapping", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "channelName", "notify": "channelNameChanged", "read": "channelName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setChannelName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QNode*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "property", "notify": "propertyChanged", "read": "property", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setProperty"}], "qualifiedClassName": "Qt3DAnimation::QChannelMapping", "signals": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "name": "channelNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QNode*"}], "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QString"}], "name": "propertyChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "name": "setChannelName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QNode*"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QString"}], "name": "setProperty", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractChannelMapping"}]}], "inputFile": "qchannelmapping.h", "outputRevision": 68}, {"classes": [{"className": "QClipAnimator", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "clip", "notify": "clipChanged", "read": "clip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractAnimationClip*", "user": false, "write": "setClip"}], "qualifiedClassName": "Qt3DAnimation::QClipAnimator", "signals": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "name": "clipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "name": "setClip", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DAnimation::QAbstractClipAnimator"}]}], "inputFile": "qclipanimator.h", "outputRevision": 68}, {"classes": [{"className": "QClipBlendValue", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "clip", "notify": "clipChanged", "read": "clip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractAnimationClip*", "user": false, "write": "setClip"}], "qualifiedClassName": "Qt3DAnimation::QClipBlendValue", "signals": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "name": "clipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "name": "setClip", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DAnimation::QAbstractClipBlendNode"}]}], "inputFile": "qclipblendvalue.h", "outputRevision": 68}, {"classes": [{"className": "QClock", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "playbackRate", "notify": "playbackRateChanged", "read": "playbackRate", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setPlaybackRate"}], "qualifiedClassName": "Qt3DAnimation::QClock", "signals": [{"access": "public", "arguments": [{"name": "playbackRate", "type": "double"}], "name": "playbackRateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qclock.h", "outputRevision": 68}, {"classes": [{"className": "QKeyframeAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "RepeatMode", "values": ["None", "Constant", "Repeat"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "framePositions", "notify": "framePositionsChanged", "read": "framePositions", "required": false, "scriptable": true, "stored": true, "type": "QList<float>", "user": false, "write": "setFramePositions"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QTransform*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "easing", "notify": "easingChanged", "read": "easing", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasing"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "targetName", "notify": "targetNameChanged", "read": "targetName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTargetName"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "startMode", "notify": "startModeChanged", "read": "startMode", "required": false, "scriptable": true, "stored": true, "type": "RepeatMode", "user": false, "write": "setStartMode"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "endMode", "notify": "endModeChanged", "read": "endMode", "required": false, "scriptable": true, "stored": true, "type": "RepeatMode", "user": false, "write": "setEndMode"}], "qualifiedClassName": "Qt3DAnimation::QKeyframeAnimation", "signals": [{"access": "public", "arguments": [{"name": "positions", "type": "QList<float>"}], "name": "framePositionsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QTransform*"}], "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "name": "easingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "targetNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startMode", "type": "QKeyframeAnimation::RepeatMode"}], "name": "startModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endMode", "type": "QKeyframeAnimation::RepeatMode"}], "name": "endModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "positions", "type": "QList<float>"}], "name": "setFramePositions", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QTransform*"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "name": "setEasing", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "setTargetName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "RepeatMode"}], "name": "setStartMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "RepeatMode"}], "name": "setEndMode", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qkeyframeanimation.h", "outputRevision": 68}, {"classes": [{"className": "QLerpClipBlend", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "startClip", "notify": "startClipChanged", "read": "startClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setStartClip"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "endClip", "notify": "endClipChanged", "read": "endClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setEndClip"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "blendFactor", "notify": "blendFactorChanged", "read": "blendFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBlendFactor"}], "qualifiedClassName": "Qt3DAnimation::QLerpClipBlend", "signals": [{"access": "public", "arguments": [{"name": "blendFactor", "type": "float"}], "name": "blendFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "startClipChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "endClipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "blendFactor", "type": "float"}], "name": "setBlendFactor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "setStartClip", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "name": "setEndClip", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractClipBlendNode"}]}], "inputFile": "qlerpclipblend.h", "outputRevision": 68}, {"classes": [{"className": "QMorphingAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "Method", "values": ["Normalized", "Relative"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "targetPositions", "notify": "targetPositionsChanged", "read": "targetPositions", "required": false, "scriptable": true, "stored": true, "type": "QList<float>", "user": false, "write": "setTargetPositions"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "interpolator", "notify": "interpolatorChanged", "read": "interpolator", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "targetName", "notify": "targetNameChanged", "read": "targetName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTargetName"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "method", "notify": "methodChanged", "read": "method", "required": false, "scriptable": true, "stored": true, "type": "Method", "user": false, "write": "set<PERSON>ethod"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "easing", "notify": "easingChanged", "read": "easing", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasing"}], "qualifiedClassName": "Qt3DAnimation::QMorphingAnimation", "signals": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "name": "targetPositionsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "name": "interpolatorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "targetNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "method", "type": "QMorphingAnimation::Method"}], "name": "methodChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "name": "easingChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "name": "setTargetPositions", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "setTargetName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "method", "type": "QMorphingAnimation::Method"}], "name": "set<PERSON>ethod", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "name": "setEasing", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qmorphinganimation.h", "outputRevision": 68}, {"classes": [{"className": "QMorph<PERSON>arget", "methods": [{"access": "public", "arguments": [{"name": "geometry", "type": "Qt3DCore::QGeometry*"}, {"name": "attributes", "type": "QStringList"}], "name": "fromGeometry", "returnType": "QMorphTarget*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "attributeNames", "notify": "attributeNamesChanged", "read": "attributeNames", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}], "qualifiedClassName": "Qt3DAnimation::QMorphTarget", "signals": [{"access": "public", "arguments": [{"name": "attributeNames", "type": "QStringList"}], "name": "attributeNamesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmorphtarget.h", "outputRevision": 68}, {"classes": [{"className": "QSkeletonMapping", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "skeleton", "notify": "skeletonChanged", "read": "skeleton", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAbstractSkeleton*", "user": false, "write": "setSkeleton"}], "qualifiedClassName": "Qt3DAnimation::QSkeletonMapping", "signals": [{"access": "public", "arguments": [{"name": "skeleton", "type": "Qt3DCore::QAbstractSkeleton*"}], "name": "skeletonChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "skeleton", "type": "Qt3DCore::QAbstractSkeleton*"}], "name": "setSkeleton", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractChannelMapping"}]}], "inputFile": "qskeletonmapping.h", "outputRevision": 68}, {"classes": [{"className": "QVertexBlendAnimation", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "targetPositions", "notify": "targetPositionsChanged", "read": "targetPositions", "required": false, "scriptable": true, "stored": true, "type": "QList<float>", "user": false, "write": "setTargetPositions"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "interpolator", "notify": "interpolatorChanged", "read": "interpolator", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "targetName", "notify": "targetNameChanged", "read": "targetName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTargetName"}], "qualifiedClassName": "Qt3DAnimation::QVertexBlendAnimation", "signals": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "name": "targetPositionsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "name": "interpolatorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "targetNameChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "name": "setTargetPositions", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "setTargetName", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qvertexblendanimation.h", "outputRevision": 68}]