import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickage_p.h"
        name: "QQuickAgeAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Age 2.0",
            "QtQuick.Particles/Age 2.1",
            "QtQuick.Particles/Age 2.4",
            "QtQuick.Particles/Age 2.7",
            "QtQuick.Particles/Age 2.11",
            "QtQuick.Particles/Age 6.0",
            "QtQuick.Particles/Age 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "lifeLeft"
            type: "int"
            read: "lifeLeft"
            write: "setLifeLeft"
            notify: "lifeLeftChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "advancePosition"
            type: "bool"
            read: "advancePosition"
            write: "setAdvancePosition"
            notify: "advancePositionChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "lifeLeftChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "advancePositionChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setLifeLeft"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setAdvancePosition"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickangledirection_p.h"
        name: "QQuickAngleDirection"
        accessSemantics: "reference"
        prototype: "QQuickDirection"
        exports: [
            "QtQuick.Particles/AngleDirection 2.0",
            "QtQuick.Particles/AngleDirection 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "angle"
            type: "double"
            read: "angle"
            write: "setAngle"
            notify: "angleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "magnitude"
            type: "double"
            read: "magnitude"
            write: "setMagnitude"
            notify: "magnitudeChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "angleVariation"
            type: "double"
            read: "angleVariation"
            write: "setAngleVariation"
            notify: "angleVariationChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "magnitudeVariation"
            type: "double"
            read: "magnitudeVariation"
            write: "setMagnitudeVariation"
            notify: "magnitudeVariationChanged"
            index: 3
            isFinal: true
        }
        Signal {
            name: "angleChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "magnitudeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "angleVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "magnitudeVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAngle"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAngleVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitudeVariation"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "private/qquickpointattractor_p.h"
        name: "QQuickAttractorAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Attractor 2.0",
            "QtQuick.Particles/Attractor 2.1",
            "QtQuick.Particles/Attractor 2.4",
            "QtQuick.Particles/Attractor 2.7",
            "QtQuick.Particles/Attractor 2.11",
            "QtQuick.Particles/Attractor 6.0",
            "QtQuick.Particles/Attractor 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Enum {
            name: "Proportion"
            values: [
                "Constant",
                "Linear",
                "Quadratic",
                "InverseLinear",
                "InverseQuadratic"
            ]
        }
        Enum {
            name: "AffectableParameters"
            values: ["Position", "Velocity", "Acceleration"]
        }
        Property {
            name: "strength"
            type: "double"
            read: "strength"
            write: "setStrength"
            notify: "strengthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "pointX"
            type: "double"
            read: "pointX"
            write: "setPointX"
            notify: "pointXChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "pointY"
            type: "double"
            read: "pointY"
            write: "setPointY"
            notify: "pointYChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "affectedParameter"
            type: "AffectableParameters"
            read: "affectedParameter"
            write: "setAffectedParameter"
            notify: "affectedParameterChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "proportionalToDistance"
            type: "Proportion"
            read: "proportionalToDistance"
            write: "setProportionalToDistance"
            notify: "proportionalToDistanceChanged"
            index: 4
            isFinal: true
        }
        Signal {
            name: "strengthChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "pointXChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "pointYChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "affectedParameterChanged"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
        Signal {
            name: "proportionalToDistanceChanged"
            Parameter { name: "arg"; type: "Proportion" }
        }
        Method {
            name: "setStrength"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setPointX"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setPointY"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAffectedParameter"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
        Method {
            name: "setProportionalToDistance"
            Parameter { name: "arg"; type: "Proportion" }
        }
    }
    Component {
        file: "private/qquickcumulativedirection_p.h"
        name: "QQuickCumulativeDirection"
        accessSemantics: "reference"
        defaultProperty: "directions"
        prototype: "QQuickDirection"
        exports: [
            "QtQuick.Particles/CumulativeDirection 2.0",
            "QtQuick.Particles/CumulativeDirection 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "directions"
            type: "QQuickDirection"
            isList: true
            read: "directions"
            index: 0
            isReadonly: true
            isFinal: true
        }
    }
    Component {
        file: "private/qquickcustomaffector_p.h"
        name: "QQuickCustomAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Affector 2.0",
            "QtQuick.Particles/Affector 2.1",
            "QtQuick.Particles/Affector 2.4",
            "QtQuick.Particles/Affector 2.7",
            "QtQuick.Particles/Affector 2.11",
            "QtQuick.Particles/Affector 6.0",
            "QtQuick.Particles/Affector 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "relative"
            type: "bool"
            read: "relative"
            write: "setRelative"
            notify: "relativeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "position"
            type: "QQuickDirection"
            isPointer: true
            read: "position"
            write: "setPosition"
            reset: "positionReset"
            notify: "positionChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "velocity"
            type: "QQuickDirection"
            isPointer: true
            read: "velocity"
            write: "setVelocity"
            reset: "velocityReset"
            notify: "velocityChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "acceleration"
            type: "QQuickDirection"
            isPointer: true
            read: "acceleration"
            write: "setAcceleration"
            reset: "accelerationReset"
            notify: "accelerationChanged"
            index: 3
            isFinal: true
        }
        Signal {
            name: "affectParticles"
            Parameter { name: "particles"; type: "QJSValue" }
            Parameter { name: "dt"; type: "double" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "velocityChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "relativeChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setVelocity"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setRelative"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickdirection_p.h"
        name: "QQuickDirection"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Particles/NullVector 2.0",
            "QtQuick.Particles/NullVector 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickellipseextruder_p.h"
        name: "QQuickEllipseExtruder"
        accessSemantics: "reference"
        prototype: "QQuickParticleExtruder"
        exports: [
            "QtQuick.Particles/EllipseShape 2.0",
            "QtQuick.Particles/EllipseShape 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "fill"
            type: "bool"
            read: "fill"
            write: "setFill"
            notify: "fillChanged"
            index: 0
            isFinal: true
        }
        Signal {
            name: "fillChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFill"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickfriction_p.h"
        name: "QQuickFrictionAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Friction 2.0",
            "QtQuick.Particles/Friction 2.1",
            "QtQuick.Particles/Friction 2.4",
            "QtQuick.Particles/Friction 2.7",
            "QtQuick.Particles/Friction 2.11",
            "QtQuick.Particles/Friction 6.0",
            "QtQuick.Particles/Friction 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "factor"
            type: "double"
            read: "factor"
            write: "setFactor"
            notify: "factorChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "threshold"
            type: "double"
            read: "threshold"
            write: "setThreshold"
            notify: "thresholdChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "factorChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "thresholdChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFactor"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setThreshold"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "private/qquickgravity_p.h"
        name: "QQuickGravityAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Gravity 2.0",
            "QtQuick.Particles/Gravity 2.1",
            "QtQuick.Particles/Gravity 2.4",
            "QtQuick.Particles/Gravity 2.7",
            "QtQuick.Particles/Gravity 2.11",
            "QtQuick.Particles/Gravity 6.0",
            "QtQuick.Particles/Gravity 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "magnitude"
            type: "double"
            read: "magnitude"
            write: "setMagnitude"
            notify: "magnitudeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "acceleration"
            type: "double"
            read: "magnitude"
            write: "setAcceleration"
            notify: "magnitudeChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "angle"
            type: "double"
            read: "angle"
            write: "setAngle"
            notify: "angleChanged"
            index: 2
            isFinal: true
        }
        Signal {
            name: "magnitudeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "angleChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAngle"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "private/qquickgroupgoal_p.h"
        name: "QQuickGroupGoalAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/GroupGoal 2.0",
            "QtQuick.Particles/GroupGoal 2.1",
            "QtQuick.Particles/GroupGoal 2.4",
            "QtQuick.Particles/GroupGoal 2.7",
            "QtQuick.Particles/GroupGoal 2.11",
            "QtQuick.Particles/GroupGoal 6.0",
            "QtQuick.Particles/GroupGoal 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "goalState"
            type: "QString"
            read: "goalState"
            write: "setGoalState"
            notify: "goalStateChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "jump"
            type: "bool"
            read: "jump"
            write: "setJump"
            notify: "jumpChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "goalStateChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "jumpChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setGoalState"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setJump"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickimageparticle_p.h"
        name: "QQuickImageParticle"
        accessSemantics: "reference"
        prototype: "QQuickParticlePainter"
        exports: [
            "QtQuick.Particles/ImageParticle 2.0",
            "QtQuick.Particles/ImageParticle 2.1",
            "QtQuick.Particles/ImageParticle 2.4",
            "QtQuick.Particles/ImageParticle 2.7",
            "QtQuick.Particles/ImageParticle 2.11",
            "QtQuick.Particles/ImageParticle 6.0",
            "QtQuick.Particles/ImageParticle 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Enum {
            name: "EntryEffect"
            values: ["None", "Fade", "Scale"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "image"
            write: "setImage"
            notify: "imageChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "sprites"
            type: "QQuickSprite"
            isList: true
            read: "sprites"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "colorTable"
            type: "QUrl"
            read: "colortable"
            write: "setColortable"
            notify: "colortableChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "sizeTable"
            type: "QUrl"
            read: "sizetable"
            write: "setSizetable"
            notify: "sizetableChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "opacityTable"
            type: "QUrl"
            read: "opacitytable"
            write: "setOpacitytable"
            notify: "opacitytableChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            reset: "resetColor"
            notify: "colorChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "colorVariation"
            type: "double"
            read: "colorVariation"
            write: "setColorVariation"
            reset: "resetColor"
            notify: "colorVariationChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "redVariation"
            type: "double"
            read: "redVariation"
            write: "setRedVariation"
            reset: "resetColor"
            notify: "redVariationChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "greenVariation"
            type: "double"
            read: "greenVariation"
            write: "setGreenVariation"
            reset: "resetColor"
            notify: "greenVariationChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "blueVariation"
            type: "double"
            read: "blueVariation"
            write: "setBlueVariation"
            reset: "resetColor"
            notify: "blueVariationChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "alpha"
            type: "double"
            read: "alpha"
            write: "setAlpha"
            reset: "resetColor"
            notify: "alphaChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "alphaVariation"
            type: "double"
            read: "alphaVariation"
            write: "setAlphaVariation"
            reset: "resetColor"
            notify: "alphaVariationChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "rotation"
            type: "double"
            read: "rotation"
            write: "setRotation"
            reset: "resetRotation"
            notify: "rotationChanged"
            index: 13
        }
        Property {
            name: "rotationVariation"
            type: "double"
            read: "rotationVariation"
            write: "setRotationVariation"
            reset: "resetRotation"
            notify: "rotationVariationChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "rotationVelocity"
            type: "double"
            read: "rotationVelocity"
            write: "setRotationVelocity"
            reset: "resetRotation"
            notify: "rotationVelocityChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "rotationVelocityVariation"
            type: "double"
            read: "rotationVelocityVariation"
            write: "setRotationVelocityVariation"
            reset: "resetRotation"
            notify: "rotationVelocityVariationChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "autoRotation"
            type: "bool"
            read: "autoRotation"
            write: "setAutoRotation"
            reset: "resetRotation"
            notify: "autoRotationChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "xVector"
            type: "QQuickDirection"
            isPointer: true
            read: "xVector"
            write: "setXVector"
            reset: "resetDeformation"
            notify: "xVectorChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "yVector"
            type: "QQuickDirection"
            isPointer: true
            read: "yVector"
            write: "setYVector"
            reset: "resetDeformation"
            notify: "yVectorChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "spritesInterpolate"
            type: "bool"
            read: "spritesInterpolate"
            write: "setSpritesInterpolate"
            notify: "spritesInterpolateChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "entryEffect"
            type: "EntryEffect"
            read: "entryEffect"
            write: "setEntryEffect"
            notify: "entryEffectChanged"
            index: 21
            isFinal: true
        }
        Signal { name: "imageChanged" }
        Signal { name: "colortableChanged" }
        Signal { name: "sizetableChanged" }
        Signal { name: "opacitytableChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "colorVariationChanged" }
        Signal {
            name: "alphaVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "alphaChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "redVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "greenVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "blueVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationVelocityChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationVelocityVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "autoRotationChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "xVectorChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "yVectorChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "spritesInterpolateChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "bypassOptimizationsChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "entryEffectChanged"
            Parameter { name: "arg"; type: "EntryEffect" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "arg"; type: "Status" }
        }
        Method {
            name: "setAlphaVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAlpha"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRedVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setGreenVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setBlueVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotationVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotationVelocity"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotationVelocityVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAutoRotation"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setXVector"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setYVector"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setSpritesInterpolate"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setBypassOptimizations"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setEntryEffect"
            Parameter { name: "arg"; type: "EntryEffect" }
        }
        Method { name: "createEngine" }
        Method {
            name: "spriteAdvance"
            Parameter { name: "spriteIndex"; type: "int" }
        }
        Method {
            name: "spritesUpdate"
            Parameter { name: "time"; type: "double" }
        }
        Method { name: "spritesUpdate"; isCloned: true }
        Method { name: "mainThreadFetchImageData" }
        Method {
            name: "finishBuildParticleNodes"
            Parameter { name: "n"; type: "QSGNode*"; isPointer: true }
        }
        Method { name: "invalidateSceneGraph" }
    }
    Component {
        file: "private/qquickitemparticle_p.h"
        name: "QQuickItemParticle"
        accessSemantics: "reference"
        prototype: "QQuickParticlePainter"
        exports: [
            "QtQuick.Particles/ItemParticle 2.0",
            "QtQuick.Particles/ItemParticle 2.1",
            "QtQuick.Particles/ItemParticle 2.4",
            "QtQuick.Particles/ItemParticle 2.7",
            "QtQuick.Particles/ItemParticle 2.11",
            "QtQuick.Particles/ItemParticle 6.0",
            "QtQuick.Particles/ItemParticle 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        attachedType: "QQuickItemParticleAttached"
        Property {
            name: "fade"
            type: "bool"
            read: "fade"
            write: "setFade"
            notify: "fadeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "fadeChanged" }
        Signal {
            name: "delegateChanged"
            Parameter { name: "arg"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "freeze"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "unfreeze"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "take"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "prioritize"; type: "bool" }
        }
        Method {
            name: "take"
            isCloned: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "give"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setFade"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setDelegate"
            Parameter { name: "arg"; type: "QQmlComponent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickitemparticle_p.h"
        name: "QQuickItemParticleAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "particle"
            type: "QQuickItemParticle"
            isPointer: true
            read: "particle"
            index: 0
            isReadonly: true
            isFinal: true
            isConstant: true
        }
        Signal { name: "detached" }
        Signal { name: "attached" }
    }
    Component {
        file: "private/qquicklineextruder_p.h"
        name: "QQuickLineExtruder"
        accessSemantics: "reference"
        prototype: "QQuickParticleExtruder"
        exports: [
            "QtQuick.Particles/LineShape 2.0",
            "QtQuick.Particles/LineShape 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "mirrored"
            type: "bool"
            read: "mirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 0
            isFinal: true
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickmaskextruder_p.h"
        name: "QQuickMaskExtruder"
        accessSemantics: "reference"
        prototype: "QQuickParticleExtruder"
        exports: [
            "QtQuick.Particles/MaskShape 2.0",
            "QtQuick.Particles/MaskShape 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
            isFinal: true
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method { name: "startMaskLoading" }
        Method { name: "finishMaskLoading" }
    }
    Component {
        file: "private/qquickparticleaffector_p.h"
        name: "QQuickParticleAffector"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/ParticleAffector 2.0",
            "QtQuick.Particles/ParticleAffector 2.1",
            "QtQuick.Particles/ParticleAffector 2.4",
            "QtQuick.Particles/ParticleAffector 2.7",
            "QtQuick.Particles/ParticleAffector 2.11",
            "QtQuick.Particles/ParticleAffector 6.0",
            "QtQuick.Particles/ParticleAffector 6.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "system"
            type: "QQuickParticleSystem"
            isPointer: true
            read: "system"
            write: "setSystem"
            notify: "systemChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "groups"
            type: "QStringList"
            read: "groups"
            write: "setGroups"
            notify: "groupsChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "whenCollidingWith"
            type: "QStringList"
            read: "whenCollidingWith"
            write: "setWhenCollidingWith"
            notify: "whenCollidingWithChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "once"
            type: "bool"
            read: "onceOff"
            write: "setOnceOff"
            notify: "onceChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "shape"
            type: "QQuickParticleExtruder"
            isPointer: true
            read: "shape"
            write: "setShape"
            notify: "shapeChanged"
            index: 5
            isFinal: true
        }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Signal {
            name: "groupsChanged"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "onceChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "shapeChanged"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Signal {
            name: "affected"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Signal {
            name: "whenCollidingWithChanged"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setGroups"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setOnceOff"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setShape"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method {
            name: "setWhenCollidingWith"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method { name: "updateOffsets" }
    }
    Component {
        file: "private/qquickparticleemitter_p.h"
        name: "QQuickParticleEmitter"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/Emitter 2.0",
            "QtQuick.Particles/Emitter 2.1",
            "QtQuick.Particles/Emitter 2.4",
            "QtQuick.Particles/Emitter 2.7",
            "QtQuick.Particles/Emitter 2.11",
            "QtQuick.Particles/Emitter 6.0",
            "QtQuick.Particles/Emitter 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Enum {
            name: "Lifetime"
            values: ["InfiniteLife"]
        }
        Property {
            name: "system"
            type: "QQuickParticleSystem"
            isPointer: true
            read: "system"
            write: "setSystem"
            notify: "systemChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "group"
            type: "QString"
            read: "group"
            write: "setGroup"
            notify: "groupChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "shape"
            type: "QQuickParticleExtruder"
            isPointer: true
            read: "extruder"
            write: "setExtruder"
            notify: "extruderChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "startTime"
            type: "int"
            read: "startTime"
            write: "setStartTime"
            notify: "startTimeChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "emitRate"
            type: "double"
            read: "particlesPerSecond"
            write: "setParticlesPerSecond"
            notify: "particlesPerSecondChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "lifeSpan"
            type: "int"
            read: "particleDuration"
            write: "setParticleDuration"
            notify: "particleDurationChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "lifeSpanVariation"
            type: "int"
            read: "particleDurationVariation"
            write: "setParticleDurationVariation"
            notify: "particleDurationVariationChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "maximumEmitted"
            type: "int"
            read: "maxParticleCount"
            write: "setMaxParticleCount"
            notify: "maximumEmittedChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "size"
            type: "double"
            read: "particleSize"
            write: "setParticleSize"
            notify: "particleSizeChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "endSize"
            type: "double"
            read: "particleEndSize"
            write: "setParticleEndSize"
            notify: "particleEndSizeChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "sizeVariation"
            type: "double"
            read: "particleSizeVariation"
            write: "setParticleSizeVariation"
            notify: "particleSizeVariationChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "velocity"
            type: "QQuickDirection"
            isPointer: true
            read: "velocity"
            write: "setVelocity"
            notify: "velocityChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "acceleration"
            type: "QQuickDirection"
            isPointer: true
            read: "acceleration"
            write: "setAcceleration"
            notify: "accelerationChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "velocityFromMovement"
            type: "double"
            read: "velocityFromMovement"
            write: "setVelocityFromMovement"
            notify: "velocityFromMovementChanged"
            index: 14
            isFinal: true
        }
        Signal {
            name: "emitParticles"
            Parameter { name: "particles"; type: "QJSValue" }
        }
        Signal {
            name: "particlesPerSecondChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "particleDurationChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "enabledChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Signal {
            name: "groupChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "particleDurationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "extruderChanged"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Signal {
            name: "particleSizeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "particleEndSizeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "particleSizeVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "velocityChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "maximumEmittedChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal { name: "particleCountChanged" }
        Signal { name: "velocityFromMovementChanged" }
        Signal {
            name: "startTimeChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "pulse"
            Parameter { name: "milliseconds"; type: "int" }
        }
        Method {
            name: "burst"
            Parameter { name: "num"; type: "int" }
        }
        Method {
            name: "burst"
            Parameter { name: "num"; type: "int" }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setParticlesPerSecond"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setParticleDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setGroup"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setParticleDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setExtruder"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method {
            name: "setParticleSize"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setParticleEndSize"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setParticleSizeVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setVelocity"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setMaxParticleCount"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setStartTime"
            Parameter { name: "arg"; type: "int" }
        }
        Method { name: "reset" }
    }
    Component {
        file: "private/qquickparticleextruder_p.h"
        name: "QQuickParticleExtruder"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Particles/ParticleExtruder 2.0",
            "QtQuick.Particles/ParticleExtruder 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickparticlegroup_p.h"
        name: "QQuickParticleGroup"
        accessSemantics: "reference"
        defaultProperty: "particleChildren"
        prototype: "QQuickStochasticState"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick.Particles/ParticleGroup 2.0",
            "QtQuick.Particles/ParticleGroup 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "system"
            type: "QQuickParticleSystem"
            isPointer: true
            read: "system"
            write: "setSystem"
            notify: "systemChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "particleChildren"
            type: "QObject"
            isList: true
            read: "particleChildren"
            index: 1
            isReadonly: true
        }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "delayRedirect"
            Parameter { name: "obj"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickparticlepainter_p.h"
        name: "QQuickParticlePainter"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/ParticlePainter 2.0",
            "QtQuick.Particles/ParticlePainter 2.1",
            "QtQuick.Particles/ParticlePainter 2.4",
            "QtQuick.Particles/ParticlePainter 2.7",
            "QtQuick.Particles/ParticlePainter 2.11",
            "QtQuick.Particles/ParticlePainter 6.0",
            "QtQuick.Particles/ParticlePainter 6.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "system"
            type: "QQuickParticleSystem"
            isPointer: true
            read: "system"
            write: "setSystem"
            notify: "systemChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "groups"
            type: "QStringList"
            read: "groups"
            write: "setGroups"
            notify: "groupsChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "countChanged" }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Signal {
            name: "groupsChanged"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setGroups"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "calcSystemOffset"
            Parameter { name: "resetPending"; type: "bool" }
        }
        Method { name: "calcSystemOffset"; isCloned: true }
        Method { name: "sceneGraphInvalidated" }
    }
    Component {
        file: "private/qquickparticlesystem_p.h"
        name: "QQuickParticleSystem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/ParticleSystem 2.0",
            "QtQuick.Particles/ParticleSystem 2.1",
            "QtQuick.Particles/ParticleSystem 2.4",
            "QtQuick.Particles/ParticleSystem 2.7",
            "QtQuick.Particles/ParticleSystem 2.11",
            "QtQuick.Particles/ParticleSystem 6.0",
            "QtQuick.Particles/ParticleSystem 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "paused"
            type: "bool"
            read: "isPaused"
            write: "setPaused"
            notify: "pausedChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "empty"
            type: "bool"
            read: "isEmpty"
            notify: "emptyChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "systemInitialized" }
        Signal {
            name: "runningChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "pausedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "emptyChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "restart" }
        Method { name: "pause" }
        Method { name: "resume" }
        Method { name: "reset" }
        Method {
            name: "setRunning"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setPaused"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "duration"; type: "int" }
        Method { name: "emittersChanged" }
        Method {
            name: "loadPainter"
            Parameter { name: "p"; type: "QQuickParticlePainter"; isPointer: true }
        }
        Method { name: "createEngine" }
        Method {
            name: "particleStateChange"
            Parameter { name: "idx"; type: "int" }
        }
    }
    Component {
        file: "private/qquickpointdirection_p.h"
        name: "QQuickPointDirection"
        accessSemantics: "reference"
        prototype: "QQuickDirection"
        exports: [
            "QtQuick.Particles/PointDirection 2.0",
            "QtQuick.Particles/PointDirection 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "x"
            type: "double"
            read: "x"
            write: "setX"
            notify: "xChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "y"
            type: "double"
            read: "y"
            write: "setY"
            notify: "yChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "xVariation"
            type: "double"
            read: "xVariation"
            write: "setXVariation"
            notify: "xVariationChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "yVariation"
            type: "double"
            read: "yVariation"
            write: "setYVariation"
            notify: "yVariationChanged"
            index: 3
            isFinal: true
        }
        Signal {
            name: "xChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "xVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "yVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setX"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setY"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setXVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setYVariation"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "private/qquickrectangleextruder_p.h"
        name: "QQuickRectangleExtruder"
        accessSemantics: "reference"
        prototype: "QQuickParticleExtruder"
        exports: [
            "QtQuick.Particles/RectangleShape 2.0",
            "QtQuick.Particles/RectangleShape 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "fill"
            type: "bool"
            read: "fill"
            write: "setFill"
            notify: "fillChanged"
            index: 0
            isFinal: true
        }
        Signal {
            name: "fillChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFill"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickspritegoal_p.h"
        name: "QQuickSpriteGoalAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/SpriteGoal 2.0",
            "QtQuick.Particles/SpriteGoal 2.1",
            "QtQuick.Particles/SpriteGoal 2.4",
            "QtQuick.Particles/SpriteGoal 2.7",
            "QtQuick.Particles/SpriteGoal 2.11",
            "QtQuick.Particles/SpriteGoal 6.0",
            "QtQuick.Particles/SpriteGoal 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "goalState"
            type: "QString"
            read: "goalState"
            write: "setGoalState"
            notify: "goalStateChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "jump"
            type: "bool"
            read: "jump"
            write: "setJump"
            notify: "jumpChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "systemStates"
            type: "bool"
            read: "systemStates"
            write: "setSystemStates"
            notify: "systemStatesChanged"
            index: 2
            isFinal: true
        }
        Signal {
            name: "goalStateChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "jumpChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "systemStatesChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setGoalState"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setJump"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setSystemStates"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickspriteengine_p.h"
        name: "QQuickStochasticState"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "durationVariation"
            type: "int"
            read: "durationVariation"
            write: "setDurationVariation"
            notify: "durationVariationChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "randomStart"
            type: "bool"
            read: "randomStart"
            write: "setRandomStart"
            notify: "randomStartChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "to"
            type: "QVariantMap"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 4
            isFinal: true
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "toChanged"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Signal {
            name: "durationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal { name: "entered" }
        Signal {
            name: "randomStartChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setName"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setTo"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Method {
            name: "setDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setRandomStart"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquicktargetdirection_p.h"
        name: "QQuickTargetDirection"
        accessSemantics: "reference"
        prototype: "QQuickDirection"
        exports: [
            "QtQuick.Particles/TargetDirection 2.0",
            "QtQuick.Particles/TargetDirection 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "targetX"
            type: "double"
            read: "targetX"
            write: "setTargetX"
            notify: "targetXChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "targetY"
            type: "double"
            read: "targetY"
            write: "setTargetY"
            notify: "targetYChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "targetItem"
            type: "QQuickItem"
            isPointer: true
            read: "targetItem"
            write: "setTargetItem"
            notify: "targetItemChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "targetVariation"
            type: "double"
            read: "targetVariation"
            write: "setTargetVariation"
            notify: "targetVariationChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "proportionalMagnitude"
            type: "bool"
            read: "proportionalMagnitude"
            write: "setProportionalMagnitude"
            notify: "proprotionalMagnitudeChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "magnitude"
            type: "double"
            read: "magnitude"
            write: "setMagnitude"
            notify: "magnitudeChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "magnitudeVariation"
            type: "double"
            read: "magnitudeVariation"
            write: "setMagnitudeVariation"
            notify: "magnitudeVariationChanged"
            index: 6
            isFinal: true
        }
        Signal {
            name: "targetXChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "targetYChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "targetVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "magnitudeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "proprotionalMagnitudeChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "magnitudeVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "targetItemChanged"
            Parameter { name: "arg"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setTargetX"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setTargetY"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setTargetVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setProportionalMagnitude"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setMagnitudeVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setTargetItem"
            Parameter { name: "arg"; type: "QQuickItem"; isPointer: true }
        }
    }
    Component {
        file: "private/qquicktrailemitter_p.h"
        name: "QQuickTrailEmitter"
        accessSemantics: "reference"
        prototype: "QQuickParticleEmitter"
        exports: [
            "QtQuick.Particles/TrailEmitter 2.0",
            "QtQuick.Particles/TrailEmitter 2.1",
            "QtQuick.Particles/TrailEmitter 2.4",
            "QtQuick.Particles/TrailEmitter 2.7",
            "QtQuick.Particles/TrailEmitter 2.11",
            "QtQuick.Particles/TrailEmitter 6.0",
            "QtQuick.Particles/TrailEmitter 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Enum {
            name: "EmitSize"
            values: ["ParticleSize"]
        }
        Property {
            name: "follow"
            type: "QString"
            read: "follow"
            write: "setFollow"
            notify: "followChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "emitRatePerParticle"
            type: "int"
            read: "particlesPerParticlePerSecond"
            write: "setParticlesPerParticlePerSecond"
            notify: "particlesPerParticlePerSecondChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "emitShape"
            type: "QQuickParticleExtruder"
            isPointer: true
            read: "emissonShape"
            write: "setEmissionShape"
            notify: "emissionShapeChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "emitHeight"
            type: "double"
            read: "emitterYVariation"
            write: "setEmitterYVariation"
            notify: "emitterYVariationChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "emitWidth"
            type: "double"
            read: "emitterXVariation"
            write: "setEmitterXVariation"
            notify: "emitterXVariationChanged"
            index: 4
            isFinal: true
        }
        Signal {
            name: "emitFollowParticles"
            Parameter { name: "particles"; type: "QJSValue" }
            Parameter { name: "followed"; type: "QJSValue" }
        }
        Signal {
            name: "particlesPerParticlePerSecondChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "emitterXVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "emitterYVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "followChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "emissionShapeChanged"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method {
            name: "setParticlesPerParticlePerSecond"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setEmitterXVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setEmitterYVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFollow"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setEmissionShape"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method { name: "recalcParticlesPerSecond" }
    }
    Component {
        file: "private/qquickturbulence_p.h"
        name: "QQuickTurbulenceAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Turbulence 2.0",
            "QtQuick.Particles/Turbulence 2.1",
            "QtQuick.Particles/Turbulence 2.4",
            "QtQuick.Particles/Turbulence 2.7",
            "QtQuick.Particles/Turbulence 2.11",
            "QtQuick.Particles/Turbulence 6.0",
            "QtQuick.Particles/Turbulence 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property {
            name: "strength"
            type: "double"
            read: "strength"
            write: "setStrength"
            notify: "strengthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "noiseSource"
            type: "QUrl"
            read: "noiseSource"
            write: "setNoiseSource"
            notify: "noiseSourceChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "strengthChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "noiseSourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setStrength"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setNoiseSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qquickwander_p.h"
        name: "QQuickWanderAffector"
        accessSemantics: "reference"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Wander 2.0",
            "QtQuick.Particles/Wander 2.1",
            "QtQuick.Particles/Wander 2.4",
            "QtQuick.Particles/Wander 2.7",
            "QtQuick.Particles/Wander 2.11",
            "QtQuick.Particles/Wander 6.0",
            "QtQuick.Particles/Wander 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Enum {
            name: "AffectableParameters"
            values: ["Position", "Velocity", "Acceleration"]
        }
        Property {
            name: "pace"
            type: "double"
            read: "pace"
            write: "setPace"
            notify: "paceChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "xVariance"
            type: "double"
            read: "xVariance"
            write: "setXVariance"
            notify: "xVarianceChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "yVariance"
            type: "double"
            read: "yVariance"
            write: "setYVariance"
            notify: "yVarianceChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "affectedParameter"
            type: "AffectableParameters"
            read: "affectedParameter"
            write: "setAffectedParameter"
            notify: "affectedParameterChanged"
            index: 3
            isFinal: true
        }
        Signal {
            name: "xVarianceChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "yVarianceChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "paceChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "affectedParameterChanged"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
        Method {
            name: "setXVariance"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setYVariance"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setPace"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAffectedParameter"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
    }
}
