<!--
// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<modify-function signature="^glClearBufferu?[fi]v\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
<modify-function signature="^glUniform\duiv\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
